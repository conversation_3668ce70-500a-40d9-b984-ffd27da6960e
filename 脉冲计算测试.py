#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
脉冲计算测试程序
用于验证长度到脉冲的转换计算
"""

import sys
import math
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

class PulseCalculatorTest(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("脉冲计算测试程序")
        self.setGeometry(100, 100, 800, 600)
        
        # 硬件参数
        self.wheel_diameter = 48.0  # mm
        self.pulses_per_revolution = 2000
        self.wheel_circumference = math.pi * self.wheel_diameter
        self.pulses_per_mm = self.pulses_per_revolution / self.wheel_circumference
        
        self.init_ui()
        
    def init_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title = QLabel("脉冲计算测试程序")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 18px; font-weight: bold; padding: 10px;")
        layout.addWidget(title)
        
        # 硬件参数显示
        params_group = QGroupBox("硬件参数")
        params_layout = QGridLayout()
        
        params_layout.addWidget(QLabel("滚轮直径:"), 0, 0)
        params_layout.addWidget(QLabel(f"{self.wheel_diameter} mm"), 0, 1)
        
        params_layout.addWidget(QLabel("每圈脉冲数:"), 1, 0)
        params_layout.addWidget(QLabel(f"{self.pulses_per_revolution} 脉冲"), 1, 1)
        
        params_layout.addWidget(QLabel("滚轮周长:"), 2, 0)
        params_layout.addWidget(QLabel(f"{self.wheel_circumference:.3f} mm"), 2, 1)
        
        params_layout.addWidget(QLabel("每毫米脉冲数:"), 3, 0)
        params_layout.addWidget(QLabel(f"{self.pulses_per_mm:.6f} 脉冲/mm"), 3, 1)
        
        params_group.setLayout(params_layout)
        layout.addWidget(params_group)
        
        # 计算区域
        calc_group = QGroupBox("长度转脉冲计算")
        calc_layout = QGridLayout()
        
        # 第一根长度
        calc_layout.addWidget(QLabel("第一根长度 (mm):"), 0, 0)
        self.first_length_spin = QDoubleSpinBox()
        self.first_length_spin.setRange(0, 99999.99)
        self.first_length_spin.setDecimals(2)
        self.first_length_spin.setValue(1000.00)
        self.first_length_spin.valueChanged.connect(self.calculate_pulses)
        calc_layout.addWidget(self.first_length_spin, 0, 1)
        
        calc_layout.addWidget(QLabel("第一根脉冲数:"), 0, 2)
        self.first_pulses_label = QLabel("0")
        self.first_pulses_label.setStyleSheet("font-weight: bold; color: #2ecc71;")
        calc_layout.addWidget(self.first_pulses_label, 0, 3)
        
        # 递增长度
        calc_layout.addWidget(QLabel("递增长度 (mm):"), 1, 0)
        self.increment_spin = QDoubleSpinBox()
        self.increment_spin.setRange(-100, 100)
        self.increment_spin.setDecimals(2)
        self.increment_spin.setValue(10.00)
        self.increment_spin.valueChanged.connect(self.calculate_pulses)
        calc_layout.addWidget(self.increment_spin, 1, 1)
        
        calc_layout.addWidget(QLabel("递增脉冲数:"), 1, 2)
        self.increment_pulses_label = QLabel("0")
        self.increment_pulses_label.setStyleSheet("font-weight: bold; color: #3498db;")
        calc_layout.addWidget(self.increment_pulses_label, 1, 3)
        
        calc_group.setLayout(calc_layout)
        layout.addWidget(calc_group)
        
        # 32位数据拆分显示
        split_group = QGroupBox("32位数据拆分")
        split_layout = QGridLayout()
        
        split_layout.addWidget(QLabel("第一根脉冲 - 高16位:"), 0, 0)
        self.first_high_label = QLabel("0")
        self.first_high_label.setStyleSheet("font-weight: bold; color: #e74c3c;")
        split_layout.addWidget(self.first_high_label, 0, 1)
        
        split_layout.addWidget(QLabel("第一根脉冲 - 低16位:"), 0, 2)
        self.first_low_label = QLabel("0")
        self.first_low_label.setStyleSheet("font-weight: bold; color: #e74c3c;")
        split_layout.addWidget(self.first_low_label, 0, 3)
        
        split_layout.addWidget(QLabel("递增脉冲 - 高16位:"), 1, 0)
        self.increment_high_label = QLabel("0")
        self.increment_high_label.setStyleSheet("font-weight: bold; color: #9b59b6;")
        split_layout.addWidget(self.increment_high_label, 1, 1)
        
        split_layout.addWidget(QLabel("递增脉冲 - 低16位:"), 1, 2)
        self.increment_low_label = QLabel("0")
        self.increment_low_label.setStyleSheet("font-weight: bold; color: #9b59b6;")
        split_layout.addWidget(self.increment_low_label, 1, 3)
        
        split_group.setLayout(split_layout)
        layout.addWidget(split_group)
        
        # 精度验证
        accuracy_group = QGroupBox("精度验证")
        accuracy_layout = QGridLayout()
        
        accuracy_layout.addWidget(QLabel("第一根实际长度:"), 0, 0)
        self.first_actual_label = QLabel("0.000 mm")
        self.first_actual_label.setStyleSheet("font-weight: bold; color: #27ae60;")
        accuracy_layout.addWidget(self.first_actual_label, 0, 1)
        
        accuracy_layout.addWidget(QLabel("第一根误差:"), 0, 2)
        self.first_error_label = QLabel("0.000 mm")
        self.first_error_label.setStyleSheet("font-weight: bold; color: #f39c12;")
        accuracy_layout.addWidget(self.first_error_label, 0, 3)
        
        accuracy_layout.addWidget(QLabel("递增实际长度:"), 1, 0)
        self.increment_actual_label = QLabel("0.000 mm")
        self.increment_actual_label.setStyleSheet("font-weight: bold; color: #27ae60;")
        accuracy_layout.addWidget(self.increment_actual_label, 1, 1)
        
        accuracy_layout.addWidget(QLabel("递增误差:"), 1, 2)
        self.increment_error_label = QLabel("0.000 mm")
        self.increment_error_label.setStyleSheet("font-weight: bold; color: #f39c12;")
        accuracy_layout.addWidget(self.increment_error_label, 1, 3)
        
        accuracy_group.setLayout(accuracy_layout)
        layout.addWidget(accuracy_group)
        
        # 多根长度预览
        preview_group = QGroupBox("多根长度预览")
        preview_layout = QVBoxLayout()
        
        # 根数设置
        roots_layout = QHBoxLayout()
        roots_layout.addWidget(QLabel("预览根数:"))
        self.roots_spin = QSpinBox()
        self.roots_spin.setRange(1, 20)
        self.roots_spin.setValue(5)
        self.roots_spin.valueChanged.connect(self.calculate_pulses)
        roots_layout.addWidget(self.roots_spin)
        roots_layout.addStretch()
        preview_layout.addLayout(roots_layout)
        
        # 预览表格
        self.preview_table = QTableWidget()
        self.preview_table.setColumnCount(4)
        self.preview_table.setHorizontalHeaderLabels(["根数", "长度(mm)", "脉冲数", "实际长度(mm)"])
        self.preview_table.setMaximumHeight(200)
        preview_layout.addWidget(self.preview_table)
        
        preview_group.setLayout(preview_layout)
        layout.addWidget(preview_group)
        
        # 初始计算
        self.calculate_pulses()
        
    def length_to_pulses(self, length_mm):
        """长度转换为脉冲数"""
        if length_mm <= 0:
            return 0
        return int(round(length_mm * self.pulses_per_mm))
    
    def pulses_to_length(self, pulses):
        """脉冲数转换为长度"""
        if pulses <= 0:
            return 0.0
        return pulses / self.pulses_per_mm
    
    def split_32bit_to_16bit(self, value_32bit):
        """将32位数值拆分为两个16位数值"""
        if value_32bit < 0 or value_32bit > 0xFFFFFFFF:
            return 0, 0
        
        high_16bit = (value_32bit >> 16) & 0xFFFF
        low_16bit = value_32bit & 0xFFFF
        
        return high_16bit, low_16bit
    
    def calculate_pulses(self):
        """计算脉冲数据"""
        try:
            # 获取输入值
            first_length = self.first_length_spin.value()
            increment = self.increment_spin.value()
            
            # 计算脉冲数
            first_pulses = self.length_to_pulses(first_length)
            increment_pulses = self.length_to_pulses(increment)
            
            # 更新脉冲数显示
            self.first_pulses_label.setText(str(first_pulses))
            self.increment_pulses_label.setText(str(increment_pulses))
            
            # 计算32位数据拆分
            first_high, first_low = self.split_32bit_to_16bit(first_pulses)
            increment_high, increment_low = self.split_32bit_to_16bit(increment_pulses)
            
            self.first_high_label.setText(str(first_high))
            self.first_low_label.setText(str(first_low))
            self.increment_high_label.setText(str(increment_high))
            self.increment_low_label.setText(str(increment_low))
            
            # 计算实际长度和误差
            first_actual = self.pulses_to_length(first_pulses)
            increment_actual = self.pulses_to_length(increment_pulses)
            
            first_error = abs(first_actual - first_length)
            increment_error = abs(increment_actual - increment)
            
            self.first_actual_label.setText(f"{first_actual:.3f} mm")
            self.first_error_label.setText(f"{first_error:.3f} mm")
            self.increment_actual_label.setText(f"{increment_actual:.3f} mm")
            self.increment_error_label.setText(f"{increment_error:.3f} mm")
            
            # 更新多根长度预览
            self.update_preview_table(first_length, increment, first_pulses, increment_pulses)
            
        except Exception as e:
            print(f"计算脉冲数据时发生错误: {e}")
    
    def update_preview_table(self, first_length, increment, first_pulses, increment_pulses):
        """更新多根长度预览表格"""
        try:
            roots_count = self.roots_spin.value()
            self.preview_table.setRowCount(roots_count)
            
            for i in range(roots_count):
                # 计算每根的长度和脉冲数
                root_length = first_length + increment * i
                root_pulses = first_pulses + increment_pulses * i
                root_actual = self.pulses_to_length(root_pulses)
                
                # 填充表格
                self.preview_table.setItem(i, 0, QTableWidgetItem(f"第{i+1}根"))
                self.preview_table.setItem(i, 1, QTableWidgetItem(f"{root_length:.2f}"))
                self.preview_table.setItem(i, 2, QTableWidgetItem(str(root_pulses)))
                self.preview_table.setItem(i, 3, QTableWidgetItem(f"{root_actual:.3f}"))
            
            # 调整列宽
            self.preview_table.resizeColumnsToContents()
            
        except Exception as e:
            print(f"更新预览表格时发生错误: {e}")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # 设置应用程序样式
    app.setStyle(QStyleFactory.create('Fusion'))
    
    window = PulseCalculatorTest()
    window.show()
    
    print("脉冲计算测试程序已启动")
    print("硬件参数:")
    print(f"  滚轮直径: {window.wheel_diameter}mm")
    print(f"  每圈脉冲数: {window.pulses_per_revolution}")
    print(f"  滚轮周长: {window.wheel_circumference:.3f}mm")
    print(f"  每毫米脉冲数: {window.pulses_per_mm:.6f}")
    
    sys.exit(app.exec_())

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资源管理和内存泄漏修复测试程序
"""

import sys
import time
import psutil
import os
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

class ResourceMonitor(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("剪切机自动化系统 - 资源管理测试")
        self.setGeometry(100, 100, 900, 700)
        
        # 获取当前进程
        self.process = psutil.Process(os.getpid())
        self.initial_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        
        self.init_ui()
        
        # 监控定时器
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self.update_resource_info)
        self.monitor_timer.start(1000)  # 每秒更新
        
        # 测试计数器
        self.test_count = 0
        self.max_test_count = 500  # 减少测试次数，更容易看到进度
        
    def init_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title = QLabel("资源管理和内存泄漏修复测试")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 18px; font-weight: bold; padding: 10px;")
        layout.addWidget(title)
        
        # 资源信息显示
        info_group = QGroupBox("系统资源信息")
        info_layout = QGridLayout()
        
        # 内存使用
        info_layout.addWidget(QLabel("内存使用:"), 0, 0)
        self.memory_label = QLabel("0 MB")
        self.memory_label.setStyleSheet("font-weight: bold; color: #2ecc71;")
        info_layout.addWidget(self.memory_label, 0, 1)
        
        # 内存增长
        info_layout.addWidget(QLabel("内存增长:"), 1, 0)
        self.memory_growth_label = QLabel("0 MB")
        self.memory_growth_label.setStyleSheet("font-weight: bold; color: #e74c3c;")
        info_layout.addWidget(self.memory_growth_label, 1, 1)
        
        # CPU使用率
        info_layout.addWidget(QLabel("CPU使用率:"), 2, 0)
        self.cpu_label = QLabel("0%")
        self.cpu_label.setStyleSheet("font-weight: bold; color: #3498db;")
        info_layout.addWidget(self.cpu_label, 2, 1)
        
        # 线程数
        info_layout.addWidget(QLabel("线程数:"), 3, 0)
        self.thread_label = QLabel("0")
        self.thread_label.setStyleSheet("font-weight: bold; color: #f39c12;")
        info_layout.addWidget(self.thread_label, 3, 1)
        
        # 文件句柄数
        info_layout.addWidget(QLabel("文件句柄:"), 4, 0)
        self.handle_label = QLabel("0")
        self.handle_label.setStyleSheet("font-weight: bold; color: #9b59b6;")
        info_layout.addWidget(self.handle_label, 4, 1)
        
        info_group.setLayout(info_layout)
        layout.addWidget(info_group)
        
        # 测试控制
        control_group = QGroupBox("测试控制")
        control_layout = QHBoxLayout()
        
        # 开始内存泄漏测试
        self.start_test_btn = QPushButton("开始内存泄漏测试")
        self.start_test_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        self.start_test_btn.clicked.connect(self.start_memory_leak_test)
        control_layout.addWidget(self.start_test_btn)
        
        # 停止测试
        self.stop_test_btn = QPushButton("停止测试")
        self.stop_test_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        self.stop_test_btn.clicked.connect(self.stop_test)
        self.stop_test_btn.setEnabled(False)
        control_layout.addWidget(self.stop_test_btn)
        
        # 强制垃圾回收
        gc_btn = QPushButton("强制垃圾回收")
        gc_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        gc_btn.clicked.connect(self.force_garbage_collection)
        control_layout.addWidget(gc_btn)
        
        control_group.setLayout(control_layout)
        layout.addWidget(control_group)
        
        # 测试状态
        status_group = QGroupBox("测试状态")
        status_layout = QVBoxLayout()
        
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("font-size: 14px; padding: 10px;")
        status_layout.addWidget(self.status_label)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        status_layout.addWidget(self.progress_bar)
        
        status_group.setLayout(status_layout)
        layout.addWidget(status_group)
        
        # 日志显示
        log_group = QGroupBox("测试日志")
        log_layout = QVBoxLayout()
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        self.log_text.setStyleSheet("""
            QTextEdit {
                border: 1px solid #ddd;
                border-radius: 5px;
                padding: 5px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 10px;
                background-color: #f8f9fa;
            }
        """)
        log_layout.addWidget(self.log_text)
        
        log_group.setLayout(log_layout)
        layout.addWidget(log_group)
        
        # 测试定时器
        self.test_timer = QTimer()
        self.test_timer.timeout.connect(self.simulate_memory_operations)
        
    def add_log(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        self.log_text.append(log_message)
        
        # 自动滚动到底部
        cursor = self.log_text.textCursor()
        cursor.movePosition(cursor.End)
        self.log_text.setTextCursor(cursor)
    
    def update_resource_info(self):
        """更新资源信息显示"""
        try:
            # 内存使用
            memory_info = self.process.memory_info()
            current_memory = memory_info.rss / 1024 / 1024  # MB
            memory_growth = current_memory - self.initial_memory
            
            self.memory_label.setText(f"{current_memory:.1f} MB")
            self.memory_growth_label.setText(f"{memory_growth:+.1f} MB")
            
            # 根据内存增长设置颜色
            if memory_growth > 50:  # 超过50MB增长显示红色
                self.memory_growth_label.setStyleSheet("font-weight: bold; color: #e74c3c;")
            elif memory_growth > 20:  # 超过20MB增长显示橙色
                self.memory_growth_label.setStyleSheet("font-weight: bold; color: #f39c12;")
            else:
                self.memory_growth_label.setStyleSheet("font-weight: bold; color: #27ae60;")
            
            # CPU使用率
            cpu_percent = self.process.cpu_percent()
            self.cpu_label.setText(f"{cpu_percent:.1f}%")
            
            # 线程数
            thread_count = self.process.num_threads()
            self.thread_label.setText(str(thread_count))
            
            # 文件句柄数（Windows上可能不支持）
            try:
                handle_count = len(self.process.open_files())
                self.handle_label.setText(str(handle_count))
            except:
                self.handle_label.setText("N/A")
                
        except Exception as e:
            self.add_log(f"更新资源信息时发生错误: {e}")
    
    def start_memory_leak_test(self):
        """开始内存泄漏测试"""
        self.add_log(f"开始内存泄漏测试，总共将进行 {self.max_test_count} 次操作...")
        self.test_count = 0
        self.start_test_btn.setEnabled(False)
        self.stop_test_btn.setEnabled(True)
        self.progress_bar.setVisible(True)
        self.progress_bar.setMaximum(self.max_test_count)
        self.progress_bar.setValue(0)
        self.status_label.setText("正在进行内存泄漏测试... (0%)")

        # 启动测试定时器
        self.test_timer.start(50)  # 每50ms执行一次，更容易观察进度

        # 立即执行一次测试，确保测试开始
        self.simulate_memory_operations()
    
    def simulate_memory_operations(self):
        """模拟可能导致内存泄漏的操作"""
        try:
            # 模拟创建大量数据（类似于通信数据累积）
            data_list = []
            current_time = time.time()
            time_str = time.strftime('%H:%M:%S', time.localtime(current_time))
            microseconds = int((current_time % 1) * 1000000)
            formatted_time = f"{time_str}.{microseconds:06d}"

            for i in range(100):
                data_list.append({
                    'time': formatted_time,
                    'data': f"test_data_{self.test_count}_{i}" * 10,
                    'type': 'test_operation'
                })

            # 模拟数据清理（测试我们的修复）
            if len(data_list) > 50:
                data_list = data_list[-50:]  # 只保留最新的50条

            # 模拟定时器创建和销毁
            temp_timer = QTimer()
            temp_timer.timeout.connect(lambda: None)
            temp_timer.start(1000)
            temp_timer.stop()
            temp_timer.deleteLater()

            # 更新计数器和进度条
            self.test_count += 1
            self.progress_bar.setValue(self.test_count)

            # 每50次测试输出一次日志，让用户看到进度
            if self.test_count % 50 == 0:
                self.add_log(f"已完成 {self.test_count}/{self.max_test_count} 次内存操作测试")

            # 更新状态标签显示当前进度
            progress_percent = int((self.test_count / self.max_test_count) * 100)
            self.status_label.setText(f"正在进行内存泄漏测试... ({progress_percent}%)")

            # 检查是否完成测试
            if self.test_count >= self.max_test_count:
                self.stop_test()
                self.add_log("内存泄漏测试完成")

        except Exception as e:
            self.add_log(f"模拟内存操作时发生错误: {e}")
            print(f"内存操作测试错误: {e}")  # 同时输出到控制台便于调试
    
    def stop_test(self):
        """停止测试"""
        self.test_timer.stop()
        self.start_test_btn.setEnabled(True)
        self.stop_test_btn.setEnabled(False)
        self.progress_bar.setVisible(False)
        self.status_label.setText("测试已停止")
        self.add_log("测试已停止")
    
    def force_garbage_collection(self):
        """强制垃圾回收"""
        import gc
        before_count = len(gc.get_objects())
        collected = gc.collect()
        after_count = len(gc.get_objects())
        
        self.add_log(f"垃圾回收完成: 释放了{collected}个对象")
        self.add_log(f"对象数量: {before_count} -> {after_count}")
    
    def closeEvent(self, event):
        """关闭事件"""
        self.monitor_timer.stop()
        self.test_timer.stop()
        self.add_log("资源监控程序已关闭")
        event.accept()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # 设置应用程序样式
    app.setStyle(QStyleFactory.create('Fusion'))
    
    window = ResourceMonitor()
    window.show()
    
    sys.exit(app.exec_())

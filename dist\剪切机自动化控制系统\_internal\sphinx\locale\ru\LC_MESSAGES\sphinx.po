# Translations template for Sphinx.
# Copyright (C) 2025 ORGANIZATION
# This file is distributed under the same license as the Sphinx project.
# 
# Translators:
# <PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2013
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2014,2016,2019
# <AUTHOR> <EMAIL>, 2013
# <AUTHOR> <EMAIL>, 2022
# <PERSON> <<EMAIL>>, 2016
# <AUTHOR> <EMAIL>, 2015,2017
# Евгений <PERSON> <<EMAIL>>, 2024
# Евгений <PERSON> <<EMAIL>>, 2024
msgid ""
msgstr ""
"Project-Id-Version: Sphinx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-02-18 00:33+0000\n"
"PO-Revision-Date: 2013-04-02 08:44+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Russian (http://app.transifex.com/sphinx-doc/sphinx-1/language/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#: extension.py:58
#, python-format
msgid ""
"The %s extension is required by needs_extensions settings, but it is not "
"loaded."
msgstr "Расширение %s требуется для настройки needs_extensions, но оно не загружено."

#: extension.py:79
#, python-format
msgid ""
"This project needs the extension %s at least in version %s and therefore "
"cannot be built with the loaded version (%s)."
msgstr "Проекту требуется расширение %s версии не ниже %s, и, следовательно, он не может быть собран с загруженной версией (%s)."

#: application.py:212
#, python-format
msgid "Cannot find source directory (%s)"
msgstr "Не удалось найти исходный каталог (%s)"

#: application.py:217
#, python-format
msgid "Output directory (%s) is not a directory"
msgstr "Целевой путь (%s) не является каталогом"

#: application.py:222
msgid "Source directory and destination directory cannot be identical"
msgstr "Исходный и целевой каталоги не должны совпадать"

#: application.py:252
#, python-format
msgid "Running Sphinx v%s"
msgstr "Запуск Sphinx v%s"

#: application.py:278
#, python-format
msgid ""
"This project needs at least Sphinx v%s and therefore cannot be built with "
"this version."
msgstr "Проект требует версию Sphinx не ниже v%s и не может быть построен текущей версией."

#: application.py:297
msgid "making output directory"
msgstr "создание целевого каталога"

#: application.py:302 registry.py:538
#, python-format
msgid "while setting up extension %s:"
msgstr "при настройку расширения %s:"

#: application.py:309
msgid ""
"'setup' as currently defined in conf.py isn't a Python callable. Please "
"modify its definition to make it a callable function. This is needed for "
"conf.py to behave as a Sphinx extension."
msgstr ""

#: application.py:346
#, python-format
msgid "loading translations [%s]... "
msgstr "загрузка переводов [%s]... "

#: application.py:370 util/display.py:89
msgid "done"
msgstr "готово"

#: application.py:372
msgid "not available for built-in messages"
msgstr "недоступно для встроенных сообщений"

#: application.py:386
msgid "loading pickled environment"
msgstr "загрузка фиксированного окружения"

#: application.py:394
#, python-format
msgid "failed: %s"
msgstr "ошибка: %s"

#: application.py:407
msgid "No builder selected, using default: html"
msgstr "Сборщик не указан, по умолчанию используется html"

#: application.py:439
msgid "build finished with problems."
msgstr "сборка завершена с проблемами."

#: application.py:441
msgid "build succeeded."
msgstr "сборка успешна."

#: application.py:446
msgid ""
"build finished with problems, 1 warning (with warnings treated as errors)."
msgstr "сборка завершена с проблемами, 1 предупреждение (с предупреждениями, рассматриваемыми как ошибки)."

#: application.py:450
msgid "build finished with problems, 1 warning."
msgstr "сборка завершена с проблемами, 1 предупреждение."

#: application.py:452
msgid "build succeeded, 1 warning."
msgstr "сборка успешна, 1 предупреждение."

#: application.py:458
#, python-format
msgid ""
"build finished with problems, %s warnings (with warnings treated as errors)."
msgstr "сборка завершена с проблемами, %s предупреждений (в том числе с предупреждениями, рассматриваемыми как ошибки)."

#: application.py:462
#, python-format
msgid "build finished with problems, %s warnings."
msgstr "сборка завершена с проблемами, %s предупреждений."

#: application.py:464
#, python-format
msgid "build succeeded, %s warnings."
msgstr "сборка успешна, %s предупреждение(-ий)."

#: application.py:1026
#, python-format
msgid "node class %r is already registered, its visitors will be overridden"
msgstr "узел класса %r уже зарегистрирован, его посетители будут переопределены"

#: application.py:1119
#, python-format
msgid "directive %r is already registered and will not be overridden"
msgstr ""

#: application.py:1145 application.py:1173
#, python-format
msgid "role %r is already registered and will not be overridden"
msgstr ""

#: application.py:1770
#, python-format
msgid ""
"the %s extension does not declare if it is safe for parallel reading, "
"assuming it isn't - please ask the extension author to check and make it "
"explicit"
msgstr "расширение %s не определено как безопасное для параллельного чтения; если вы полагаете что это не так - пожалуйста, попросите автора проверить и явно указать это"

#: application.py:1775
#, python-format
msgid "the %s extension is not safe for parallel reading"
msgstr "расширение %s не безопасно для параллельного чтения"

#: application.py:1779
#, python-format
msgid ""
"the %s extension does not declare if it is safe for parallel writing, "
"assuming it isn't - please ask the extension author to check and make it "
"explicit"
msgstr "расширение %s не объявлено как безопасное для параллельное записи; если вы считаете что это не так - пожалуйста, попросите автора проверить и явно указать это"

#: application.py:1784
#, python-format
msgid "the %s extension is not safe for parallel writing"
msgstr "расширение %s не безопасно использует параллельную записи"

#: application.py:1792 application.py:1796
#, python-format
msgid "doing serial %s"
msgstr ""

#: config.py:355
#, python-format
msgid "config directory doesn't contain a conf.py file (%s)"
msgstr "в конфигурационной папке нет файла conf.py file (%s)"

#: config.py:366
msgid ""
"Invalid configuration value found: 'language = None'. Update your "
"configuration to a valid language code. Falling back to 'en' (English)."
msgstr "Обнаружено некорректное значение настройки: 'language = None'. Обновите настройки, указав корректный код языка. Откат к 'en' (English)."

#: config.py:394
#, python-format
msgid "'%s' must be '0' or '1', got '%s'"
msgstr ""

#: config.py:399
#, python-format
msgid ""
"cannot override dictionary config setting %r, ignoring (use %r to set "
"individual elements)"
msgstr "не удалось переопределить настройку словаря %r, пропускается (используйте %r для установки значений отдельных элементов)"

#: config.py:411
#, python-format
msgid "invalid number %r for config value %r, ignoring"
msgstr "недопустимое число %r для настройки %r, пропускается"

#: config.py:419
#, python-format
msgid "cannot override config setting %r with unsupported type, ignoring"
msgstr "не удалось переопределить настройку %r с неподдерживаемым типом, пропускается"

#: config.py:442
#, python-format
msgid "unknown config value %r in override, ignoring"
msgstr "неизвестное значение параметра конфигурации %r в переопределении, пропускается"

#: config.py:496
#, python-format
msgid "No such config value: %r"
msgstr "Нет такого значения конфигурации: %r"

#: config.py:524
#, python-format
msgid "Config value %r already present"
msgstr "Ключ конфигурации %r уже существует"

#: config.py:561
#, python-format
msgid ""
"cannot cache unpickleable configuration value: %r (because it contains a "
"function, class, or module object)"
msgstr ""

#: config.py:603
#, python-format
msgid "There is a syntax error in your configuration file: %s\n"
msgstr "Синтаксическая ошибка в вашем файле конфигурации: %s\n"

#: config.py:607
msgid ""
"The configuration file (or one of the modules it imports) called sys.exit()"
msgstr "Файл конфигурации (или один из импортированных модулей) вызвал sys.exit()"

#: config.py:615
#, python-format
msgid ""
"There is a programmable error in your configuration file:\n"
"\n"
"%s"
msgstr "В вашем файле конфигурации программная ошибка:\n\n%s"

#: config.py:637
#, python-format
msgid "Failed to convert %r to a frozenset"
msgstr ""

#: config.py:655 config.py:663
#, python-format
msgid "Converting `source_suffix = %r` to `source_suffix = %r`."
msgstr "Преобразование `source_suffix = %r` в `source_suffix = %r`."

#: config.py:669
#, python-format
msgid ""
"The config value `source_suffix' expects a dictionary, a string, or a list "
"of strings. Got `%r' instead (type %s)."
msgstr "Значением настройки `source_suffix' может быть словарь, строка или список строк. Вместо этого указано значение `%r' (тип %s)."

#: config.py:690
#, python-format
msgid "Section %s"
msgstr "Раздел %s"

#: config.py:691
#, python-format
msgid "Fig. %s"
msgstr "Рис. %s"

#: config.py:692
#, python-format
msgid "Table %s"
msgstr "Таблица %s"

#: config.py:693
#, python-format
msgid "Listing %s"
msgstr "Список %s"

#: config.py:802
#, python-brace-format
msgid ""
"The config value `{name}` has to be a one of {candidates}, but `{current}` "
"is given."
msgstr "Для настройки `{name}` возможны значения {candidates}, но указано `{current}`."

#: config.py:833
#, python-brace-format
msgid ""
"The config value `{name}' has type `{current.__name__}'; expected "
"{permitted}."
msgstr "Значение настройки `{name}' имеет тип `{current.__name__}'; ожидается {permitted}."

#: config.py:850
#, python-brace-format
msgid ""
"The config value `{name}' has type `{current.__name__}', defaults to "
"`{default.__name__}'."
msgstr "Значение настройки `{name}' имеет тип `{current.__name__}', приведено к значению по умолчанию `{default.__name__}'."

#: config.py:862
#, python-format
msgid "primary_domain %r not found, ignored."
msgstr "primary_domain %r не найден, пропускается."

#: config.py:882
msgid ""
"Sphinx now uses \"index\" as the master document by default. To keep pre-2.0"
" behaviour, set \"master_doc = 'contents'\"."
msgstr ""

#: highlighting.py:170
#, python-format
msgid "Pygments lexer name %r is not known"
msgstr "Неизвестный лексер Pygments %r"

#: highlighting.py:209
#, python-format
msgid ""
"Lexing literal_block %r as \"%s\" resulted in an error at token: %r. "
"Retrying in relaxed mode."
msgstr "Разбор literal_block %r как \"%s\" завершилось ошибкой на токене: %r. Перезапуск в relaxed mode."

#: theming.py:115
#, python-format
msgid ""
"Theme configuration sections other than [theme] and [options] are not "
"supported (tried to get a value from %r)."
msgstr "Разделы конфигурации темы, отличные от [theme] и [option], не поддерживаются (попытка получения значения из %r)."

#: theming.py:120
#, python-format
msgid "setting %s.%s occurs in none of the searched theme configs"
msgstr "параметр %s.%s не встречается ни в одной из найденных настроек тем"

#: theming.py:135
#, python-format
msgid "unsupported theme option %r given"
msgstr "указана неподдерживаемая опция темы %r"

#: theming.py:208
#, python-format
msgid "file %r on theme path is not a valid zipfile or contains no theme"
msgstr "файл %r указанной темы не является корректным ZIP-архивов или не содержит тему"

#: theming.py:228
#, python-format
msgid "no theme named %r found (missing theme.toml?)"
msgstr "не найдена тема %r (потерян theme.toml?)"

#: theming.py:268
#, python-format
msgid "The %r theme has circular inheritance"
msgstr "Тема %r имеет циклическое наследование"

#: theming.py:276
#, python-format
msgid ""
"The %r theme inherits from %r, which is not a loaded theme. Loaded themes "
"are: %s"
msgstr "Тема %r наследуется от темы %r, которая не загружена. Загруженные темы: %s"

#: theming.py:282
#, python-format
msgid "The %r theme has too many ancestors"
msgstr "У темы %r слишком много предков"

#: theming.py:310
#, python-format
msgid "no theme configuration file found in %r"
msgstr "файл конфигурации темы не найден в %r"

#: theming.py:335 theming.py:388
#, python-format
msgid "theme %r doesn't have the \"theme\" table"
msgstr "тема %r не имеет таблицы \"theme\""

#: theming.py:339
#, python-format
msgid "The %r theme \"[theme]\" table is not a table"
msgstr "В теме %r \"[theme]\" не является таблицей"

#: theming.py:343 theming.py:391
#, python-format
msgid "The %r theme must define the \"theme.inherit\" setting"
msgstr "В теме %r должна быть определена настройка  \"theme.inherit\""

#: theming.py:347
#, python-format
msgid "The %r theme \"[options]\" table is not a table"
msgstr "В теме %r \"[options]\" не является таблицей"

#: theming.py:366
#, python-format
msgid "The \"theme.pygments_style\" setting must be a table. Hint: \"%s\""
msgstr "Настройка \"theme.pygments_style\" должна быть таблицей. Подсказка: \"%s\""

#: events.py:77
#, python-format
msgid "Event %r already present"
msgstr "Событие %r уже существует"

#: events.py:370
#, python-format
msgid "Unknown event name: %s"
msgstr "Неизвестное событие: %s"

#: events.py:416
#, python-format
msgid "Handler %r for event %r threw an exception"
msgstr "Обработчик %r для события %r возбудил исключение"

#: project.py:72
#, python-format
msgid ""
"multiple files found for the document \"%s\": %s\n"
"Use %r for the build."
msgstr "найдено несколько файлов для документа \"%s\": %s\nДля сборки используется %r ."

#: project.py:87
#, python-format
msgid "Ignored unreadable document %r."
msgstr "Пропущен нечитаемый документ %r."

#: registry.py:167
#, python-format
msgid "Builder class %s has no \"name\" attribute"
msgstr "Отсутствует аттрибут \"name\" у класса сборщика %s."

#: registry.py:171
#, python-format
msgid "Builder %r already exists (in module %s)"
msgstr "Сборщик %r уже существует (в модуле %s)."

#: registry.py:187
#, python-format
msgid "Builder name %s not registered or available through entry point"
msgstr "Сборщик %s не зарегистрирован явно или через ресурсы пакетов."

#: registry.py:197
#, python-format
msgid "Builder name %s not registered"
msgstr "Сборщик %s не зарегистрирован."

#: registry.py:204
#, python-format
msgid "domain %s already registered"
msgstr "домен %s уже зарегистрирован"

#: registry.py:228 registry.py:249 registry.py:262
#, python-format
msgid "domain %s not yet registered"
msgstr "домен %s не зарегистрирован"

#: registry.py:235
#, python-format
msgid "The %r directive is already registered to domain %s"
msgstr "Директива %r уже зарегистрирована в домене %s"

#: registry.py:253
#, python-format
msgid "The %r role is already registered to domain %s"
msgstr "Роль %r уже зарегистрирована в домене %s"

#: registry.py:266
#, python-format
msgid "The %r index is already registered to domain %s"
msgstr "Индекс %r уже зарегистрирован в домене %s"

#: registry.py:313
#, python-format
msgid "The %r object_type is already registered"
msgstr "object_type %r уже зарегистрирован"

#: registry.py:344
#, python-format
msgid "The %r crossref_type is already registered"
msgstr "%r crossref_type уже зарегистрирован"

#: registry.py:353
#, python-format
msgid "source_suffix %r is already registered"
msgstr "source_suffix %r уже зарегистрирован"

#: registry.py:363
#, python-format
msgid "source_parser for %r is already registered"
msgstr "source_parser для %r уже зарегистрирован"

#: registry.py:372
#, python-format
msgid "Source parser for %s not registered"
msgstr "Парсер исходного кода %s не зарегистрирован"

#: registry.py:390
#, python-format
msgid "Translator for %r already exists"
msgstr "Перевод для %r уже существует"

#: registry.py:407
#, python-format
msgid "kwargs for add_node() must be a (visit, depart) function tuple: %r=%r"
msgstr "kwargs для функции add_node() должны быть кортежем (visit, depart): %r=%r"

#: registry.py:496
#, python-format
msgid "enumerable_node %r already registered"
msgstr "enumerable_node %r уже зарегистрирован"

#: registry.py:512
#, python-format
msgid "math renderer %s is already registered"
msgstr "Движок отрисовки math %s уже зарегистрирован"

#: registry.py:529
#, python-format
msgid ""
"the extension %r was already merged with Sphinx since version %s; this "
"extension is ignored."
msgstr "расширение %r является частью Sphinx начиная с версии %s; это расширение игнорируется."

#: registry.py:543
msgid "Original exception:\n"
msgstr "Изначальное исключение:\n"

#: registry.py:545
#, python-format
msgid "Could not import extension %s"
msgstr "Не удалось загрузить расширение %s"

#: registry.py:552
#, python-format
msgid ""
"extension %r has no setup() function; is it really a Sphinx extension "
"module?"
msgstr "расширение %r не определяет функцию setup(); это действительно модуль расширения Sphinx?"

#: registry.py:565
#, python-format
msgid ""
"The %s extension used by this project needs at least Sphinx v%s; it "
"therefore cannot be built with this version."
msgstr "Расширению %s, используемому в этом проекте, требуется версия Sphinx не ниже v%s, поэтому он не может быть собран с этой версией."

#: registry.py:577
#, python-format
msgid ""
"extension %r returned an unsupported object from its setup() function; it "
"should return None or a metadata dictionary"
msgstr "расширение %r вернуло неподдерживаемый объект из функции setup() function; оно должно вернуть None или словарь метаданных"

#: registry.py:612
#, python-format
msgid "`None` is not a valid filetype for %r."
msgstr "`None` не является корректным типом файла для %r."

#: roles.py:206
#, python-format
msgid "Common Vulnerabilities and Exposures; CVE %s"
msgstr "Common Vulnerabilities and Exposures; CVE %s"

#: roles.py:229
#, python-format
msgid "invalid CVE number %s"
msgstr "неправильный номер CVE %s"

#: roles.py:251
#, python-format
msgid "Common Weakness Enumeration; CWE %s"
msgstr "Common Weakness Enumeration; CWE %s"

#: roles.py:274
#, python-format
msgid "invalid CWE number %s"
msgstr "неправильный номер CWE %s"

#: roles.py:294
#, python-format
msgid "Python Enhancement Proposals; PEP %s"
msgstr "Предложения об улучшениях Python; PEP %s"

#: roles.py:317
#, python-format
msgid "invalid PEP number %s"
msgstr "неправильный номер PEP %s"

#: roles.py:355
#, python-format
msgid "invalid RFC number %s"
msgstr "неправильный номер RFC %s"

#: ext/linkcode.py:86 ext/viewcode.py:226
msgid "[source]"
msgstr "[исходный код]"

#: ext/viewcode.py:289
msgid "highlighting module code... "
msgstr "подсветка кода модуля... "

#: ext/viewcode.py:320
msgid "[docs]"
msgstr "[документация]"

#: ext/viewcode.py:346
msgid "Module code"
msgstr "Код модуля"

#: ext/viewcode.py:353
#, python-format
msgid "<h1>Source code for %s</h1>"
msgstr "<h1>Исходный код %s</h1>"

#: ext/viewcode.py:380
msgid "Overview: module code"
msgstr "Обзор: исходный код модуля"

#: ext/viewcode.py:381
msgid "<h1>All modules for which code is available</h1>"
msgstr "<h1>Все модули, в которых есть код</h1>"

#: ext/extlinks.py:82
#, python-format
msgid ""
"hardcoded link %r could be replaced by an extlink (try using %r instead)"
msgstr "прямая ссылка %r должна быть заменена на extlink (попробуйте использовать %r)"

#: ext/autosectionlabel.py:52
#, python-format
msgid "section \"%s\" gets labeled as \"%s\""
msgstr "секция \"%s\" отмечена как \"%s\""

#: domains/std/__init__.py:833 domains/std/__init__.py:960
#: ext/autosectionlabel.py:61
#, python-format
msgid "duplicate label %s, other instance in %s"
msgstr "повтоярющаяся метка %s, другой экземпляр в %s"

#: ext/imgmath.py:387 ext/mathjax.py:60
msgid "Link to this equation"
msgstr "Ссылка на эту формулу"

#: ext/duration.py:90
msgid ""
"====================== slowest reading durations ======================="
msgstr ""

#: ext/doctest.py:118
#, python-format
msgid "missing '+' or '-' in '%s' option."
msgstr "не указан '+' или '-' в опции '%s'."

#: ext/doctest.py:124
#, python-format
msgid "'%s' is not a valid option."
msgstr "'%s' — некорректная опция."

#: ext/doctest.py:139
#, python-format
msgid "'%s' is not a valid pyversion option"
msgstr "'%s' некорректная опция pyversion"

#: ext/doctest.py:226
msgid "invalid TestCode type"
msgstr "неправильный тип TestCode"

#: ext/doctest.py:297
#, python-format
msgid ""
"Testing of doctests in the sources finished, look at the results in "
"%(outdir)s/output.txt."
msgstr "Тестирование doctests в исходном каталоге завершено, посмотрите результаты в %(outdir)s/output.txt."

#: ext/doctest.py:457
#, python-format
msgid "no code/output in %s block at %s:%s"
msgstr ""

#: ext/doctest.py:568
#, python-format
msgid "ignoring invalid doctest code: %r"
msgstr "пропускается некорректный код doctest: %r"

#: ext/imgmath.py:162
#, python-format
msgid ""
"LaTeX command %r cannot be run (needed for math display), check the "
"imgmath_latex setting"
msgstr "Команда LaTeX %r не может быть выполнена (требуется для отображения математики), проверьте настройку imgmath_latex"

#: ext/imgmath.py:181
#, python-format
msgid ""
"%s command %r cannot be run (needed for math display), check the imgmath_%s "
"setting"
msgstr "Команда %s %r не может быть выполнена (требуется для отображения математики), проверьте настройку imgmath_%s"

#: ext/imgmath.py:344
#, python-format
msgid "display latex %r: %s"
msgstr "отображение latex %r: %s"

#: ext/imgmath.py:380
#, python-format
msgid "inline latex %r: %s"
msgstr "встроенный LaTeX %r: %s"

#: ext/coverage.py:48
#, python-format
msgid "invalid regex %r in %s"
msgstr "некорректное регулярное выражение %r в %s"

#: ext/coverage.py:140 ext/coverage.py:301
#, python-format
msgid "module %s could not be imported: %s"
msgstr "модуль %s не может быть импортирован: %s"

#: ext/coverage.py:148
#, python-format
msgid ""
"the following modules are documented but were not specified in "
"coverage_modules: %s"
msgstr "следующие модули документированы но не указаны в coverage_modules: %s"

#: ext/coverage.py:158
msgid ""
"the following modules are specified in coverage_modules but were not "
"documented"
msgstr "следующие модули указаны в coverage_modules но не документированы"

#: ext/coverage.py:172
#, python-brace-format, python-format
msgid ""
"Testing of coverage in the sources finished, look at the results in "
"%(outdir)s{sep}python.txt."
msgstr ""

#: ext/coverage.py:187
#, python-format
msgid "invalid regex %r in coverage_c_regexes"
msgstr "некорректное регулярное выражение %r в coverage_c_regexes"

#: ext/coverage.py:260
#, python-format
msgid "undocumented c api: %s [%s] in file %s"
msgstr "недокументированный C API: %s [%s] в файле %s"

#: ext/coverage.py:452
#, python-format
msgid "undocumented python function: %s :: %s"
msgstr "недокументированная функция Python: %s :: %s"

#: ext/coverage.py:473
#, python-format
msgid "undocumented python class: %s :: %s"
msgstr "недокументированный класс Python: %s :: %s"

#: ext/coverage.py:492
#, python-format
msgid "undocumented python method: %s :: %s :: %s"
msgstr "недокументированный метод Python: %s :: %s :: %s"

#: ext/imgconverter.py:44
#, python-format
msgid ""
"Unable to run the image conversion command %r. 'sphinx.ext.imgconverter' requires ImageMagick by default. Ensure it is installed, or set the 'image_converter' option to a custom conversion command.\n"
"\n"
"Traceback: %s"
msgstr "Не удалось запустить команду преобразования изображений %r. По умолчанию  для 'sphinx.ext.imgconverter' требуется ImageMagick. Убедитесь, что он установлен, или укажите в опции 'image_converter' собственную команду преобразования.\n\nВывод: %s"

#: ext/imgconverter.py:56 ext/imgconverter.py:90
#, python-format
msgid ""
"convert exited with error:\n"
"[stderr]\n"
"%r\n"
"[stdout]\n"
"%r"
msgstr "преобразование выполнено с ошибкой:\n[stderr]\n%r\n[stdout]\n%r"

#: ext/imgconverter.py:83
#, python-format
msgid "convert command %r cannot be run, check the image_converter setting"
msgstr "команда преобразования %r не может быть запущена, проверьте настройку image_converter"

#: ext/graphviz.py:138
msgid "Graphviz directive cannot have both content and a filename argument"
msgstr "В директиве graphviz нельзя одновременно использовать аргументы content и filename"

#: ext/graphviz.py:153
#, python-format
msgid "External Graphviz file %r not found or reading it failed"
msgstr "Внешний файл Graphviz %r не найден или недоступен для чтения"

#: ext/graphviz.py:164
msgid "Ignoring \"graphviz\" directive without content."
msgstr "Пропускается директива \"graphviz\" без содержимого."

#: ext/graphviz.py:287
#, python-format
msgid "graphviz_dot executable path must be set! %r"
msgstr "Путь к исполняемому файлу graphviz_dot должен быть задан! %r"

#: ext/graphviz.py:328
#, python-format
msgid ""
"dot command %r cannot be run (needed for graphviz output), check the "
"graphviz_dot setting"
msgstr "команда dot %r не может быть выполнена (требуется для graphviz output), проверьте настройки graphviz_dot"

#: ext/graphviz.py:339
#, python-format
msgid ""
"dot exited with error:\n"
"[stderr]\n"
"%r\n"
"[stdout]\n"
"%r"
msgstr ""

#: ext/graphviz.py:344
#, python-format
msgid ""
"dot did not produce an output file:\n"
"[stderr]\n"
"%r\n"
"[stdout]\n"
"%r"
msgstr ""

#: ext/graphviz.py:367
#, python-format
msgid "graphviz_output_format must be either 'png' or 'svg', but is %r"
msgstr ""

#: ext/graphviz.py:373 ext/graphviz.py:436 ext/graphviz.py:480
#, python-format
msgid "dot code %r: %s"
msgstr "код dot %r: %s"

#: ext/graphviz.py:493 ext/graphviz.py:501
#, python-format
msgid "[graph: %s]"
msgstr "[иллюстрация: %s]"

#: ext/graphviz.py:495 ext/graphviz.py:503
msgid "[graph]"
msgstr "[иллюстрация]"

#: ext/todo.py:61
msgid "Todo"
msgstr "План"

#: ext/todo.py:94
#, python-format
msgid "TODO entry found: %s"
msgstr "найдена запись TODO: %s"

#: ext/todo.py:152
msgid "<<original entry>>"
msgstr "<<исходная запись>>"

#: ext/todo.py:154
#, python-format
msgid "(The <<original entry>> is located in %s, line %d.)"
msgstr "(<<Исходная запись>> находится в %s, строка %d.)"

#: ext/todo.py:166
msgid "original entry"
msgstr "исходный элемент"

#: directives/code.py:66
msgid "non-whitespace stripped by dedent"
msgstr ""

#: directives/code.py:87
#, python-format
msgid "Invalid caption: %s"
msgstr "Неправильный заголовок: %s"

#: directives/code.py:131 directives/code.py:297 directives/code.py:483
#, python-format
msgid "line number spec is out of range(1-%d): %r"
msgstr "указанный номер строки находится за границами диапазона(1-%d): %r"

#: directives/code.py:216
#, python-format
msgid "Cannot use both \"%s\" and \"%s\" options"
msgstr "Нельзя одновременно использовать опции \"%s\" и \"%s\""

#: directives/code.py:231
#, python-format
msgid "Include file '%s' not found or reading it failed"
msgstr ""

#: directives/code.py:235
#, python-format
msgid ""
"Encoding %r used for reading included file '%s' seems to be wrong, try "
"giving an :encoding: option"
msgstr ""

#: directives/code.py:276
#, python-format
msgid "Object named %r not found in include file %r"
msgstr "Объект с названием %r не найден в подключенном файле %r"

#: directives/code.py:309
msgid "Cannot use \"lineno-match\" with a disjoint set of \"lines\""
msgstr ""

#: directives/code.py:314
#, python-format
msgid "Line spec %r: no lines pulled from include file %r"
msgstr "Указаны строки %r: ни одна строка не вставлена из файла %r"

#: directives/patches.py:71
msgid ""
"\":file:\" option for csv-table directive now recognizes an absolute path as"
" a relative path from source directory. Please update your document."
msgstr "Опция \":file:\" директивы csv-table теперь рассчитывает абсолютный путь относительно исходного каталога. Пожалуйста, обновите свой документ."

#: directives/other.py:119
#, python-format
msgid "toctree glob pattern %r didn't match any documents"
msgstr "глобальный шаблон toctree %r не соответствует ни одному документу"

#: directives/other.py:153 environment/adapters/toctree.py:361
#, python-format
msgid "toctree contains reference to excluded document %r"
msgstr "дерево оглавления содержит ссылку на исключённый документ %r"

#: directives/other.py:156
#, python-format
msgid "toctree contains reference to nonexisting document %r"
msgstr "дерево оглавления содержит ссылку на несуществующий документ %r"

#: directives/other.py:169
#, python-format
msgid "duplicated entry found in toctree: %s"
msgstr "повторяющееся значение в дереве оглавления: %s"

#: directives/other.py:203
msgid "Section author: "
msgstr "Автор раздела: "

#: directives/other.py:205
msgid "Module author: "
msgstr "Автор модуля: "

#: directives/other.py:207
msgid "Code author: "
msgstr "Автор кода:"

#: directives/other.py:209
msgid "Author: "
msgstr "Автор: "

#: directives/other.py:269
msgid ".. acks content is not a list"
msgstr "содержимое .. acks не является списком"

#: directives/other.py:292
msgid ".. hlist content is not a list"
msgstr "содержимое .. hlist не является списком"

#: builders/changes.py:29
#, python-format
msgid "The overview file is in %(outdir)s."
msgstr "Обзорный файл находится в %(outdir)s."

#: builders/changes.py:56
#, python-format
msgid "no changes in version %s."
msgstr "нет изменений в версии %s."

#: builders/changes.py:58
msgid "writing summary file..."
msgstr "запись итогового файла..."

#: builders/changes.py:70
msgid "Builtins"
msgstr "Встроенные функции"

#: builders/changes.py:72
msgid "Module level"
msgstr "Модуль"

#: builders/changes.py:124
msgid "copying source files..."
msgstr "копирование исходных файлов..."

#: builders/changes.py:133
#, python-format
msgid "could not read %r for changelog creation"
msgstr "не удалось прочитать %r для создания журнала изменений"

#: builders/manpage.py:37
#, python-format
msgid "The manual pages are in %(outdir)s."
msgstr "Страницы руководств находятся в %(outdir)s."

#: builders/manpage.py:45
msgid "no \"man_pages\" config value found; no manual pages will be written"
msgstr "не найдено значение настройки \"man_pages\"; страницы руководств не были записаны"

#: builders/latex/__init__.py:347 builders/manpage.py:54
#: builders/singlehtml.py:176 builders/texinfo.py:119
msgid "writing"
msgstr "запись"

#: builders/manpage.py:71
#, python-format
msgid "\"man_pages\" config value references unknown document %s"
msgstr "Значение параметра \"man_pages\" ссылается на неизвестный документ %s"

#: builders/__init__.py:224
#, python-format
msgid "a suitable image for %s builder not found: %s (%s)"
msgstr "подходящий образ для сборщика %s не найден: %s (%s)"

#: builders/__init__.py:232
#, python-format
msgid "a suitable image for %s builder not found: %s"
msgstr "подходящий образ для сборщика %s не найден: %s"

#: builders/__init__.py:255
msgid "building [mo]: "
msgstr "сборка [mo]:"

#: builders/__init__.py:258 builders/__init__.py:759 builders/__init__.py:791
msgid "writing output... "
msgstr "запись вывода..."

#: builders/__init__.py:275
#, python-format
msgid "all of %d po files"
msgstr "все из %d po-файлов"

#: builders/__init__.py:297
#, python-format
msgid "targets for %d po files that are specified"
msgstr "цели для %d файлов PO указаны"

#: builders/__init__.py:309
#, python-format
msgid "targets for %d po files that are out of date"
msgstr "цели для %d файлов PO устарели"

#: builders/__init__.py:319
msgid "all source files"
msgstr "все исходные файлы"

#: builders/__init__.py:330
#, python-format
msgid "file %r given on command line does not exist, "
msgstr "файл %r, указанный в аргументах командной строки, не существует,"

#: builders/__init__.py:337
#, python-format
msgid ""
"file %r given on command line is not under the source directory, ignoring"
msgstr "файл %r, переданный в командной строке, не найден в исходном каталоге, пропускается"

#: builders/__init__.py:348
#, python-format
msgid "file %r given on command line is not a valid document, ignoring"
msgstr "файл %r, переданный в командной строке, не является корректным документом, пропускается"

#: builders/__init__.py:361
#, python-format
msgid "%d source files given on command line"
msgstr "%d исходных файлов передано в командной строке"

#: builders/__init__.py:377
#, python-format
msgid "targets for %d source files that are out of date"
msgstr "цели для %d не обновлённых исходных файлов"

#: builders/__init__.py:395 builders/gettext.py:265
#, python-format
msgid "building [%s]: "
msgstr "сборка [%s]: "

#: builders/__init__.py:406
msgid "looking for now-outdated files... "
msgstr "ищем файлы, устаревшие на данный момент... "

#: builders/__init__.py:410
#, python-format
msgid "%d found"
msgstr "%d найден"

#: builders/__init__.py:412
msgid "none found"
msgstr "ничего не найдено"

#: builders/__init__.py:419
msgid "pickling environment"
msgstr ""

#: builders/__init__.py:426
msgid "checking consistency"
msgstr "проверка целостности"

#: builders/__init__.py:430
msgid "no targets are out of date."
msgstr "нет устаревших целей."

#: builders/__init__.py:469
msgid "updating environment: "
msgstr "обновление окружения:"

#: builders/__init__.py:494
#, python-format
msgid "%s added, %s changed, %s removed"
msgstr "%s добавлено, %s изменено, %s удалено"

#: builders/__init__.py:531
#, python-format
msgid ""
"Sphinx is unable to load the master document (%s) because it matches a "
"built-in exclude pattern %r. Please move your master document to a different"
" location."
msgstr "Sphinx не может загрузить основной документ (%s) потому что он соответствует встроенному шаблону исключений %r. Пожалуйста, переместите свой основной документ в другое расположение."

#: builders/__init__.py:540
#, python-format
msgid ""
"Sphinx is unable to load the master document (%s) because it matches an "
"exclude pattern specified in conf.py, %r. Please remove this pattern from "
"conf.py."
msgstr "Sphinx не может загрузить основной документ (%s), потому что он соответствует шаблону исключений, указанному в conf.py, %r. Пожалуйста, удалите этот шаблон из conf.py."

#: builders/__init__.py:551
#, python-format
msgid ""
"Sphinx is unable to load the master document (%s) because it is not included"
" in the custom include_patterns = %r. Ensure that a pattern in "
"include_patterns matches the master document."
msgstr "Sphinx не может загрузить основной документ (%s), потому что он не включен ни в один из настроенных include_patterns = %r. Убедитесь, что шаблон в include_patterns соответствует основному документу."

#: builders/__init__.py:558
#, python-format
msgid ""
"Sphinx is unable to load the master document (%s). The master document must "
"be within the source directory or a subdirectory of it."
msgstr "Sphinx не удалось загрузить основной документ (%s). Основной документ должен размещаться внутри исходного каталога или в одном из его подкаталогов."

#: builders/__init__.py:576 builders/__init__.py:592
msgid "reading sources... "
msgstr "чтение исходных файлов..."

#: builders/__init__.py:713
#, python-format
msgid "docnames to write: %s"
msgstr "названия документов для записи: %s"

#: builders/__init__.py:715
msgid "no docnames to write!"
msgstr ""

#: builders/__init__.py:728
msgid "preparing documents"
msgstr "подготовка документов"

#: builders/__init__.py:731
msgid "copying assets"
msgstr "копирование материалов"

#: builders/__init__.py:883
#, python-format
msgid "undecodable source characters, replacing with \"?\": %r"
msgstr "неперекодируемые исходные символы, заменены на \"?\": %r"

#: builders/epub3.py:84
#, python-format
msgid "The ePub file is in %(outdir)s."
msgstr "Файл ePub находится в %(outdir)s."

#: builders/epub3.py:189
msgid "writing nav.xhtml file..."
msgstr "запись файла nav.xhtml..."

#: builders/epub3.py:221
msgid "conf value \"epub_language\" (or \"language\") should not be empty for EPUB3"
msgstr "настройка \"epub_language\" (или \"language\") должна быть не пустой при использовании EPUB3"

#: builders/epub3.py:227
msgid "conf value \"epub_uid\" should be XML NAME for EPUB3"
msgstr "значение настройки \"epub_uid\" должно быть равно XML NAME для EPUB3"

#: builders/epub3.py:232
msgid "conf value \"epub_title\" (or \"html_title\") should not be empty for EPUB3"
msgstr "значение настройки \"epub_title\" (или \"html_title\") должно быть не пустым для EPUB3"

#: builders/epub3.py:238
msgid "conf value \"epub_author\" should not be empty for EPUB3"
msgstr "значение настройки \"epub_author\" должно быть не пустым для EPUB3"

#: builders/epub3.py:242
msgid "conf value \"epub_contributor\" should not be empty for EPUB3"
msgstr "значение настройки \"epub_contributor\" должно быть не пустым для EPUB3"

#: builders/epub3.py:247
msgid "conf value \"epub_description\" should not be empty for EPUB3"
msgstr "значение настройки \"epub_description\" должно быть не пустым для EPUB3"

#: builders/epub3.py:251
msgid "conf value \"epub_publisher\" should not be empty for EPUB3"
msgstr "значение настройки \"epub_publisher\" должно быть не пустым для EPUB3"

#: builders/epub3.py:256
msgid "conf value \"epub_copyright\" (or \"copyright\")should not be empty for EPUB3"
msgstr "значение настройки \"epub_copyright\" (или \"copyright\") должно быть не пустым для EPUB3"

#: builders/epub3.py:262
msgid "conf value \"epub_identifier\" should not be empty for EPUB3"
msgstr "значение настройки \"epub_identifier\" должно быть не пустым для EPUB3"

#: builders/epub3.py:265
msgid "conf value \"version\" should not be empty for EPUB3"
msgstr "значение настройки \"version\" должно быть не пустым для EPUB3"

#: builders/epub3.py:279 builders/html/__init__.py:1291
#, python-format
msgid "invalid css_file: %r, ignored"
msgstr "некорректный css_file: %r, пропускается"

#: builders/xml.py:31
#, python-format
msgid "The XML files are in %(outdir)s."
msgstr "Файлы XML находятся в %(outdir)s."

#: builders/html/__init__.py:1241 builders/text.py:76 builders/xml.py:90
#, python-format
msgid "error writing file %s: %s"
msgstr "ошибка записи файла %s: %s"

#: builders/xml.py:101
#, python-format
msgid "The pseudo-XML files are in %(outdir)s."
msgstr "Файлы pseudo-XML находятся в %(outdir)s."

#: builders/texinfo.py:45
#, python-format
msgid "The Texinfo files are in %(outdir)s."
msgstr "Файлы Texinfo находятся в %(outdir)s."

#: builders/texinfo.py:48
msgid ""
"\n"
"Run 'make' in that directory to run these through makeinfo\n"
"(use 'make info' here to do that automatically)."
msgstr ""

#: builders/texinfo.py:77
msgid "no \"texinfo_documents\" config value found; no documents will be written"
msgstr "не найдено значение настройки \"texinfo_documents\"; документы не были записаны"

#: builders/texinfo.py:89
#, python-format
msgid "\"texinfo_documents\" config value references unknown document %s"
msgstr "значение настройки \"texinfo_documents\" указывает на несуществующий документ %s"

#: builders/latex/__init__.py:325 builders/texinfo.py:113
#, python-format
msgid "processing %s"
msgstr "обработка %s"

#: builders/latex/__init__.py:405 builders/texinfo.py:172
msgid "resolving references..."
msgstr "разрешение ссылок..."

#: builders/latex/__init__.py:416 builders/texinfo.py:182
msgid " (in "
msgstr " (в "

#: builders/_epub_base.py:422 builders/html/__init__.py:779
#: builders/latex/__init__.py:481 builders/texinfo.py:198
msgid "copying images... "
msgstr "копирование изображений..."

#: builders/_epub_base.py:444 builders/latex/__init__.py:496
#: builders/texinfo.py:215
#, python-format
msgid "cannot copy image file %r: %s"
msgstr "Не получается скопировать файл изображения %r: %s"

#: builders/texinfo.py:222
msgid "copying Texinfo support files"
msgstr "копирование файлов поддержки Texinfo"

#: builders/texinfo.py:230
#, python-format
msgid "error writing file Makefile: %s"
msgstr "ошибка записи Makefile: %s"

#: builders/_epub_base.py:223
#, python-format
msgid "duplicated ToC entry found: %s"
msgstr "найден повторяющийся элемент оглавления: %s"

#: builders/_epub_base.py:433
#, python-format
msgid "cannot read image file %r: copying it instead"
msgstr "Не получается считать файл изображение %r: скопируйте его"

#: builders/_epub_base.py:464
#, python-format
msgid "cannot write image file %r: %s"
msgstr "Не получается записать файл изображения %r: %s"

#: builders/_epub_base.py:476
msgid "Pillow not found - copying image files"
msgstr "Pillow не найден - копирование файлов изображений"

#: builders/_epub_base.py:511
msgid "writing mimetype file..."
msgstr "запись файла mimetype..."

#: builders/_epub_base.py:520
msgid "writing META-INF/container.xml file..."
msgstr "запись файла META-INF/container.xml..."

#: builders/_epub_base.py:558
msgid "writing content.opf file..."
msgstr "запись файла content.opf..."

#: builders/_epub_base.py:591
#, python-format
msgid "unknown mimetype for %s, ignoring"
msgstr "неизвестный mimetype для %s, пропускается"

#: builders/_epub_base.py:745
msgid "node has an invalid level"
msgstr "неправильный уровень узла"

#: builders/_epub_base.py:765
msgid "writing toc.ncx file..."
msgstr "запись файла toc.ncx..."

#: builders/_epub_base.py:794
#, python-format
msgid "writing %s file..."
msgstr "записывается %s файл..."

#: builders/dummy.py:19
msgid "The dummy builder generates no files."
msgstr "Глупый сборщик не генерирует файлы."

#: builders/gettext.py:244
#, python-format
msgid "The message catalogs are in %(outdir)s."
msgstr "Каталоги сообщений находятся в %(outdir)s."

#: builders/gettext.py:266
#, python-format
msgid "targets for %d template files"
msgstr "цели для %d файлов шаблонов"

#: builders/gettext.py:271
msgid "reading templates... "
msgstr "чтение шаблонов..."

#: builders/gettext.py:307
msgid "writing message catalogs... "
msgstr "запись каталогов сообщений... "

#: builders/singlehtml.py:35
#, python-format
msgid "The HTML page is in %(outdir)s."
msgstr "Страница HTML находится в %(outdir)s."

#: builders/singlehtml.py:171
msgid "assembling single document"
msgstr "сборка единого документа"

#: builders/singlehtml.py:189
msgid "writing additional files"
msgstr "запись дополнительных файлов"

#: builders/linkcheck.py:77
#, python-format
msgid "Look for any errors in the above output or in %(outdir)s/output.txt"
msgstr ""

#: builders/linkcheck.py:149
#, python-format
msgid "broken link: %s (%s)"
msgstr "сломанная ссылка: %s (%s)"

#: builders/linkcheck.py:548
#, python-format
msgid "Anchor '%s' not found"
msgstr "Якорь '%s' не найден"

#: builders/linkcheck.py:758
#, python-format
msgid "Failed to compile regex in linkcheck_allowed_redirects: %r %s"
msgstr "Ошибка компиляции регулярного выражения в linkcheck_allowed_redirects: %r %s"

#: builders/text.py:29
#, python-format
msgid "The text files are in %(outdir)s."
msgstr "Текстовые файлы находятся в %(outdir)s."

#: transforms/i18n.py:227 transforms/i18n.py:302
#, python-brace-format
msgid ""
"inconsistent footnote references in translated message. original: {0}, "
"translated: {1}"
msgstr "противоречивые ссылки на сноску в переведённом сообщении. Оригинал: {0}, перевод: {1}"

#: transforms/i18n.py:272
#, python-brace-format
msgid ""
"inconsistent references in translated message. original: {0}, translated: "
"{1}"
msgstr "противоречивые ссылки в переведённом сообщении. Оригинал: {0}, перевод: {1}"

#: transforms/i18n.py:322
#, python-brace-format
msgid ""
"inconsistent citation references in translated message. original: {0}, "
"translated: {1}"
msgstr "противоречивые ссылки на цитату в переведённом сообщении. Оригинал: {0}, перевод: {1}"

#: transforms/i18n.py:344
#, python-brace-format
msgid ""
"inconsistent term references in translated message. original: {0}, "
"translated: {1}"
msgstr "противоречивая ссылка на термин в переведённом сообщении. Оригинал: {0}, перевод: {1}"

#: builders/html/__init__.py:486 builders/latex/__init__.py:199
#: transforms/__init__.py:129 writers/manpage.py:98 writers/texinfo.py:220
#, python-format
msgid "%b %d, %Y"
msgstr "%b %d, %Y"

#: transforms/__init__.py:139
msgid "could not calculate translation progress!"
msgstr "не удалось рассчитать прогресс перевода!"

#: transforms/__init__.py:144
msgid "no translated elements!"
msgstr "нет переведённых элементов!"

#: transforms/__init__.py:253
#, python-format
msgid ""
"4 column based index found. It might be a bug of extensions you use: %r"
msgstr "Найден четырёхстолбцовый индекс. Это может быть ошибкой используемого вами расширения: %r"

#: transforms/__init__.py:294
#, python-format
msgid "Footnote [%s] is not referenced."
msgstr "Сссылка на сноску [%s] не определена."

#: transforms/__init__.py:303
msgid "Footnote [*] is not referenced."
msgstr "Ссылка на сноску [*] не определена."

#: transforms/__init__.py:314
msgid "Footnote [#] is not referenced."
msgstr "Ссылка на сноску [#] отсутствует."

#: _cli/__init__.py:73
msgid "Usage:"
msgstr "Использование:"

#: _cli/__init__.py:75
#, python-brace-format
msgid "{0} [OPTIONS] <COMMAND> [<ARGS>]"
msgstr "{0} [ОПЦИИ] <COMMAND> [<ARGS>]"

#: _cli/__init__.py:78
msgid "  The Sphinx documentation generator."
msgstr "Генератор документации Sphinx."

#: _cli/__init__.py:87
msgid "Commands:"
msgstr "Команды:"

#: _cli/__init__.py:98
msgid "Options"
msgstr "Опции"

#: _cli/__init__.py:113 _cli/__init__.py:181
msgid "For more information, visit https://www.sphinx-doc.org/en/master/man/."
msgstr "Для получения дополнительной информации посетите https://www.sphinx-doc.org/en/master/man/."

#: _cli/__init__.py:171
#, python-brace-format
msgid ""
"{0}: error: {1}\n"
"Run '{0} --help' for information"
msgstr "{0}: ошибка: {1}\nВыполните '{0} --help' для получения информации"

#: _cli/__init__.py:179
msgid "   Manage documentation with Sphinx."
msgstr "Управление документацией со Sphinx."

#: _cli/__init__.py:191
msgid "Show the version and exit."
msgstr "Показать версию и выйти."

#: _cli/__init__.py:199
msgid "Show this message and exit."
msgstr "Показать это сообщение и выйти."

#: _cli/__init__.py:203
msgid "Logging"
msgstr "Журналирование"

#: _cli/__init__.py:210
msgid "Increase verbosity (can be repeated)"
msgstr "Увеличить подробность (можно указать несколько раз)"

#: _cli/__init__.py:218
msgid "Only print errors and warnings."
msgstr "Выводить только ошибки и предупреждения."

#: _cli/__init__.py:225
msgid "No output at all"
msgstr ""

#: _cli/__init__.py:231
msgid "<command>"
msgstr "<command>"

#: _cli/__init__.py:263
msgid "See 'sphinx --help'.\n"
msgstr "См. 'sphinx --help'.\n"

#: environment/__init__.py:86
msgid "new config"
msgstr "новая конфигурация"

#: environment/__init__.py:87
msgid "config changed"
msgstr "конфигурация изменена"

#: environment/__init__.py:88
msgid "extensions changed"
msgstr "расширения изменились"

#: environment/__init__.py:253
msgid "build environment version not current"
msgstr "версия окружения сборки отличается от текущей"

#: environment/__init__.py:255
msgid "source directory has changed"
msgstr "каталог исходных файлов изменился"

#: environment/__init__.py:325
#, python-format
msgid "The configuration has changed (1 option: %r)"
msgstr "Конфигурация изменилась (1 опция: %r)"

#: environment/__init__.py:330
#, python-format
msgid "The configuration has changed (%d options: %s)"
msgstr "Конфигурация изменилась (%d опций: %s)"

#: environment/__init__.py:336
#, python-format
msgid "The configuration has changed (%d options: %s, ...)"
msgstr "Конфигурация изменилась (%d опций: %s, ...)"

#: environment/__init__.py:379
msgid ""
"This environment is incompatible with the selected builder, please choose "
"another doctree directory."
msgstr "Это окружение несовместимо с выбранным сборщиком, пожалуйста, выберите другой каталог doctree."

#: environment/__init__.py:493
#, python-format
msgid "Failed to scan documents in %s: %r"
msgstr "Ошибка сканирования документов в %s: %r"

#: environment/__init__.py:658 ext/intersphinx/_resolve.py:234
#, python-format
msgid "Domain %r is not registered"
msgstr "Домен %r не зарегистрирован"

#: environment/__init__.py:813
msgid "document isn't included in any toctree"
msgstr "документ не включен ни в одно оглавление"

#: environment/__init__.py:859
msgid "self referenced toctree found. Ignored."
msgstr "найдено оглавление, ссылающееся на само себя. Пропускается."

#: environment/__init__.py:889
#, python-format
msgid "document is referenced in multiple toctrees: %s, selecting: %s <- %s"
msgstr ""

#: util/i18n.py:100
#, python-format
msgid "reading error: %s, %s"
msgstr "ошибка чтения: %s, %s"

#: util/i18n.py:113
#, python-format
msgid "writing error: %s, %s"
msgstr "ошибка записи: %s, %s"

#: util/i18n.py:146
#, python-format
msgid "locale_dir %s does not exist"
msgstr "locale_dir %s не существует"

#: util/i18n.py:236
#, python-format
msgid "Invalid Babel locale: %r."
msgstr ""

#: util/i18n.py:245
#, python-format
msgid ""
"Invalid date format. Quote the string by single quote if you want to output "
"it directly: %s"
msgstr "Неправильный формат даты. Оберните строку в одинарные кавычки, если хоитет вывести её \"как есть\": %s"

#: util/docfields.py:103
#, python-format
msgid ""
"Problem in %s domain: field is supposed to use role '%s', but that role is "
"not in the domain."
msgstr "Проблема в домене %s: предполагается, что в поле должна использоваться роль  '%s', но этой роли нет в домене."

#: util/nodes.py:423
#, python-format
msgid ""
"%r is deprecated for index entries (from entry %r). Use 'pair: %s' instead."
msgstr "%r считается устаревшим для записей индекса (из записи %r). Вместо этого используйте 'pair: %s'."

#: util/nodes.py:490
#, python-format
msgid "toctree contains ref to nonexisting file %r"
msgstr "оглавление содержит ссылку на несуществующий файл %r"

#: util/nodes.py:706
#, python-format
msgid "exception while evaluating only directive expression: %s"
msgstr "исключение при вычислении значения директивы only: %s"

#: util/display.py:82
msgid "skipped"
msgstr "пропущено"

#: util/display.py:87
msgid "failed"
msgstr "сбой"

#: util/osutil.py:131
#, python-format
msgid ""
"Aborted attempted copy from %s to %s (the destination path has existing "
"data)."
msgstr ""

#: util/docutils.py:309
#, python-format
msgid "unknown directive name: %s"
msgstr ""

#: util/docutils.py:345
#, python-format
msgid "unknown role name: %s"
msgstr ""

#: util/docutils.py:789
#, python-format
msgid "unknown node type: %r"
msgstr "неизвестный тип узла: %r"

#: util/fileutil.py:76
#, python-format
msgid ""
"Aborted attempted copy from rendered template %s to %s (the destination path"
" has existing data)."
msgstr ""

#: util/fileutil.py:89
#, python-format
msgid "Writing evaluated template result to %s"
msgstr "Запись результатов обработки шаблона в %s"

#: util/rst.py:73
#, python-format
msgid "default role %s not found"
msgstr "роль по умолчанию %s не найдена"

#: util/inventory.py:147
#, python-format
msgid "inventory <%s> contains duplicate definitions of %s"
msgstr "инвентарь <%s> содержит повторяющиеся определения %s"

#: util/inventory.py:166
#, python-format
msgid "inventory <%s> contains multiple definitions for %s"
msgstr "инвентарь <%s> содержит множество определений для %s"

#: writers/latex.py:1097 writers/manpage.py:259 writers/texinfo.py:663
msgid "Footnotes"
msgstr "Сноски"

#: writers/manpage.py:289 writers/text.py:945
#, python-format
msgid "[image: %s]"
msgstr "[рисунок: %s]"

#: writers/manpage.py:290 writers/text.py:946
msgid "[image]"
msgstr "[рисунок]"

#: builders/latex/__init__.py:206 domains/std/__init__.py:771
#: domains/std/__init__.py:784 templates/latex/latex.tex.jinja:106
#: themes/basic/genindex-single.html:22 themes/basic/genindex-single.html:48
#: themes/basic/genindex-split.html:3 themes/basic/genindex-split.html:6
#: themes/basic/genindex.html:3 themes/basic/genindex.html:26
#: themes/basic/genindex.html:59 themes/basic/layout.html:127
#: writers/texinfo.py:514
msgid "Index"
msgstr "Алфавитный указатель"

#: writers/latex.py:743 writers/texinfo.py:646
msgid ""
"encountered title node not in section, topic, table, admonition or sidebar"
msgstr ""

#: writers/texinfo.py:1217
msgid "caption not inside a figure."
msgstr "заголовок не внутри иллюстрации."

#: writers/texinfo.py:1303
#, python-format
msgid "unimplemented node type: %r"
msgstr "нереализованный тип узла: %r"

#: writers/latex.py:361
#, python-format
msgid "unknown %r toplevel_sectioning for class %r"
msgstr "неизвестный %r toplevel_sectioning для класса %r"

#: builders/latex/__init__.py:224 writers/latex.py:411
#, python-format
msgid "no Babel option known for language %r"
msgstr "неизвестная опция Babel для языка %r"

#: writers/latex.py:429
msgid "too large :maxdepth:, ignored."
msgstr "слишком большое значение :maxdepth:, пропускается."

#: writers/latex.py:591
#, python-format
msgid "template %s not found; loading from legacy %s instead"
msgstr "шаблон %s не найден; вместо этого загружается унаследованный %s"

#: writers/latex.py:707
msgid "document title is not a single Text node"
msgstr "заголовок документа не является одиночным Text node"

#: writers/html5.py:572 writers/latex.py:1106
#, python-format
msgid "unsupported rubric heading level: %s"
msgstr "неподдерживаемый уровень заголовка рубрики: %s"

#: writers/latex.py:1183
msgid ""
"both tabularcolumns and :widths: option are given. :widths: is ignored."
msgstr "заданы tabularcolumns и опция :widths:. :widths: пропускается."

#: writers/latex.py:1580
#, python-format
msgid "dimension unit %s is invalid. Ignored."
msgstr "неправильная единица измерения %s. Пропускается."

#: writers/latex.py:1939
#, python-format
msgid "unknown index entry type %s found"
msgstr "найдена запись неизвестного типа индекса %s"

#: domains/math.py:128 writers/latex.py:2495
#, python-format
msgid "Invalid math_eqref_format: %r"
msgstr "Неправильный math_eqref_format: %r"

#: writers/html5.py:96 writers/html5.py:105
msgid "Link to this definition"
msgstr "Ссылка на это определение"

#: writers/html5.py:431
#, python-format
msgid "numfig_format is not defined for %s"
msgstr "numfig_format не определён для %s"

#: writers/html5.py:441
#, python-format
msgid "Any IDs not assigned for %s node"
msgstr "Любые ID не разрешены для узла %s"

#: writers/html5.py:496
msgid "Link to this term"
msgstr "Ссылка на этот термин"

#: writers/html5.py:548 writers/html5.py:553
msgid "Link to this heading"
msgstr "Ссылка на этот заголовок"

#: writers/html5.py:558
msgid "Link to this table"
msgstr "Ссылка на эту таблицу"

#: writers/html5.py:636
msgid "Link to this code"
msgstr "Ссылка на этот код"

#: writers/html5.py:638
msgid "Link to this image"
msgstr "Ссылка на это изображение"

#: writers/html5.py:640
msgid "Link to this toctree"
msgstr "Ссылка на это оглавление"

#: writers/html5.py:766
msgid "Could not obtain image size. :scale: option is ignored."
msgstr "Не удалось получить размеры изображения. Опция :scale: пропускается."

#: domains/__init__.py:322
#, python-format
msgid "%s %s"
msgstr "%s %s"

#: domains/math.py:73
#, python-format
msgid "duplicate label of equation %s, other instance in %s"
msgstr "повторяющаяся метка уравнения %s, также используется в %s"

#: domains/javascript.py:182
#, python-format
msgid "%s() (built-in function)"
msgstr "%s() (встроенная функция)"

#: domains/javascript.py:183 domains/python/__init__.py:287
#, python-format
msgid "%s() (%s method)"
msgstr "%s() (метод %s)"

#: domains/javascript.py:185
#, python-format
msgid "%s() (class)"
msgstr "%s() (класс)"

#: domains/javascript.py:187
#, python-format
msgid "%s (global variable or constant)"
msgstr "%s (глобальная переменная или константа)"

#: domains/javascript.py:189 domains/python/__init__.py:378
#, python-format
msgid "%s (%s attribute)"
msgstr "%s (атрибут %s)"

#: domains/javascript.py:273
msgid "Arguments"
msgstr "Аргументы"

#: domains/cpp/__init__.py:489 domains/javascript.py:280
msgid "Throws"
msgstr "Бросает исключение"

#: domains/c/__init__.py:339 domains/cpp/__init__.py:502
#: domains/javascript.py:287 domains/python/_object.py:221
msgid "Returns"
msgstr "Результат"

#: domains/c/__init__.py:345 domains/javascript.py:293
#: domains/python/_object.py:227
msgid "Return type"
msgstr "Тип результата"

#: domains/javascript.py:370
#, python-format
msgid "%s (module)"
msgstr "%s (модуль)"

#: domains/c/__init__.py:751 domains/cpp/__init__.py:941
#: domains/javascript.py:415 domains/python/__init__.py:740
msgid "function"
msgstr "функция"

#: domains/javascript.py:416 domains/python/__init__.py:744
msgid "method"
msgstr "метод"

#: domains/cpp/__init__.py:939 domains/javascript.py:417
#: domains/python/__init__.py:742
msgid "class"
msgstr "класс"

#: domains/javascript.py:418 domains/python/__init__.py:741
msgid "data"
msgstr "данные"

#: domains/javascript.py:419 domains/python/__init__.py:747
msgid "attribute"
msgstr "атрибут"

#: domains/javascript.py:420 domains/python/__init__.py:750
msgid "module"
msgstr "модуль"

#: domains/javascript.py:454
#, python-format
msgid "duplicate %s description of %s, other %s in %s"
msgstr "повтор описания %s для %s, другое %s в %s"

#: domains/changeset.py:26
#, python-format
msgid "Added in version %s"
msgstr "Добавлено в версии %s"

#: domains/changeset.py:27
#, python-format
msgid "Changed in version %s"
msgstr "Изменено в версии %s"

#: domains/changeset.py:28
#, python-format
msgid "Deprecated since version %s"
msgstr "Устарело, начиная с версии %s"

#: domains/changeset.py:29
#, python-format
msgid "Removed in version %s"
msgstr "Удалено в версии %s"

#: domains/rst.py:131 domains/rst.py:190
#, python-format
msgid "%s (directive)"
msgstr "%s (директива)"

#: domains/rst.py:191 domains/rst.py:202
#, python-format
msgid ":%s: (directive option)"
msgstr ":%s: (опция директивы)"

#: domains/rst.py:224
#, python-format
msgid "%s (role)"
msgstr "%s (роль)"

#: domains/rst.py:234
msgid "directive"
msgstr "директива"

#: domains/rst.py:235
msgid "directive-option"
msgstr ""

#: domains/rst.py:236
msgid "role"
msgstr "роль"

#: domains/rst.py:262
#, python-format
msgid "duplicate description of %s %s, other instance in %s"
msgstr "повторное описание %s %s, другой экземпляр в %s"

#: domains/citation.py:75
#, python-format
msgid "duplicate citation %s, other instance in %s"
msgstr "повторное цитирование %s, другой экземпляр в %s"

#: domains/citation.py:92
#, python-format
msgid "Citation [%s] is not referenced."
msgstr "Ссылка на цитату [%s] отсутствует."

#: locale/__init__.py:228
msgid "Attention"
msgstr "Внимание"

#: locale/__init__.py:229
msgid "Caution"
msgstr "Осторожно"

#: locale/__init__.py:230
msgid "Danger"
msgstr "Опасно"

#: locale/__init__.py:231
msgid "Error"
msgstr "Ошибка"

#: locale/__init__.py:232
msgid "Hint"
msgstr "Подсказка"

#: locale/__init__.py:233
msgid "Important"
msgstr "Важно"

#: locale/__init__.py:234
msgid "Note"
msgstr "Примечание"

#: locale/__init__.py:235
msgid "See also"
msgstr "См. также"

#: locale/__init__.py:236
msgid "Tip"
msgstr "Совет"

#: locale/__init__.py:237
msgid "Warning"
msgstr "Предупреждение"

#: cmd/quickstart.py:52
msgid "automatically insert docstrings from modules"
msgstr "автоматически вставлены docstrings из модулей"

#: cmd/quickstart.py:53
msgid "automatically test code snippets in doctest blocks"
msgstr "автоматическое тестирование фрагментов кода в блоках doctest"

#: cmd/quickstart.py:54
msgid "link between Sphinx documentation of different projects"
msgstr "связь между разными проектами Sphinx"

#: cmd/quickstart.py:55
msgid "write \"todo\" entries that can be shown or hidden on build"
msgstr "укажите записи \"todo\", которые могут быть показаны или скрыты при сборке"

#: cmd/quickstart.py:56
msgid "checks for documentation coverage"
msgstr "проверка полноты документации"

#: cmd/quickstart.py:57
msgid "include math, rendered as PNG or SVG images"
msgstr "включить математику, отображаемую как изображения PNG или SVG"

#: cmd/quickstart.py:58
msgid "include math, rendered in the browser by MathJax"
msgstr "включить математику, отображаемую в браузере с помомощью MathJax"

#: cmd/quickstart.py:59
msgid "conditional inclusion of content based on config values"
msgstr "условное включение содержимого на основе значений конфигурации"

#: cmd/quickstart.py:60
msgid "include links to the source code of documented Python objects"
msgstr "включить ссылки на исходный код документируемых объектов Python"

#: cmd/quickstart.py:61
msgid "create .nojekyll file to publish the document on GitHub pages"
msgstr "создать файл .nojekyll для публикации документа на GitHub Pages"

#: cmd/quickstart.py:110
msgid "Please enter a valid path name."
msgstr "Пожалуйста, укажите корректный путь."

#: cmd/quickstart.py:126
msgid "Please enter some text."
msgstr "Пожалуйста, введите текст."

#: cmd/quickstart.py:133
#, python-format
msgid "Please enter one of %s."
msgstr "Пожалуйста, введите одно из %s."

#: cmd/quickstart.py:141
msgid "Please enter either 'y' or 'n'."
msgstr "Пожалуйста, введите 'y' или 'n'."

#: cmd/quickstart.py:147
msgid "Please enter a file suffix, e.g. '.rst' or '.txt'."
msgstr "Пожалуйста, введите расширение файла, например, '.rst' или '.txt'."

#: cmd/quickstart.py:229
#, python-format
msgid "Welcome to the Sphinx %s quickstart utility."
msgstr "Добро пожаловать в утилиту quickstart Sphinx %s."

#: cmd/quickstart.py:234
msgid ""
"Please enter values for the following settings (just press Enter to\n"
"accept a default value, if one is given in brackets)."
msgstr "Пожалуйста, введите значения для следующих настроек (нажмите Enter для\nиспользования значения по умолчанию, оно указано в квадратных скобках)."

#: cmd/quickstart.py:241
#, python-format
msgid "Selected root path: %s"
msgstr "Выбранный корневой путь: %s"

#: cmd/quickstart.py:244
msgid "Enter the root path for documentation."
msgstr "Введите корневой путь для документации."

#: cmd/quickstart.py:245
msgid "Root path for the documentation"
msgstr "Корневой каталог документации"

#: cmd/quickstart.py:254
msgid "Error: an existing conf.py has been found in the selected root path."
msgstr "Ошибка: по указанному корневому пути уже существует conf.py."

#: cmd/quickstart.py:259
msgid "sphinx-quickstart will not overwrite existing Sphinx projects."
msgstr "sphinx-quickstart не будет перезаписывать существующие проекты Sphinx."

#: cmd/quickstart.py:262
msgid "Please enter a new root path (or just Enter to exit)"
msgstr "Пожалуйста, укажите новый корневой путь (или нажмите Enter для выхода)"

#: cmd/quickstart.py:273
msgid ""
"You have two options for placing the build directory for Sphinx output.\n"
"Either, you use a directory \"_build\" within the root path, or you separate\n"
"\"source\" and \"build\" directories within the root path."
msgstr ""

#: cmd/quickstart.py:279
msgid "Separate source and build directories (y/n)"
msgstr "Разделить каталоги исходных файлов и результатов сборки (y/n)"

#: cmd/quickstart.py:286
msgid ""
"Inside the root directory, two more directories will be created; \"_templates\"\n"
"for custom HTML templates and \"_static\" for custom stylesheets and other static\n"
"files. You can enter another prefix (such as \".\") to replace the underscore."
msgstr "Внутри корневого каталога могут быт ьсозданы 2 каталога; \"_templates\"\nдля собственных шаблонов HTML и \"_static\" для таблиц стилей и других статичных\nфайлов. Вы можете ввести другой префикс (например, \".\") для замены знака подчёркивания."

#: cmd/quickstart.py:291
msgid "Name prefix for templates and static dir"
msgstr "Префикс имен каталогов с шаблонами и статикой"

#: cmd/quickstart.py:297
msgid ""
"The project name will occur in several places in the built documentation."
msgstr "Название проекта будет встречаться в нескольких местах рабочей документации."

#: cmd/quickstart.py:300
msgid "Project name"
msgstr "Название проекта"

#: cmd/quickstart.py:302
msgid "Author name(s)"
msgstr "Имя(ена) автора(ов)"

#: cmd/quickstart.py:308
msgid ""
"Sphinx has the notion of a \"version\" and a \"release\" for the\n"
"software. Each version can have multiple releases. For example, for\n"
"Python the version is something like 2.5 or 3.0, while the release is\n"
"something like 2.5.1 or 3.0a1. If you don't need this dual structure,\n"
"just set both to the same value."
msgstr "Sphinx использует понятия \"версия\" и \"релиз\" для \nпрограммного обеспечения. Каждая версия может иметь несколько релизов. Например, для\nPyhon версии выглядят как 2.5 или 3.0, при этом релизы\nвыглядят как 2.5.1 или 3.0a1. Если вам не нужна двойная структура,\nпросто используйте одно и то же значение для обоих параметров."

#: cmd/quickstart.py:315
msgid "Project version"
msgstr "Версия проекта"

#: cmd/quickstart.py:317
msgid "Project release"
msgstr "Релиз проекта"

#: cmd/quickstart.py:323
msgid ""
"If the documents are to be written in a language other than English,\n"
"you can select a language here by its language code. Sphinx will then\n"
"translate text that it generates into that language.\n"
"\n"
"For a list of supported codes, see\n"
"https://www.sphinx-doc.org/en/master/usage/configuration.html#confval-language."
msgstr "Если для написания документации используется язык, отличный от английского,\nвы можете указать его здесь, выбрав нужный код языка. Затем Sphinx переведёт генерируемый текст на этот язык.\n\nДля получения списка поддерживаемых кодов см.\nhttps://www.sphinx-doc.org/en/master/usage/configuration.html#confval-language."

#: cmd/quickstart.py:331
msgid "Project language"
msgstr "Язык проекта"

#: cmd/quickstart.py:339
msgid ""
"The file name suffix for source files. Commonly, this is either \".txt\"\n"
"or \".rst\". Only files with this suffix are considered documents."
msgstr "Расширение исходных файлов. Как правило, это \".txt\"\nили \".rst\". Только файлы с указанным расширением считаются документами."

#: cmd/quickstart.py:343
msgid "Source file suffix"
msgstr "Суффикс исходный файлов"

#: cmd/quickstart.py:349
msgid ""
"One document is special in that it is considered the top node of the\n"
"\"contents tree\", that is, it is the root of the hierarchical structure\n"
"of the documents. Normally, this is \"index\", but if your \"index\"\n"
"document is a custom template, you can also set this to another filename."
msgstr ""

#: cmd/quickstart.py:356
msgid "Name of your master document (without suffix)"
msgstr "Название основного документа (без суффикса)"

#: cmd/quickstart.py:367
#, python-format
msgid ""
"Error: the master file %s has already been found in the selected root path."
msgstr "Ошибка: основной файл %s уже существует в корневом каталоге по указанному пути."

#: cmd/quickstart.py:373
msgid "sphinx-quickstart will not overwrite the existing file."
msgstr "sphinx-quickstart не перезапишет существующий файл."

#: cmd/quickstart.py:377
msgid ""
"Please enter a new file name, or rename the existing file and press Enter"
msgstr "Введите имя нового файла, или переименуйте существующий файл и нажмите Enter"

#: cmd/quickstart.py:385
msgid "Indicate which of the following Sphinx extensions should be enabled:"
msgstr "Укажите, какие из расширений Sphinx должны быть включены:"

#: cmd/quickstart.py:396
msgid ""
"Note: imgmath and mathjax cannot be enabled at the same time. imgmath has "
"been deselected."
msgstr "Примечание: imgmath и mathjax не могут быть включены одновременно. imgmath выключен."

#: cmd/quickstart.py:406
msgid ""
"A Makefile and a Windows command file can be generated for you so that you\n"
"only have to run e.g. `make html' instead of invoking sphinx-build\n"
"directly."
msgstr "Makefile и командный файл Windows могут быть сгенерированы,\nчтобы вы могли выполнять `make html' вместо прямого вызова\nsphinx-build."

#: cmd/quickstart.py:411
msgid "Create Makefile? (y/n)"
msgstr "Создать Makefile? (y/n)"

#: cmd/quickstart.py:415
msgid "Create Windows command file? (y/n)"
msgstr "Создать файл команд Windows? (y/n)"

#: cmd/quickstart.py:467 ext/apidoc/_generate.py:76
#, python-format
msgid "Creating file %s."
msgstr "Создание файла %s."

#: cmd/quickstart.py:472 ext/apidoc/_generate.py:73
#, python-format
msgid "File %s already exists, skipping."
msgstr "Файл %s уже существует, пропускаем."

#: cmd/quickstart.py:515
msgid "Finished: An initial directory structure has been created."
msgstr "Завершено: Исходная структура каталогов создана."

#: cmd/quickstart.py:519
#, python-format
msgid ""
"You should now populate your master file %s and create other documentation\n"
"source files. "
msgstr "Теперь вам нужно заполнить свой основной файл %s и создать другие\nисходные файлы документации. "

#: cmd/quickstart.py:526
msgid ""
"Use the Makefile to build the docs, like so:\n"
"   make builder"
msgstr "Используйте Makefile для сборки документации, например:\n   make builder"

#: cmd/quickstart.py:530
#, python-format
msgid ""
"Use the sphinx-build command to build the docs, like so:\n"
"   sphinx-build -b builder %s %s"
msgstr "Используйте команду sphinx-build для сборки документации, например:\n   sphinx-build -b builder %s %s"

#: cmd/quickstart.py:537
msgid ""
"where \"builder\" is one of the supported builders, e.g. html, latex or "
"linkcheck."
msgstr "где \"builder\" - один из поддерживаемых сборщиков, например, html, latex или linkcheck."

#: cmd/quickstart.py:572
msgid ""
"\n"
"Generate required files for a Sphinx project.\n"
"\n"
"sphinx-quickstart is an interactive tool that asks some questions about your\n"
"project and then generates a complete documentation directory and sample\n"
"Makefile to be used with sphinx-build.\n"
msgstr "\nГенерирует необходимые файлы для проекта Sphinx.\n\nsphinx-quickstart это интерактивный инструмент, который задаст вам несколько вопросов о вашем\nпроекте и затем полностью сгенерирует каталог документации и пример\nMakefile для использования со sphinx-build.\n"

#: cmd/build.py:73 cmd/quickstart.py:581 ext/apidoc/_cli.py:27
#: ext/autosummary/generate.py:835
msgid "For more information, visit <https://www.sphinx-doc.org/>."
msgstr "Для получения дополнительной информации посетите <https://www.sphinx-doc.org/>."

#: cmd/quickstart.py:591
msgid "quiet mode"
msgstr "тихий режим"

#: cmd/quickstart.py:601
msgid "project root"
msgstr "корень проекта"

#: cmd/quickstart.py:604
msgid "Structure options"
msgstr "Опции структуры"

#: cmd/quickstart.py:610
msgid "if specified, separate source and build dirs"
msgstr "если указано, разделить исходный и целевой каталоги"

#: cmd/quickstart.py:616
msgid "if specified, create build dir under source dir"
msgstr "если указано, разместить целевой каталог в исходном"

#: cmd/quickstart.py:622
msgid "replacement for dot in _templates etc."
msgstr "замена точки в _templates и т. п."

#: cmd/quickstart.py:625
msgid "Project basic options"
msgstr "Основные настройки проекта"

#: cmd/quickstart.py:627
msgid "project name"
msgstr "название проекта"

#: cmd/quickstart.py:630
msgid "author names"
msgstr "имена авторов"

#: cmd/quickstart.py:637
msgid "version of project"
msgstr "версия проекта"

#: cmd/quickstart.py:644
msgid "release of project"
msgstr "релиз проекта"

#: cmd/quickstart.py:651
msgid "document language"
msgstr "язык документа"

#: cmd/quickstart.py:654
msgid "source file suffix"
msgstr "суффикс исходных файлов"

#: cmd/quickstart.py:657
msgid "master document name"
msgstr "название основного документа"

#: cmd/quickstart.py:660
msgid "use epub"
msgstr "использовать epub"

#: cmd/quickstart.py:663
msgid "Extension options"
msgstr "Опции расширения"

#: cmd/quickstart.py:670
#, python-format
msgid "enable %s extension"
msgstr "включить расширение %s"

#: cmd/quickstart.py:677
msgid "enable arbitrary extensions"
msgstr "включить произвольные расширения"

#: cmd/quickstart.py:680
msgid "Makefile and Batchfile creation"
msgstr "Создание Makefile и Batchfile"

#: cmd/quickstart.py:686
msgid "create makefile"
msgstr "создать Makefile"

#: cmd/quickstart.py:692
msgid "do not create makefile"
msgstr "не создавать Makefile"

#: cmd/quickstart.py:699
msgid "create batchfile"
msgstr "создать batchfile"

#: cmd/quickstart.py:705
msgid "do not create batchfile"
msgstr "не создавать batchfile"

#: cmd/quickstart.py:714
msgid "use make-mode for Makefile/make.bat"
msgstr "использовать make-mode для Makefile/make.bat"

#: cmd/quickstart.py:717 ext/apidoc/_cli.py:243
msgid "Project templating"
msgstr "Шаблонизация проекта"

#: cmd/quickstart.py:723 ext/apidoc/_cli.py:249
msgid "template directory for template files"
msgstr "каталог шаблонов для файлов шаблонов"

#: cmd/quickstart.py:730
msgid "define a template variable"
msgstr "определить переменную шаблона"

#: cmd/quickstart.py:766
msgid "\"quiet\" is specified, but any of \"project\" or \"author\" is not specified."
msgstr "указано \"quiet\", но \"project\" или \"author\" не заданы."

#: cmd/quickstart.py:785
msgid ""
"Error: specified path is not a directory, or sphinx files already exist."
msgstr "Ошибка: указанный путь не является каталогом или файлы Sphinx в нём уже существуют."

#: cmd/quickstart.py:792
msgid ""
"sphinx-quickstart only generate into a empty directory. Please specify a new"
" root path."
msgstr "sphinx-quickstart генерирует только в пустой каталог. Пожалуйста, укажите новый корневой путь."

#: cmd/quickstart.py:809
#, python-format
msgid "Invalid template variable: %s"
msgstr "Неправильная переменная шаблона: %s"

#: cmd/build.py:64
msgid "job number should be a positive number"
msgstr "номер задания должен быть положительным числом"

#: cmd/build.py:74
msgid ""
"\n"
"Generate documentation from source files.\n"
"\n"
"sphinx-build generates documentation from the files in SOURCEDIR and places it\n"
"in OUTPUTDIR. It looks for 'conf.py' in SOURCEDIR for the configuration\n"
"settings. The 'sphinx-quickstart' tool may be used to generate template files,\n"
"including 'conf.py'\n"
"\n"
"sphinx-build can create documentation in different formats. A format is\n"
"selected by specifying the builder name on the command line; it defaults to\n"
"HTML. Builders can also perform other tasks related to documentation\n"
"processing.\n"
"\n"
"By default, everything that is outdated is built. Output only for selected\n"
"files can be built by specifying individual filenames.\n"
msgstr "\nСоздание документации из исходных файлов.\n\nsphinx-build создает документацию из файлов в SOURCEDIR и помещает ее в\nOUTPUTDIR. Он ищет 'conf.py' в SOURCEDIR для определения настроек\nконфигурации. Инструмент 'sphinx-quickstart' может быть использован для\nсоздания файлов шаблонов, включая 'conf.py'\n\nsphinx-build может создавать документацию в различных форматах. Формат\nвыбирается путем указания названия сборщика в аргументах командной\nстроки; по умолчанию используется HTML. Сборщики также могут\n выполнять другие задачи, связанные с обработкой документации.\n\nПо умолчанию собирается все что устарело. Вывод только для выбранных\nфайлов может быть реализован путем указания отдельных имен файлов.\n"

#: cmd/build.py:100
msgid "path to documentation source files"
msgstr "путь к исходным файлам документации"

#: cmd/build.py:103
msgid "path to output directory"
msgstr "путь к целевому каталогу"

#: cmd/build.py:109
msgid ""
"(optional) a list of specific files to rebuild. Ignored if --write-all is "
"specified"
msgstr "(опционально) список файлов для пересборки. Пропускается если указано --write-all"

#: cmd/build.py:114
msgid "general options"
msgstr "основные опции"

#: cmd/build.py:121
msgid "builder to use (default: 'html')"
msgstr "используемый сборщик (по умолчанию: 'html')"

#: cmd/build.py:131
msgid ""
"run in parallel with N processes, when possible. 'auto' uses the number of "
"CPU cores"
msgstr "запустить параллельно N процессов, если возможно. 'auto'  соответствует числу ядер CPU"

#: cmd/build.py:140
msgid "write all files (default: only write new and changed files)"
msgstr "записать все файлы (по умолчанию: записать только новые и изменённые файлы)"

#: cmd/build.py:147
msgid "don't use a saved environment, always read all files"
msgstr "не использовать сохраненное окружение, всегда читать все файлы"

#: cmd/build.py:150
msgid "path options"
msgstr "опции пути"

#: cmd/build.py:157
msgid ""
"directory for doctree and environment files (default: OUTPUT_DIR/.doctrees)"
msgstr "каталог для дерева документов и файлов окружения (по умолчанию: OUTPUT_DIR/.doctrees)"

#: cmd/build.py:166
msgid "directory for the configuration file (conf.py) (default: SOURCE_DIR)"
msgstr "каталог с файлом конфигурации (conf.py) (по умолчанию: SOURCE_DIR)"

#: cmd/build.py:175
msgid "use no configuration file, only use settings from -D options"
msgstr "не использовать конфигурационный файл, только настройки, указанные в опциях -D"

#: cmd/build.py:184
msgid "override a setting in configuration file"
msgstr "переопределяет настройки в конфигурационном файле"

#: cmd/build.py:193
msgid "pass a value into HTML templates"
msgstr "передать значение в шаблон HTML"

#: cmd/build.py:202
msgid "define tag: include \"only\" blocks with TAG"
msgstr "определить тег: включить \"только\" блоги с ТЕГОМ"

#: cmd/build.py:209
msgid "nitpicky mode: warn about all missing references"
msgstr "придирчивый режим: предупреждать обо всех потерянных ссылках"

#: cmd/build.py:212
msgid "console output options"
msgstr "опции вывода в терминал"

#: cmd/build.py:219
msgid "increase verbosity (can be repeated)"
msgstr "увеличить детальность (может повторяться несколько раз)"

#: cmd/build.py:226 ext/apidoc/_cli.py:66
msgid "no output on stdout, just warnings on stderr"
msgstr "без вывода в stdout, только предупреждения в stderr"

#: cmd/build.py:233
msgid "no output at all, not even warnings"
msgstr ""

#: cmd/build.py:241
msgid "do emit colored output (default: auto-detect)"
msgstr "использовать цветной вывод (по умолчанию: автоопределение)"

#: cmd/build.py:249
msgid "do not emit colored output (default: auto-detect)"
msgstr "не использовать цветной вывод (по умолчанию: автоопределение)"

#: cmd/build.py:252
msgid "warning control options"
msgstr "настройки предупреждений"

#: cmd/build.py:258
msgid "write warnings (and errors) to given file"
msgstr "запись предупреждений (и ошибок) в указанный файл"

#: cmd/build.py:265
msgid "turn warnings into errors"
msgstr "обрабатывать предупреждение как ошибки"

#: cmd/build.py:273
msgid "show full traceback on exception"
msgstr "отображать полный traceback при исключении"

#: cmd/build.py:276
msgid "run Pdb on exception"
msgstr "запустить Pdb при исключении"

#: cmd/build.py:282
msgid "raise an exception on warnings"
msgstr "возбудить исключение при предупреждении"

#: cmd/build.py:325
msgid "cannot combine -a option and filenames"
msgstr "Невозможно совмещать ключ -a и названия файлов"

#: cmd/build.py:357
#, python-format
msgid "cannot open warning file '%s': %s"
msgstr ""

#: cmd/build.py:376
msgid "-D option argument must be in the form name=value"
msgstr "аргументы с опцией -D должны быть указаны в виде пар название=значение"

#: cmd/build.py:383
msgid "-A option argument must be in the form name=value"
msgstr "аргумент опции -A должен быть указан в виде пары название=значение"

#: themes/classic/layout.html:12 themes/classic/static/sidebar.js.jinja:51
msgid "Collapse sidebar"
msgstr "Свернуть боковую панель"

#: themes/agogo/layout.html:29 themes/basic/globaltoc.html:2
#: themes/basic/localtoc.html:4 themes/scrolls/layout.html:32
msgid "Table of Contents"
msgstr "Оглавление"

#: themes/agogo/layout.html:34 themes/basic/layout.html:130
#: themes/basic/search.html:3 themes/basic/search.html:15
msgid "Search"
msgstr "Поиск"

#: themes/agogo/layout.html:37 themes/basic/searchbox.html:8
#: themes/basic/searchfield.html:12
msgid "Go"
msgstr "Искать"

#: themes/agogo/layout.html:81 themes/basic/sourcelink.html:7
msgid "Show Source"
msgstr "Исходный текст"

#: themes/haiku/layout.html:16
msgid "Contents"
msgstr "Содержание"

#: themes/basic/opensearch.xml:4
#, python-format
msgid "Search %(docstitle)s"
msgstr "Поиск в документе «%(docstitle)s»"

#: themes/basic/defindex.html:4
msgid "Overview"
msgstr "Обзор"

#: themes/basic/defindex.html:8
msgid "Welcome! This is"
msgstr "Добро пожаловать! Это"

#: themes/basic/defindex.html:9
msgid "the documentation for"
msgstr "документация"

#: themes/basic/defindex.html:10
msgid "last updated"
msgstr "последнее изменение"

#: themes/basic/defindex.html:13
msgid "Indices and tables:"
msgstr "Таблицы и указатели:"

#: themes/basic/defindex.html:16
msgid "Complete Table of Contents"
msgstr "Полное оглавление"

#: themes/basic/defindex.html:17
msgid "lists all sections and subsections"
msgstr "список всех разделов и подразделов"

#: domains/std/__init__.py:773 domains/std/__init__.py:786
#: themes/basic/defindex.html:18
msgid "Search Page"
msgstr "Поиск"

#: themes/basic/defindex.html:19
msgid "search this documentation"
msgstr "поиск в документации"

#: themes/basic/defindex.html:21
msgid "Global Module Index"
msgstr "Алфавитный указатель модулей"

#: themes/basic/defindex.html:22
msgid "quick access to all modules"
msgstr "сводный список всех модулей"

#: builders/html/__init__.py:507 themes/basic/defindex.html:23
msgid "General Index"
msgstr "Алфавитный указатель"

#: themes/basic/defindex.html:24
msgid "all functions, classes, terms"
msgstr "все функции, классы, переменные и константы"

#: themes/basic/sourcelink.html:4
msgid "This Page"
msgstr "Эта страница"

#: themes/basic/genindex-single.html:26
#, python-format
msgid "Index &#x2013; %(key)s"
msgstr "Индекс &#x2013; %(key)s"

#: themes/basic/genindex-single.html:54 themes/basic/genindex-split.html:16
#: themes/basic/genindex-split.html:30 themes/basic/genindex.html:65
msgid "Full index on one page"
msgstr "Полный алфавитный указатель на одной странице"

#: themes/basic/searchbox.html:4
msgid "Quick search"
msgstr "Быстрый поиск"

#: themes/basic/genindex-split.html:8
msgid "Index pages by letter"
msgstr "Указатели по буквам алфавита"

#: themes/basic/genindex-split.html:17
msgid "can be huge"
msgstr "может быть очень большим"

#: themes/basic/relations.html:4
msgid "Previous topic"
msgstr "Предыдущий раздел"

#: themes/basic/relations.html:6
msgid "previous chapter"
msgstr "предыдущая глава"

#: themes/basic/relations.html:11
msgid "Next topic"
msgstr "Следующий раздел"

#: themes/basic/relations.html:13
msgid "next chapter"
msgstr "следующая глава"

#: themes/basic/layout.html:18
msgid "Navigation"
msgstr "Навигация"

#: themes/basic/layout.html:115
#, python-format
msgid "Search within %(docstitle)s"
msgstr "Поиск в документе «%(docstitle)s»"

#: themes/basic/layout.html:124
msgid "About these documents"
msgstr "Об этих документах"

#: themes/basic/layout.html:133 themes/basic/layout.html:177
#: themes/basic/layout.html:179
msgid "Copyright"
msgstr "Авторские права"

#: themes/basic/layout.html:183 themes/basic/layout.html:189
#, python-format
msgid "&#169; %(copyright_prefix)s %(copyright)s."
msgstr ""

#: themes/basic/layout.html:201
#, python-format
msgid "Last updated on %(last_updated)s."
msgstr "Обновлено: %(last_updated)s."

#: themes/basic/layout.html:204
#, python-format
msgid ""
"Created using <a href=\"https://www.sphinx-doc.org/\">Sphinx</a> "
"%(sphinx_version)s."
msgstr "Создано с использованием <a href=\"https://www.sphinx-doc.org/\">Sphinx</a> %(sphinx_version)s."

#: themes/basic/search.html:20
msgid ""
"Please activate JavaScript to enable the search\n"
"    functionality."
msgstr "Для работы поиска включите JavaScript в браузере."

#: themes/basic/search.html:28
msgid ""
"Searching for multiple words only shows matches that contain\n"
"    all words."
msgstr "Поиск по нескольким словам отображает только результаты,\n    содержащие все слова."

#: themes/basic/search.html:35
msgid "search"
msgstr "искать"

#: themes/basic/static/sphinx_highlight.js:112
msgid "Hide Search Matches"
msgstr "Снять выделение"

#: themes/basic/static/searchtools.js:117
msgid "Search Results"
msgstr "Результаты поиска"

#: themes/basic/static/searchtools.js:119
msgid ""
"Your search did not match any documents. Please make sure that all words are"
" spelled correctly and that you've selected enough categories."
msgstr "По вашему поиску не найдено ни одного документа. Проверьте, что все слова написаны без ошибок, и что вы выбрали достаточно категорий."

#: themes/basic/static/searchtools.js:123
#, python-brace-format
msgid "Search finished, found one page matching the search query."
msgid_plural ""
"Search finished, found ${resultCount} pages matching the search query."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""
msgstr[3] ""

#: themes/basic/static/searchtools.js:253
msgid "Searching"
msgstr "Идёт поиск"

#: themes/basic/static/searchtools.js:270
msgid "Preparing search..."
msgstr "Подготовка поиска…"

#: themes/basic/static/searchtools.js:474
msgid ", in "
msgstr ", в"

#: themes/basic/changes/rstsource.html:5
#, python-format
msgid "%(filename)s &#8212; %(docstitle)s"
msgstr "%(filename)s &#8212; %(docstitle)s"

#: themes/basic/changes/frameset.html:5
#: themes/basic/changes/versionchanges.html:12
#, python-format
msgid "Changes in Version %(version)s &#8212; %(docstitle)s"
msgstr "Изменения в версии %(version)s &#8212; %(docstitle)s"

#: themes/basic/changes/versionchanges.html:17
#, python-format
msgid "Automatically generated list of changes in version %(version)s"
msgstr "Автоматически созданный список изменений в версии %(version)s"

#: themes/basic/changes/versionchanges.html:18
msgid "Library changes"
msgstr "Изменения в библиотеке"

#: themes/basic/changes/versionchanges.html:23
msgid "C API changes"
msgstr "Изменения в API C"

#: themes/basic/changes/versionchanges.html:25
msgid "Other changes"
msgstr "Другие изменения"

#: themes/classic/static/sidebar.js.jinja:42
msgid "Expand sidebar"
msgstr "Развернуть боковую панель"

#: domains/python/_annotations.py:529
msgid "Positional-only parameter separator (PEP 570)"
msgstr ""

#: domains/python/_annotations.py:540
msgid "Keyword-only parameters separator (PEP 3102)"
msgstr ""

#: domains/python/__init__.py:113 domains/python/__init__.py:278
#, python-format
msgid "%s() (in module %s)"
msgstr "%s() (в модуле %s)"

#: domains/python/__init__.py:180 domains/python/__init__.py:374
#: domains/python/__init__.py:434 domains/python/__init__.py:474
#, python-format
msgid "%s (in module %s)"
msgstr "%s (в модуле %s)"

#: domains/python/__init__.py:182
#, python-format
msgid "%s (built-in variable)"
msgstr "%s (встроенная переменная)"

#: domains/python/__init__.py:217
#, python-format
msgid "%s (built-in class)"
msgstr "%s (встроенный класс)"

#: domains/python/__init__.py:218
#, python-format
msgid "%s (class in %s)"
msgstr "%s (класс в %s)"

#: domains/python/__init__.py:283
#, python-format
msgid "%s() (%s class method)"
msgstr "%s() (метод класса %s)"

#: domains/python/__init__.py:285
#, python-format
msgid "%s() (%s static method)"
msgstr "%s() (статический метод %s)"

#: domains/python/__init__.py:438
#, python-format
msgid "%s (%s property)"
msgstr "%s (свойство %s)"

#: domains/python/__init__.py:478
#, python-format
msgid "%s (type alias in %s)"
msgstr "%s (псевдоним типа в %s)"

#: domains/python/__init__.py:638
msgid "Python Module Index"
msgstr "Содержание модулей Python"

#: domains/python/__init__.py:639
msgid "modules"
msgstr "модули"

#: domains/python/__init__.py:717
msgid "Deprecated"
msgstr "Устарело"

#: domains/python/__init__.py:743
msgid "exception"
msgstr "исключение"

#: domains/python/__init__.py:745
msgid "class method"
msgstr "метод класса"

#: domains/python/__init__.py:746
msgid "static method"
msgstr "статический метод"

#: domains/python/__init__.py:748
msgid "property"
msgstr "свойство"

#: domains/python/__init__.py:749
msgid "type alias"
msgstr "псевдоним типа"

#: domains/python/__init__.py:818
#, python-format
msgid ""
"duplicate object description of %s, other instance in %s, use :no-index: for"
" one of them"
msgstr "повторное описание объекта %s, другой экземпляр в %s, используйте :no-index: для одного из них"

#: domains/python/__init__.py:978
#, python-format
msgid "more than one target found for cross-reference %r: %s"
msgstr "найдено больше одной цели для перекрёстной ссылки %r: %s"

#: domains/python/__init__.py:1052
msgid " (deprecated)"
msgstr "(использование не рекомендуется)"

#: domains/c/__init__.py:326 domains/cpp/__init__.py:483
#: domains/python/_object.py:190 ext/napoleon/docstring.py:974
msgid "Parameters"
msgstr "Параметры"

#: domains/python/_object.py:206
msgid "Variables"
msgstr "Переменные"

#: domains/python/_object.py:214
msgid "Raises"
msgstr "Исключение"

#: domains/cpp/__init__.py:159
msgid "Template Parameters"
msgstr "Параметры шаблона"

#: domains/cpp/__init__.py:302
#, python-format
msgid "%s (C++ %s)"
msgstr "%s (C++ %s)"

#: domains/cpp/__init__.py:392 domains/cpp/_symbol.py:942
#, python-format
msgid ""
"Duplicate C++ declaration, also defined at %s:%s.\n"
"Declaration is '.. cpp:%s:: %s'."
msgstr "Повторная декларация C++, также определено как %s:%s.\nДекларация в '.. cpp:%s:: %s'."

#: domains/c/__init__.py:333 domains/cpp/__init__.py:496
msgid "Return values"
msgstr "Возвращаемые значения"

#: domains/c/__init__.py:754 domains/cpp/__init__.py:940
msgid "union"
msgstr "объединение"

#: domains/c/__init__.py:749 domains/cpp/__init__.py:942
msgid "member"
msgstr "поле"

#: domains/c/__init__.py:757 domains/cpp/__init__.py:943
msgid "type"
msgstr "тип"

#: domains/cpp/__init__.py:944
msgid "concept"
msgstr "концепт"

#: domains/c/__init__.py:755 domains/cpp/__init__.py:945
msgid "enum"
msgstr "перечисляемый тип"

#: domains/c/__init__.py:756 domains/cpp/__init__.py:946
msgid "enumerator"
msgstr "перечислитель"

#: domains/c/__init__.py:760 domains/cpp/__init__.py:949
msgid "function parameter"
msgstr "параметр функции"

#: domains/cpp/__init__.py:952
msgid "template parameter"
msgstr "параметр шаблона"

#: domains/c/__init__.py:211
#, python-format
msgid "%s (C %s)"
msgstr "%s (C %s)"

#: domains/c/__init__.py:277 domains/c/_symbol.py:557
#, python-format
msgid ""
"Duplicate C declaration, also defined at %s:%s.\n"
"Declaration is '.. c:%s:: %s'."
msgstr "Повторная декларация C, также определено как %s:%s.\nДекларация в '.. c:%s:: %s'."

#: domains/c/__init__.py:750
msgid "variable"
msgstr "переменная"

#: domains/c/__init__.py:752
msgid "macro"
msgstr "макрос"

#: domains/c/__init__.py:753
msgid "struct"
msgstr "структура"

#: domains/std/__init__.py:91 domains/std/__init__.py:111
#, python-format
msgid "environment variable; %s"
msgstr "переменная окружения; %s"

#: domains/std/__init__.py:119
#, python-format
msgid "%s; configuration value"
msgstr "%s; значение настройки"

#: domains/std/__init__.py:175
msgid "Type"
msgstr "Тип"

#: domains/std/__init__.py:185
msgid "Default"
msgstr "По умолчанию"

#: domains/std/__init__.py:242
#, python-format
msgid ""
"Malformed option description %r, should look like \"opt\", \"-opt args\", \""
"--opt args\", \"/opt args\" or \"+opt args\""
msgstr "Неправильно сформированное описание параметра %r, должно выглядеть как \"opt\", \"-opt args\", \"--opt args\", \"/opt args\" или \"+opt args\""

#: domains/std/__init__.py:319
#, python-format
msgid "%s command line option"
msgstr "%s опция командной строки"

#: domains/std/__init__.py:321
msgid "command line option"
msgstr "опция командной строки"

#: domains/std/__init__.py:461
msgid "glossary term must be preceded by empty line"
msgstr "перед термином глоссария должна быть пустая строка"

#: domains/std/__init__.py:474
msgid "glossary terms must not be separated by empty lines"
msgstr "термины в глоссарии должны отделяться друг от друга пустой строкой"

#: domains/std/__init__.py:486 domains/std/__init__.py:504
msgid "glossary seems to be misformatted, check indentation"
msgstr "глоссарий, по-видимому, неправильно отформатирован, проверьте отступы"

#: domains/std/__init__.py:729
msgid "glossary term"
msgstr "термин глоссария"

#: domains/std/__init__.py:730
msgid "grammar token"
msgstr "токен грамматики"

#: domains/std/__init__.py:731
msgid "reference label"
msgstr "текст ссылки"

#: domains/std/__init__.py:733
msgid "environment variable"
msgstr "переменная окружения"

#: domains/std/__init__.py:734
msgid "program option"
msgstr "опция программы"

#: domains/std/__init__.py:735
msgid "document"
msgstr "документ"

#: domains/std/__init__.py:772 domains/std/__init__.py:785
msgid "Module Index"
msgstr "Состав модуля"

#: domains/std/__init__.py:857
#, python-format
msgid "duplicate %s description of %s, other instance in %s"
msgstr "duplicate %s description of %s, other instance in %s"

#: domains/std/__init__.py:1113
msgid "numfig is disabled. :numref: is ignored."
msgstr "numfig выключен. :numref: пропускается."

#: domains/std/__init__.py:1124
#, python-format
msgid "Failed to create a cross reference. Any number is not assigned: %s"
msgstr "Ошибка создания перекрёстной ссылки. Какой-либо номер не присвоен: %s"

#: domains/std/__init__.py:1138
#, python-format
msgid "the link has no caption: %s"
msgstr "ссылка без заголовка: %s"

#: domains/std/__init__.py:1153
#, python-format
msgid "invalid numfig_format: %s (%r)"
msgstr "неправильный numfig_format: %s (%r)"

#: domains/std/__init__.py:1157
#, python-format
msgid "invalid numfig_format: %s"
msgstr "неправильный numfig_format: %s"

#: domains/std/__init__.py:1453
#, python-format
msgid "undefined label: %r"
msgstr "неопределённая метка: %r"

#: domains/std/__init__.py:1456
#, python-format
msgid "Failed to create a cross reference. A title or caption not found: %r"
msgstr "Ошибка создания перекрёстной ссылки. Заголовок или подзаголовок не найдены: %r"

#: environment/adapters/toctree.py:324
#, python-format
msgid "circular toctree references detected, ignoring: %s <- %s"
msgstr "обнаружены циклические ссылки в оглавлении, пропускается: %s <- %s"

#: environment/adapters/toctree.py:349
#, python-format
msgid ""
"toctree contains reference to document %r that doesn't have a title: no link"
" will be generated"
msgstr "оглавление содержит ссылку на документ %r, у которого нет заголовка: ссылка не будет сгенерирована"

#: environment/adapters/toctree.py:364
#, python-format
msgid "toctree contains reference to non-included document %r"
msgstr "toctree содержит ссылку на не включенный документ %r"

#: environment/adapters/toctree.py:367
#, python-format
msgid "toctree contains reference to non-existing document %r"
msgstr ""

#: environment/adapters/indexentries.py:123
#, python-format
msgid "see %s"
msgstr "см. %s"

#: environment/adapters/indexentries.py:133
#, python-format
msgid "see also %s"
msgstr "также см. %s"

#: environment/adapters/indexentries.py:141
#, python-format
msgid "unknown index entry type %r"
msgstr "неизвестная запись типа индекса %r"

#: environment/adapters/indexentries.py:268
#: templates/latex/sphinxmessages.sty.jinja:11
msgid "Symbols"
msgstr "Символы"

#: environment/collectors/asset.py:98
#, python-format
msgid "image file not readable: %s"
msgstr "нечитаемое изображение: %s"

#: environment/collectors/asset.py:126
#, python-format
msgid "image file %s not readable: %s"
msgstr "файл изображения %s не читается: %s"

#: environment/collectors/asset.py:163
#, python-format
msgid "download file not readable: %s"
msgstr "загружаемый файл не читается: %s"

#: environment/collectors/toctree.py:259
#, python-format
msgid "%s is already assigned section numbers (nested numbered toctree?)"
msgstr ""

#: _cli/util/errors.py:190
msgid "Interrupted!"
msgstr "Прервано!"

#: _cli/util/errors.py:194
msgid "reStructuredText markup error!"
msgstr ""

#: _cli/util/errors.py:200
msgid "Encoding error!"
msgstr ""

#: _cli/util/errors.py:203
msgid "Recursion error!"
msgstr ""

#: _cli/util/errors.py:207
msgid ""
"This can happen with very large or deeply nested source files. You can "
"carefully increase the default Python recursion limit of 1,000 in conf.py "
"with e.g.:"
msgstr ""

#: _cli/util/errors.py:227
msgid "Starting debugger:"
msgstr ""

#: _cli/util/errors.py:235
msgid "The full traceback has been saved in:"
msgstr "Полный журнал будет сохранен в:"

#: _cli/util/errors.py:240
msgid ""
"To report this error to the developers, please open an issue at "
"<https://github.com/sphinx-doc/sphinx/issues/>. Thanks!"
msgstr "Чтобы сообщить об этой ошибке разработчикам, пожалуйста, откройте запрос в <https://github.com/sphinx-doc/sphinx/issues/>. Спасибо!"

#: _cli/util/errors.py:246
msgid ""
"Please also report this if it was a user error, so that a better error "
"message can be provided next time."
msgstr ""

#: transforms/post_transforms/__init__.py:88
msgid ""
"Could not determine the fallback text for the cross-reference. Might be a "
"bug."
msgstr ""

#: transforms/post_transforms/__init__.py:237
#, python-format
msgid "more than one target found for 'any' cross-reference %r: could be %s"
msgstr "для перекрёстной ссылки 'any' найдено больше одной цели %r: должно быть %s"

#: transforms/post_transforms/__init__.py:299
#, python-format
msgid "%s:%s reference target not found: %s"
msgstr "%s:%s цель перекрёстной ссылки не найдена: %s"

#: transforms/post_transforms/__init__.py:305
#, python-format
msgid "%r reference target not found: %s"
msgstr "%r цель перекрёстной ссылки не найдена: %s"

#: transforms/post_transforms/images.py:79
#, python-format
msgid "Could not fetch remote image: %s [%s]"
msgstr "Не удалось получить изображение из внешнего источника: %s [%s]"

#: transforms/post_transforms/images.py:96
#, python-format
msgid "Could not fetch remote image: %s [%d]"
msgstr "Не удалось получить изображение из внешнего источника: %s [%d]"

#: transforms/post_transforms/images.py:143
#, python-format
msgid "Unknown image format: %s..."
msgstr "Неизвестный формат изображения: %s..."

#: builders/html/__init__.py:113
#, python-format
msgid "The HTML pages are in %(outdir)s."
msgstr "Страницы HTML находятся в %(outdir)s."

#: builders/html/__init__.py:348
#, python-format
msgid "Failed to read build info file: %r"
msgstr "Ошибка чтения при сборке файла info: %r"

#: builders/html/__init__.py:364
msgid "build_info mismatch, copying .buildinfo to .buildinfo.bak"
msgstr "несоответствие build_info, копирование .buildinfo в .buildinfo.bak"

#: builders/html/__init__.py:366
msgid "building [html]: "
msgstr "сборка [html]: "

#: builders/html/__init__.py:383
#, python-format
msgid ""
"template %s has been changed since the previous build, all docs will be "
"rebuilt"
msgstr "шаблон %s был изменён с момента предыдущей сборки, все документы будут пересобраны"

#: builders/html/__init__.py:507
msgid "index"
msgstr "указатель"

#: builders/html/__init__.py:560
#, python-format
msgid "Logo of %s"
msgstr "Логотип %s"

#: builders/html/__init__.py:589
msgid "next"
msgstr "вперёд"

#: builders/html/__init__.py:598
msgid "previous"
msgstr "назад"

#: builders/html/__init__.py:696
msgid "generating indices"
msgstr "генерация индексов"

#: builders/html/__init__.py:711
msgid "writing additional pages"
msgstr "запись дополнительных страниц"

#: builders/html/__init__.py:794
#, python-format
msgid "cannot copy image file '%s': %s"
msgstr "не удалось скопировать файл изображения '%s': %s"

#: builders/html/__init__.py:806
msgid "copying downloadable files... "
msgstr "копирование загружаемых файлов..."

#: builders/html/__init__.py:818
#, python-format
msgid "cannot copy downloadable file %r: %s"
msgstr "не удалось скопировать загружаемый файл %r: %s"

#: builders/html/__init__.py:864
#, python-format
msgid "Failed to copy a file in the theme's 'static' directory: %s: %r"
msgstr "Ошибка копирования файла в каталог 'static' темы: %s: %r"

#: builders/html/__init__.py:882
#, python-format
msgid "Failed to copy a file in html_static_file: %s: %r"
msgstr "Ошибка копирования файла в html_static_file: %s: %r"

#: builders/html/__init__.py:917
msgid "copying static files"
msgstr "копирование файлов статики"

#: builders/html/__init__.py:934
#, python-format
msgid "cannot copy static file %r"
msgstr "не удалось копировать файл статики %r"

#: builders/html/__init__.py:939
msgid "copying extra files"
msgstr "копирование дополнительных файлов"

#: builders/html/__init__.py:949
#, python-format
msgid "cannot copy extra file %r"
msgstr "не удалось скопировать дополнительный файл %r"

#: builders/html/__init__.py:955
#, python-format
msgid "Failed to write build info file: %r"
msgstr "Ошибка записи при сборке файла info: %r"

#: builders/html/__init__.py:1005
msgid ""
"search index couldn't be loaded, but not all documents will be built: the "
"index will be incomplete."
msgstr ""

#: builders/html/__init__.py:1052
#, python-format
msgid "page %s matches two patterns in html_sidebars: %r and %r"
msgstr "страница %s соответствует двум шаблонам в html_sidebars: %r и %r"

#: builders/html/__init__.py:1216
#, python-format
msgid ""
"a Unicode error occurred when rendering the page %s. Please make sure all "
"config values that contain non-ASCII content are Unicode strings."
msgstr ""

#: builders/html/__init__.py:1224
#, python-format
msgid ""
"An error happened in rendering the page %s.\n"
"Reason: %r"
msgstr "Ошибка при рендере страницы %s.\nПричина: %r"

#: builders/html/__init__.py:1257
msgid "dumping object inventory"
msgstr "выгрузка объекта инвентаря"

#: builders/html/__init__.py:1265
#, python-format
msgid "dumping search index in %s"
msgstr ""

#: builders/html/__init__.py:1308
#, python-format
msgid "invalid js_file: %r, ignored"
msgstr "некорректный js_file: %r, пропускается"

#: builders/html/__init__.py:1342
msgid "Many math_renderers are registered. But no math_renderer is selected."
msgstr "Зарегистрировано несколько math_renderers. Однако, ни один math_renderer не выбран."

#: builders/html/__init__.py:1346
#, python-format
msgid "Unknown math_renderer %r is given."
msgstr "Передан неизвестный math_renderer %r ."

#: builders/html/__init__.py:1360
#, python-format
msgid "html_extra_path entry %r is placed inside outdir"
msgstr "элемент html_extra_path %r размещён внутри целевого каталога"

#: builders/html/__init__.py:1365
#, python-format
msgid "html_extra_path entry %r does not exist"
msgstr "элемент html_extra_path %r не существует"

#: builders/html/__init__.py:1380
#, python-format
msgid "html_static_path entry %r is placed inside outdir"
msgstr "элемент html_static_path %r размещён внутри целевого каталога"

#: builders/html/__init__.py:1385
#, python-format
msgid "html_static_path entry %r does not exist"
msgstr "элемент html_static_path %r не существует"

#: builders/html/__init__.py:1396 builders/latex/__init__.py:504
#, python-format
msgid "logo file %r does not exist"
msgstr "файл логотипа %r не существует"

#: builders/html/__init__.py:1407
#, python-format
msgid "favicon file %r does not exist"
msgstr "Файл favicon %r не существует"

#: builders/html/__init__.py:1420
#, python-format
msgid ""
"Values in 'html_sidebars' must be a list of strings. At least one pattern "
"has a string value: %s. Change to `html_sidebars = %r`."
msgstr "Значения в 'html_sidebars' должны быть списком строк. По крайней мере один шаблон имеет строковое значение: %s. Замените на `html_sidebars = %r`."

#: builders/html/__init__.py:1433
msgid ""
"HTML 4 is no longer supported by Sphinx. (\"html4_writer=True\" detected in "
"configuration options)"
msgstr "HTML 4 больше не поддерживается Sphinx. (строка \"html4_writer=True\" обнаружена в настройках)"

#: builders/html/__init__.py:1449
#, python-format
msgid "%s %s documentation"
msgstr "документация %s %s"

#: builders/html/_build_info.py:32
msgid "failed to read broken build info file (unknown version)"
msgstr "ошибка чтения файла сборки info (неизвестная версия)"

#: builders/html/_build_info.py:36
msgid "failed to read broken build info file (missing config entry)"
msgstr "сбой при чтении файла info для сломанной сборки (пропущено значение настройки)"

#: builders/html/_build_info.py:39
msgid "failed to read broken build info file (missing tags entry)"
msgstr "ошибка чтения сломанной сборки файла info (отсутствует запись tags)"

#: builders/latex/__init__.py:118
#, python-format
msgid "The LaTeX files are in %(outdir)s."
msgstr "Файлы LaTeX находятся в %(outdir)s."

#: builders/latex/__init__.py:121
msgid ""
"\n"
"Run 'make' in that directory to run these through (pdf)latex\n"
"(use `make latexpdf' here to do that automatically)."
msgstr "\nЗапустите 'make' в этом каталоге для выполнения с помощью (pdf)latex\n(используйте `make latexpdf' здесь, чтобы сделать это автоматически)."

#: builders/latex/__init__.py:159
msgid "no \"latex_documents\" config value found; no documents will be written"
msgstr "не найдено значение настройки \"latex_documents\"; документы не будут записаны"

#: builders/latex/__init__.py:170
#, python-format
msgid "\"latex_documents\" config value references unknown document %s"
msgstr "значение настройки \"latex_documents\" ссылается на неизвестный документ %s"

#: builders/latex/__init__.py:209 templates/latex/latex.tex.jinja:91
msgid "Release"
msgstr "Выпуск"

#: builders/latex/__init__.py:428
msgid "copying TeX support files"
msgstr "копирование файлов поддержки TeX"

#: builders/latex/__init__.py:465
msgid "copying additional files"
msgstr "копирование дополнительных файлов"

#: builders/latex/__init__.py:536
#, python-format
msgid "Unknown configure key: latex_elements[%r], ignored."
msgstr "Неизвестный ключ конфигурации: latex_elements[%r], пропускается."

#: builders/latex/__init__.py:544
#, python-format
msgid "Unknown theme option: latex_theme_options[%r], ignored."
msgstr "Неизвестная опция темы: latex_theme_options[%r], пропускается."

#: builders/latex/transforms.py:120
msgid "Failed to get a docname!"
msgstr "Ошибка получения названия документа!"

#: builders/latex/transforms.py:121
#, python-format
msgid "Failed to get a docname for source %r!"
msgstr "Сбой получения названия документа для источника %r!"

#: builders/latex/transforms.py:487
#, python-format
msgid "No footnote was found for given reference node %r"
msgstr "Не найдена сноска для указанного узла %r"

#: builders/latex/theming.py:88
#, python-format
msgid "%r doesn't have \"theme\" setting"
msgstr "%r не имеет настройки \"theme\""

#: builders/latex/theming.py:91
#, python-format
msgid "%r doesn't have \"%s\" setting"
msgstr "%r не имеет настройки \"%s\""

#: templates/latex/longtable.tex.jinja:52
#: templates/latex/sphinxmessages.sty.jinja:8
msgid "continued from previous page"
msgstr "продолжение с предыдущей страницы"

#: templates/latex/longtable.tex.jinja:63
#: templates/latex/sphinxmessages.sty.jinja:9
msgid "continues on next page"
msgstr "продолжается на следующей странице"

#: templates/latex/sphinxmessages.sty.jinja:10
msgid "Non-alphabetical"
msgstr "Не-алфавитный"

#: templates/latex/sphinxmessages.sty.jinja:12
msgid "Numbers"
msgstr "Числа"

#: templates/latex/sphinxmessages.sty.jinja:13
msgid "page"
msgstr "страница"

#: ext/napoleon/__init__.py:356 ext/napoleon/docstring.py:940
msgid "Keyword Arguments"
msgstr "Именованные аргументы"

#: ext/napoleon/docstring.py:176
#, python-format
msgid "invalid value set (missing closing brace): %s"
msgstr "задано неправильное значение (пропущена закрывающая скобка): %s"

#: ext/napoleon/docstring.py:183
#, python-format
msgid "invalid value set (missing opening brace): %s"
msgstr "задано неправильное значение (пропущена открывающая скобка): %s"

#: ext/napoleon/docstring.py:190
#, python-format
msgid "malformed string literal (missing closing quote): %s"
msgstr "неправильный строковый литерал (пропущена закрывающая кавычка): %s"

#: ext/napoleon/docstring.py:197
#, python-format
msgid "malformed string literal (missing opening quote): %s"
msgstr "неправильный стороковый литерал (пропущена открывающая кавычка): %s"

#: ext/napoleon/docstring.py:895
msgid "Example"
msgstr "Пример"

#: ext/napoleon/docstring.py:896
msgid "Examples"
msgstr "Примеры"

#: ext/napoleon/docstring.py:956
msgid "Notes"
msgstr "Заметки"

#: ext/napoleon/docstring.py:965
msgid "Other Parameters"
msgstr "Другие параметры"

#: ext/napoleon/docstring.py:1001
msgid "Receives"
msgstr "Получает"

#: ext/napoleon/docstring.py:1005
msgid "References"
msgstr "Ссылки"

#: ext/napoleon/docstring.py:1037
msgid "Warns"
msgstr "Предупрждения"

#: ext/napoleon/docstring.py:1041
msgid "Yields"
msgstr ""

#: ext/autosummary/__init__.py:284
#, python-format
msgid "autosummary references excluded document %r. Ignored."
msgstr "autosummary ссылается на исключённый документ %r. Пропускается."

#: ext/autosummary/__init__.py:288
#, python-format
msgid ""
"autosummary: stub file not found %r. Check your autosummary_generate "
"setting."
msgstr ""

#: ext/autosummary/__init__.py:309
msgid "A captioned autosummary requires :toctree: option. ignored."
msgstr "Озаглавленное autosummary требует опцию :toctree:. пропускается."

#: ext/autosummary/__init__.py:384
#, python-format
msgid ""
"autosummary: failed to import %s.\n"
"Possible hints:\n"
"%s"
msgstr "autosummary: ошибка импорта %s.\nВозможные подсказки:\n%s"

#: ext/autosummary/__init__.py:404
#, python-format
msgid "failed to parse name %s"
msgstr "ошибка разбора имени %s"

#: ext/autosummary/__init__.py:412
#, python-format
msgid "failed to import object %s"
msgstr "ошибка импорта объекта %s"

#: ext/autosummary/__init__.py:730
#, python-format
msgid ""
"Summarised items should not include the current module. Replace %r with %r."
msgstr ""

#: ext/autosummary/__init__.py:927
#, python-format
msgid "autosummary_generate: file not found: %s"
msgstr "autosummary_generate: файл не найден: %s"

#: ext/autosummary/__init__.py:937
msgid ""
"autosummary generates .rst files internally. But your source_suffix does not"
" contain .rst. Skipped."
msgstr "autosummary генерирует внутри файлы .rst . Но ваш source_suffix не содержит .rst. Пропускается."

#: ext/autosummary/generate.py:232 ext/autosummary/generate.py:450
#, python-format
msgid ""
"autosummary: failed to determine %r to be documented, the following exception was raised:\n"
"%s"
msgstr ""

#: ext/autosummary/generate.py:588
#, python-format
msgid "[autosummary] generating autosummary for: %s"
msgstr "[autosummary] генерация autosummary для: %s"

#: ext/autosummary/generate.py:592
#, python-format
msgid "[autosummary] writing to %s"
msgstr "[autosummary] запись в %s"

#: ext/autosummary/generate.py:637
#, python-format
msgid ""
"[autosummary] failed to import %s.\n"
"Possible hints:\n"
"%s"
msgstr "[autosummary] ошибка импорта %s.\nВозможные подсказки:\n%s"

#: ext/autosummary/generate.py:836
msgid ""
"\n"
"Generate ReStructuredText using autosummary directives.\n"
"\n"
"sphinx-autogen is a frontend to sphinx.ext.autosummary.generate. It generates\n"
"the reStructuredText files from the autosummary directives contained in the\n"
"given input files.\n"
"\n"
"The format of the autosummary directive is documented in the\n"
"``sphinx.ext.autosummary`` Python module and can be read using::\n"
"\n"
"  pydoc sphinx.ext.autosummary\n"
msgstr ""

#: ext/autosummary/generate.py:858
msgid "source files to generate rST files for"
msgstr "исходные файлы для генерации файлов rST для"

#: ext/autosummary/generate.py:866
msgid "directory to place all output in"
msgstr "каталог для размещения всех файлов вывода"

#: ext/autosummary/generate.py:874
#, python-format
msgid "default suffix for files (default: %(default)s)"
msgstr "расширение файлов по умолчанию (по умолчанию: %(default)s)"

#: ext/autosummary/generate.py:882
#, python-format
msgid "custom template directory (default: %(default)s)"
msgstr "собственный каталог шаблонов (по умолчанию: %(default)s)"

#: ext/autosummary/generate.py:890
#, python-format
msgid "document imported members (default: %(default)s)"
msgstr ""

#: ext/autosummary/generate.py:899
#, python-format
msgid ""
"document exactly the members in module __all__ attribute. (default: "
"%(default)s)"
msgstr ""

#: ext/apidoc/_cli.py:178 ext/autosummary/generate.py:909
msgid "Remove existing files in the output directory that were not generated"
msgstr "Удалить из целевого каталога файлы, которые не были сгенерированы"

#: ext/apidoc/_shared.py:29 ext/autosummary/generate.py:944
#, python-format
msgid "Failed to remove %s: %s"
msgstr "Ошибка удаления %s: %s"

#: ext/apidoc/_cli.py:28
msgid ""
"\n"
"Look recursively in <MODULE_PATH> for Python modules and packages and create\n"
"one reST file with automodule directives per package in the <OUTPUT_PATH>.\n"
"\n"
"The <EXCLUDE_PATTERN>s can be file and/or directory patterns that will be\n"
"excluded from generation.\n"
"\n"
"Note: By default this script will not overwrite already created files."
msgstr ""

#: ext/apidoc/_cli.py:45
msgid "path to module to document"
msgstr "путь к модулю в документе"

#: ext/apidoc/_cli.py:50
msgid ""
"fnmatch-style file and/or directory patterns to exclude from generation"
msgstr "стиль fnmatch для шаблонов файлов и/или каталогов для исключения из генерации"

#: ext/apidoc/_cli.py:60
msgid "directory to place all output"
msgstr "каталог для размещения всего вывода"

#: ext/apidoc/_cli.py:75
msgid "maximum depth of submodules to show in the TOC (default: 4)"
msgstr "максимальная глубина субмодулей для отображения в оглавлении (по умолчанию: 4)"

#: ext/apidoc/_cli.py:82
msgid "overwrite existing files"
msgstr "перезаписать существующие файлы"

#: ext/apidoc/_cli.py:91
msgid ""
"follow symbolic links. Powerful when combined with "
"collective.recipe.omelette."
msgstr "следовать по символическим ссылкам. Эффективно сочетается с collective.recipe.omelette."

#: ext/apidoc/_cli.py:99
msgid "run the script without creating files"
msgstr "запуск сценария без создания файлов"

#: ext/apidoc/_cli.py:106
msgid "put documentation for each module on its own page"
msgstr "разместить документацию каждого модуля на его собственной странице"

#: ext/apidoc/_cli.py:113
msgid "include \"_private\" modules"
msgstr "включая модули \"_private\""

#: ext/apidoc/_cli.py:120
msgid "filename of table of contents (default: modules)"
msgstr "имя файла с оглавлением (по умолчанию: modules)"

#: ext/apidoc/_cli.py:127
msgid "don't create a table of contents file"
msgstr "не создавать файл с оглавлением"

#: ext/apidoc/_cli.py:135
msgid ""
"don't create headings for the module/package packages (e.g. when the "
"docstrings already contain them)"
msgstr "не создавать заголовки для модулей/пакета пакетов (например, если docstrings уже содержат их)"

#: ext/apidoc/_cli.py:145
msgid "put module documentation before submodule documentation"
msgstr "разместить документацию модуля перед документацией подмодуля"

#: ext/apidoc/_cli.py:152
msgid ""
"interpret module paths according to PEP-0420 implicit namespaces "
"specification"
msgstr ""

#: ext/apidoc/_cli.py:160
msgid ""
"Comma-separated list of options to pass to automodule directive (or use "
"SPHINX_APIDOC_OPTIONS)."
msgstr ""

#: ext/apidoc/_cli.py:170
msgid "file suffix (default: rst)"
msgstr "расширение файла (по умолчанию: rst)"

#: ext/apidoc/_cli.py:186
msgid "generate a full project with sphinx-quickstart"
msgstr "генерация полного проекта с помощью sphinx-quickstart"

#: ext/apidoc/_cli.py:193
msgid "append module_path to sys.path, used when --full is given"
msgstr "добавить module_path в sys.path, используется совместно с --full"

#: ext/apidoc/_cli.py:200
msgid "project name (default: root module name)"
msgstr "название проекта (по умолчанию: название корневого модуля)"

#: ext/apidoc/_cli.py:207
msgid "project author(s), used when --full is given"
msgstr "автор(ы) проекта, используется совместно с --full"

#: ext/apidoc/_cli.py:214
msgid "project version, used when --full is given"
msgstr "версия проекта, используется совместно с --full"

#: ext/apidoc/_cli.py:222
msgid "project release, used when --full is given, defaults to --doc-version"
msgstr "релиз проекта, используется совместно с --full, по умолчанию --doc-version"

#: ext/apidoc/_cli.py:226
msgid "extension options"
msgstr "опции расширения"

#: ext/apidoc/_cli.py:232
msgid "enable arbitrary extensions, used when --full is given"
msgstr ""

#: ext/apidoc/_cli.py:240
#, python-format
msgid "enable %s extension, used when --full is given"
msgstr ""

#: ext/apidoc/_cli.py:291
#, python-format
msgid "%s is not a directory."
msgstr "%s не каталог."

#: ext/apidoc/_extension.py:50
msgid "Running apidoc"
msgstr ""

#: ext/apidoc/_extension.py:102
#, python-format
msgid "apidoc_modules item %i must be a dict"
msgstr ""

#: ext/apidoc/_extension.py:110
#, python-format
msgid "apidoc_modules item %i must have a 'path' key"
msgstr ""

#: ext/apidoc/_extension.py:115
#, python-format
msgid "apidoc_modules item %i 'path' must be a string"
msgstr ""

#: ext/apidoc/_extension.py:121
#, python-format
msgid "apidoc_modules item %i 'path' is not an existing folder: %s"
msgstr ""

#: ext/apidoc/_extension.py:133
#, python-format
msgid "apidoc_modules item %i must have a 'destination' key"
msgstr ""

#: ext/apidoc/_extension.py:140
#, python-format
msgid "apidoc_modules item %i 'destination' must be a string"
msgstr ""

#: ext/apidoc/_extension.py:147
#, python-format
msgid "apidoc_modules item %i 'destination' should be a relative path"
msgstr ""

#: ext/apidoc/_extension.py:157
#, python-format
msgid "apidoc_modules item %i cannot create destination directory: %s"
msgstr ""

#: ext/apidoc/_extension.py:178
#, python-format
msgid "apidoc_modules item %i '%s' must be an int"
msgstr ""

#: ext/apidoc/_extension.py:192
#, python-format
msgid "apidoc_modules item %i '%s' must be a boolean"
msgstr ""

#: ext/apidoc/_extension.py:210
#, python-format
msgid "apidoc_modules item %i has unexpected keys: %s"
msgstr ""

#: ext/apidoc/_extension.py:247
#, python-format
msgid "apidoc_modules item %i '%s' must be a sequence"
msgstr ""

#: ext/apidoc/_extension.py:256
#, python-format
msgid "apidoc_modules item %i '%s' must contain strings"
msgstr ""

#: ext/apidoc/_generate.py:69
#, python-format
msgid "Would create file %s."
msgstr "Нужно создать файл %s."

#: ext/intersphinx/_resolve.py:49
#, python-format
msgid "(in %s v%s)"
msgstr "(в %s v%s)"

#: ext/intersphinx/_resolve.py:51
#, python-format
msgid "(in %s)"
msgstr "(в %s)"

#: ext/intersphinx/_resolve.py:108
#, python-format
msgid "inventory '%s': duplicate matches found for %s:%s"
msgstr "инвентарь '%s': найден повтор для %s:%s"

#: ext/intersphinx/_resolve.py:118
#, python-format
msgid "inventory '%s': multiple matches found for %s:%s"
msgstr "инвентарь '%s': найдено несколько совпадений для %s:%s"

#: ext/intersphinx/_resolve.py:383
#, python-format
msgid "inventory for external cross-reference not found: %r"
msgstr "инвентарь для внешних перекрёстных ссылок не найден: %r"

#: ext/intersphinx/_resolve.py:392
#, python-format
msgid "invalid external cross-reference suffix: %r"
msgstr "неправильный суффикс перекрёстной ссылки: %r"

#: ext/intersphinx/_resolve.py:403
#, python-format
msgid "domain for external cross-reference not found: %r"
msgstr "домен для внешней перекрёстной ссылки не найден: %r"

#: ext/intersphinx/_resolve.py:619
#, python-format
msgid "external %s:%s reference target not found: %s"
msgstr ""

#: ext/intersphinx/_load.py:60
#, python-format
msgid ""
"Invalid intersphinx project identifier `%r` in intersphinx_mapping. Project "
"identifiers must be non-empty strings."
msgstr "Неправильный идентификатор проекта intersphinx  `%r` d intersphinx_mapping. Идентификаторы проектов должны быть непустыми строками."

#: ext/intersphinx/_load.py:71
#, python-format
msgid ""
"Invalid value `%r` in intersphinx_mapping[%r]. Expected a two-element tuple "
"or list."
msgstr "Неправильное значение `%r` в intersphinx_mapping[%r]. Ожидается кортеж или список из двух элементов."

#: ext/intersphinx/_load.py:82
#, python-format
msgid ""
"Invalid value `%r` in intersphinx_mapping[%r]. Values must be a (target URI,"
" inventory locations) pair."
msgstr "Неправильное значение `%r` в intersphinx_mapping[%r]. Значения должны быть парами (целевой URI, расположение инвентаря)."

#: ext/intersphinx/_load.py:93
#, python-format
msgid ""
"Invalid target URI value `%r` in intersphinx_mapping[%r][0]. Target URIs "
"must be unique non-empty strings."
msgstr "Некорректное значение целевого URL `%r` в intersphinx_mapping[%r][0]. Целевые URI должны быть уникальными не пустыми строками."

#: ext/intersphinx/_load.py:102
#, python-format
msgid ""
"Invalid target URI value `%r` in intersphinx_mapping[%r][0]. Target URIs "
"must be unique (other instance in intersphinx_mapping[%r])."
msgstr "Неправильный целевой URI `%r` в intersphinx_mapping[%r][0]. Целевые URI должны быть уникальными (другой экземпляр в intersphinx_mapping[%r])."

#: ext/intersphinx/_load.py:121
#, python-format
msgid ""
"Invalid inventory location value `%r` in intersphinx_mapping[%r][1]. "
"Inventory locations must be non-empty strings or None."
msgstr ""

#: ext/intersphinx/_load.py:131
msgid "Invalid `intersphinx_mapping` configuration (1 error)."
msgstr "Некорректная настройка `intersphinx_mapping` (1 ошибка)."

#: ext/intersphinx/_load.py:134
#, python-format
msgid "Invalid `intersphinx_mapping` configuration (%s errors)."
msgstr "Некорректная конфигурация `intersphinx_mapping` (%s ошибок)."

#: ext/intersphinx/_load.py:157
msgid "An invalid intersphinx_mapping entry was added after normalisation."
msgstr "После нормализации была добьавлена некорректная запись intersphinx_mapping."

#: ext/intersphinx/_load.py:261
#, python-format
msgid "loading intersphinx inventory '%s' from %s ..."
msgstr "загрузка инвентаря intersphinx '%s' из %s ..."

#: ext/intersphinx/_load.py:287
msgid ""
"encountered some issues with some of the inventories, but they had working "
"alternatives:"
msgstr ""

#: ext/intersphinx/_load.py:297
msgid "failed to reach any of the inventories with the following issues:"
msgstr "не удалось получить доступ ни к одному из инвентарей по следующим причинам:"

#: ext/intersphinx/_load.py:361
#, python-format
msgid "intersphinx inventory has moved: %s -> %s"
msgstr "инвентарь intersphinx перемещён: %s -> %s"

#: ext/autodoc/__init__.py:150
#, python-format
msgid "invalid value for member-order option: %s"
msgstr "некорректное значение для опции member-order: %s"

#: ext/autodoc/__init__.py:158
#, python-format
msgid "invalid value for class-doc-from option: %s"
msgstr "некорректное значение для опции for class-doc-from: %s"

#: ext/autodoc/__init__.py:460
#, python-format
msgid "invalid signature for auto%s (%r)"
msgstr "неправильная сигнатура для auto%s (%r)"

#: ext/autodoc/__init__.py:579
#, python-format
msgid "error while formatting arguments for %s: %s"
msgstr "ошибка форматирования аргументов для %s: %s"

#: ext/autodoc/__init__.py:898
#, python-format
msgid ""
"autodoc: failed to determine %s.%s (%r) to be documented, the following exception was raised:\n"
"%s"
msgstr ""

#: ext/autodoc/__init__.py:1021
#, python-format
msgid ""
"don't know which module to import for autodocumenting %r (try placing a "
"\"module\" or \"currentmodule\" directive in the document, or giving an "
"explicit module name)"
msgstr ""

#: ext/autodoc/__init__.py:1080
#, python-format
msgid "A mocked object is detected: %r"
msgstr ""

#: ext/autodoc/__init__.py:1103
#, python-format
msgid "error while formatting signature for %s: %s"
msgstr "ошибка при форматировании сигнатуры для %s: %s"

#: ext/autodoc/__init__.py:1177
msgid "\"::\" in automodule name doesn't make sense"
msgstr "\"::\" в названии автомодуля не имеет смысла"

#: ext/autodoc/__init__.py:1185
#, python-format
msgid "signature arguments or return annotation given for automodule %s"
msgstr ""

#: ext/autodoc/__init__.py:1201
#, python-format
msgid ""
"__all__ should be a list of strings, not %r (in module %s) -- ignoring "
"__all__"
msgstr ""

#: ext/autodoc/__init__.py:1278
#, python-format
msgid ""
"missing attribute mentioned in :members: option: module %s, attribute %s"
msgstr "пропущен атрибут упомянутый в опции :members:: модуль %s, атрибут %s"

#: ext/autodoc/__init__.py:1505 ext/autodoc/__init__.py:1593
#: ext/autodoc/__init__.py:3127
#, python-format
msgid "Failed to get a function signature for %s: %s"
msgstr "Ошибка получения сигнатуры функции для %s: %s"

#: ext/autodoc/__init__.py:1828
#, python-format
msgid "Failed to get a constructor signature for %s: %s"
msgstr "Ошибка получения сигнатуры конструктора для %s: %s"

#: ext/autodoc/__init__.py:1966
#, python-format
msgid "Bases: %s"
msgstr "Базовые классы: %s"

#: ext/autodoc/__init__.py:1985
#, python-format
msgid "missing attribute %s in object %s"
msgstr "не указан атрибут %s в объекте %s"

#: ext/autodoc/__init__.py:2081 ext/autodoc/__init__.py:2110
#: ext/autodoc/__init__.py:2204
#, python-format
msgid "alias of %s"
msgstr "псевдоним для %s"

#: ext/autodoc/__init__.py:2097
#, python-format
msgid "alias of TypeVar(%s)"
msgstr "псевдоним для TypeVar(%s)"

#: ext/autodoc/__init__.py:2456 ext/autodoc/__init__.py:2576
#, python-format
msgid "Failed to get a method signature for %s: %s"
msgstr "Ошибка получения сигнатуры метода для %s: %s"

#: ext/autodoc/__init__.py:2720
#, python-format
msgid "Invalid __slots__ found on %s. Ignored."
msgstr "Некорректные __slots__ найдены в %s. Пропускается."

#: ext/autodoc/preserve_defaults.py:195
#, python-format
msgid "Failed to parse a default argument value for %r: %s"
msgstr "Ошибка разбора значения по умолчанию для аргумента %r: %s"

#: ext/autodoc/type_comment.py:151
#, python-format
msgid "Failed to update signature for %r: parameter not found: %s"
msgstr "Ошибка обновления сигнатуры для %r: параметр не найден: %s"

#: ext/autodoc/type_comment.py:154
#, python-format
msgid "Failed to parse type_comment for %r: %s"
msgstr "Ошибка разбора type_comment для %r: %s"

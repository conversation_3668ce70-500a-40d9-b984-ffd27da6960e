#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
打包脚本，用于将剪切机自动化控制系统打包为可执行文件
"""

import os
import sys
import shutil
import subprocess

def build_exe():
    """打包为可执行文件"""
    print("开始打包剪切机自动化控制系统...")

    # 确保所有依赖已安装
    print("检查并安装依赖...")
    dependencies = [
        "pyinstaller",
        "PyQt5",
        "pyserial",
        "pandas",
        "openpyxl",
        "xlrd>=2.0.1"
    ]

    for dep in dependencies:
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", dep], check=True)
            print(f"已安装/更新 {dep}")
        except subprocess.CalledProcessError:
            print(f"安装 {dep} 失败，请手动安装后重试")
            return

    # 创建必要的目录
    if not os.path.exists("logs"):
        os.makedirs("logs")

    # 确保配置文件存在
    if not os.path.exists("config.json"):
        with open("config.json", "w", encoding="utf-8") as f:
            f.write('''{
    "port": "COM1",
    "baudrate": 38400,
    "bytesize": 8,
    "parity": "N",
    "stopbits": 1.0,
    "timeout": 0.5,
    "register_map": {
        "length": 42092,
        "increment": 42094,
        "speed": 42088,
        "error_adjust": 1004,
        "roots": 42098,
        "machines": 42104,
        "current_length": 3000,
        "completed_count": 42100,
        "machine_status": 3002
    }
}''')

    # 确保任务文件存在
    if not os.path.exists("tasks.json"):
        with open("tasks.json", "w", encoding="utf-8") as f:
            f.write("[]")

    # 使用PyInstaller打包
    print("开始使用PyInstaller打包...")

    # 强制重新创建图标文件
    try:
        print("正在创建图标文件...")
        from create_icon import create_icon
        icon_path = create_icon()
        print(f"图标已创建: {icon_path}")
    except Exception as e:
        print(f"创建图标失败: {e}")
        print("将使用默认图标")
        icon_path = ""

    # 确保图标文件存在
    if os.path.exists("icon.ico"):
        # 检查图标文件大小，确保不是空文件
        icon_size = os.path.getsize("icon.ico")
        print(f"图标文件大小: {icon_size} 字节")
        if icon_size < 100:  # 如果文件太小，可能是损坏的
            print("图标文件可能损坏，尝试重新创建...")
            try:
                from create_icon import create_icon
                icon_path = create_icon()
                print(f"图标已重新创建: {icon_path}")
            except Exception as e:
                print(f"重新创建图标失败: {e}")

    # 定义打包命令 - 使用spec文件
    cmd = [
        "pyinstaller",
        "--clean",  # 清理临时文件
        "--noconfirm",  # 不询问确认
        "剪切机自动化控制系统.spec"  # 使用spec文件
    ]

    # 过滤掉空字符串
    cmd = [item for item in cmd if item]

    try:
        subprocess.run(cmd, check=True)
        print("PyInstaller打包完成")

        # 复制必要的文件到dist目录
        dist_dir = os.path.join("dist", "剪切机自动化控制系统")

        # 创建logs目录
        logs_dir = os.path.join(dist_dir, "logs")
        if not os.path.exists(logs_dir):
            os.makedirs(logs_dir)

        # 确保图标文件被正确复制
        if os.path.exists("icon.ico"):
            icon_dest = os.path.join(dist_dir, "icon.ico")
            try:
                shutil.copy2("icon.ico", icon_dest)
                print(f"图标文件已复制到: {icon_dest}")
            except Exception as e:
                print(f"复制图标文件失败: {e}")

        print(f"打包完成，可执行文件位于: {dist_dir}")
        print("您可以将整个目录复制到其他电脑上运行。")

        # 创建启动脚本
        with open(os.path.join(dist_dir, "启动剪切机自动化控制系统.bat"), "w") as f:
            f.write("@echo off\n")
            f.write("echo 正在启动剪切机自动化控制系统...\n")
            f.write("start 剪切机自动化控制系统.exe\n")

        return True
    except subprocess.CalledProcessError as e:
        print(f"打包失败: {e}")
        return False

if __name__ == "__main__":
    build_exe()

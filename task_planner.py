#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
任务计划管理模块
用于生成和管理剪切任务计划
"""

import os
import json
import time
import logging
from logging.handlers import TimedRotatingFileHandler

# 设置日志
logger = logging.getLogger("TaskPlanner")
if not logger.handlers:
    log_dir = os.path.join(os.getcwd(), "logs")
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    handler = TimedRotatingFileHandler(
        os.path.join(log_dir, "task_planner.log"),
        when="midnight",
        backupCount=7,
        encoding="utf-8"
    )
    formatter = logging.Formatter('%(asctime)s %(levelname)s: %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)

class TaskPlanner:
    """任务计划管理类"""
    def __init__(self):
        self.tasks = []  # 存储所有任务计划
        self.current_task_index = -1  # 当前选中的任务索引
        self.task_file = "tasks.json"  # 任务保存文件

        # 尝试加载已保存的任务
        self.load_tasks()

    def create_task_from_order(self, order):
        """从排产单记录创建任务"""
        try:
            # 提取参数
            length = order['parameters'].get('length')
            increment = order['parameters'].get('increment')
            roots = order['parameters'].get('roots')  # 提取根数
            copper_spec = order['parameters'].get('copper_spec')  # 提取铜带规格

            # 创建任务
            task = {
                'code': order['code'],
                'contract': order['contract'],
                'process': order['process'],  # 保存完整做法
                'model': order.get('model', ''),  # 添加型号字段，如果不存在则使用空字符串
                'copper_spec': copper_spec if copper_spec is not None else '',  # 添加铜带规格字段
                'total_quantity': order['quantity'],
                'remaining_quantity': order['remaining'],
                'completed_quantity': 0,
                'length': length if length is not None else 0,
                'increment': increment if increment is not None else 0,
                'roots': roots if roots is not None else 0,  # 添加根数字段
                'speed': 80,  # 默认速度80%
                'error_adjust': 0,  # 默认误差调整0
                'created_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                'last_updated': time.strftime('%Y-%m-%d %H:%M:%S'),
                'status': '未开始'  # 状态：未开始、进行中、已完成
            }

            self.tasks.append(task)
            logger.info(f"已创建任务: {task['code']} - {task['contract']}")

            # 保存任务
            self.save_tasks()

            return len(self.tasks) - 1  # 返回新任务的索引
        except Exception as e:
            logger.error(f"创建任务时发生错误: {str(e)}")
            return -1

    def update_task_parameters(self, index, length=None, increment=None, roots=None, speed=None, error_adjust=None):
        """更新任务参数"""
        if 0 <= index < len(self.tasks):
            if length is not None:
                self.tasks[index]['length'] = length
            if increment is not None:
                self.tasks[index]['increment'] = increment
            if roots is not None:
                self.tasks[index]['roots'] = roots  # 添加根数更新
            if speed is not None:
                self.tasks[index]['speed'] = speed
            if error_adjust is not None:
                self.tasks[index]['error_adjust'] = error_adjust

            self.tasks[index]['last_updated'] = time.strftime('%Y-%m-%d %H:%M:%S')
            self.save_tasks()
            return True
        return False

    def update_task_quantity(self, index, quantity):
        """更新任务总数量"""
        if 0 <= index < len(self.tasks):
            self.tasks[index]['total_quantity'] = quantity
            self.tasks[index]['remaining_quantity'] = quantity - self.tasks[index]['completed_quantity']
            self.tasks[index]['last_updated'] = time.strftime('%Y-%m-%d %H:%M:%S')

            # 更新状态
            if self.tasks[index]['remaining_quantity'] <= 0:
                self.tasks[index]['status'] = '已完成'
            elif self.tasks[index]['completed_quantity'] > 0:
                self.tasks[index]['status'] = '进行中'

            self.save_tasks()
            return True
        return False

    def update_completed_quantity(self, index, completed):
        """更新已完成数量"""
        if 0 <= index < len(self.tasks):
            self.tasks[index]['completed_quantity'] += completed
            self.tasks[index]['remaining_quantity'] = max(0, self.tasks[index]['total_quantity'] - self.tasks[index]['completed_quantity'])
            self.tasks[index]['last_updated'] = time.strftime('%Y-%m-%d %H:%M:%S')

            # 更新状态
            if self.tasks[index]['remaining_quantity'] <= 0:
                self.tasks[index]['status'] = '已完成'
            else:
                self.tasks[index]['status'] = '进行中'

            self.save_tasks()
            return self.tasks[index]['remaining_quantity']
        return None

    def get_task_by_index(self, index):
        """根据索引获取任务"""
        if 0 <= index < len(self.tasks):
            return self.tasks[index]
        return None

    def get_all_tasks(self):
        """获取所有任务"""
        return self.tasks

    def delete_task(self, index):
        """删除任务"""
        if 0 <= index < len(self.tasks):
            deleted = self.tasks.pop(index)
            self.save_tasks()
            logger.info(f"已删除任务: {deleted['code']} - {deleted['contract']}")
            return True
        return False

    def save_tasks(self):
        """保存任务到文件"""
        try:
            with open(self.task_file, 'w', encoding='utf-8') as f:
                json.dump(self.tasks, f, ensure_ascii=False, indent=4)
            logger.info(f"已保存 {len(self.tasks)} 个任务到 {self.task_file}")
            return True
        except Exception as e:
            logger.error(f"保存任务时发生错误: {str(e)}")
            return False

    def load_tasks(self):
        """从文件加载任务"""
        try:
            if os.path.exists(self.task_file):
                with open(self.task_file, 'r', encoding='utf-8') as f:
                    self.tasks = json.load(f)
                logger.info(f"已从 {self.task_file} 加载 {len(self.tasks)} 个任务")
                return True
        except Exception as e:
            logger.error(f"加载任务时发生错误: {str(e)}")
            self.tasks = []
        return False

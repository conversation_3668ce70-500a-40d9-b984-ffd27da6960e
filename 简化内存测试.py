#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的内存泄漏测试程序
快速验证内存管理修复效果
"""

import sys
import time
import psutil
import os
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

class SimpleMemoryTest(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("简化内存泄漏测试")
        self.setGeometry(100, 100, 600, 400)
        
        # 获取当前进程
        self.process = psutil.Process(os.getpid())
        self.initial_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        
        # 测试参数
        self.test_count = 0
        self.max_test_count = 100  # 只测试100次，快速完成
        self.test_running = False
        
        self.init_ui()
        
        # 监控定时器
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self.update_memory_info)
        self.monitor_timer.start(500)  # 每0.5秒更新一次
        
        # 测试定时器
        self.test_timer = QTimer()
        self.test_timer.timeout.connect(self.run_memory_test)
        
    def init_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title = QLabel("简化内存泄漏测试")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 16px; font-weight: bold; padding: 10px;")
        layout.addWidget(title)
        
        # 内存信息
        info_group = QGroupBox("内存使用情况")
        info_layout = QGridLayout()
        
        info_layout.addWidget(QLabel("当前内存:"), 0, 0)
        self.current_memory_label = QLabel("0 MB")
        self.current_memory_label.setStyleSheet("font-weight: bold; color: #2ecc71;")
        info_layout.addWidget(self.current_memory_label, 0, 1)
        
        info_layout.addWidget(QLabel("内存增长:"), 1, 0)
        self.memory_growth_label = QLabel("0 MB")
        self.memory_growth_label.setStyleSheet("font-weight: bold; color: #e74c3c;")
        info_layout.addWidget(self.memory_growth_label, 1, 1)
        
        info_group.setLayout(info_layout)
        layout.addWidget(info_group)
        
        # 测试控制
        control_layout = QHBoxLayout()
        
        self.start_btn = QPushButton("开始测试")
        self.start_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        self.start_btn.clicked.connect(self.start_test)
        control_layout.addWidget(self.start_btn)
        
        self.stop_btn = QPushButton("停止测试")
        self.stop_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        self.stop_btn.clicked.connect(self.stop_test)
        self.stop_btn.setEnabled(False)
        control_layout.addWidget(self.stop_btn)
        
        layout.addLayout(control_layout)
        
        # 进度显示
        progress_group = QGroupBox("测试进度")
        progress_layout = QVBoxLayout()
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        progress_layout.addWidget(self.progress_bar)
        
        self.status_label = QLabel("就绪")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("font-size: 14px; padding: 10px;")
        progress_layout.addWidget(self.status_label)
        
        progress_group.setLayout(progress_layout)
        layout.addWidget(progress_group)
        
        # 结果显示
        result_group = QGroupBox("测试结果")
        result_layout = QVBoxLayout()
        
        self.result_text = QTextEdit()
        self.result_text.setMaximumHeight(150)
        self.result_text.setStyleSheet("""
            QTextEdit {
                border: 1px solid #ddd;
                border-radius: 5px;
                padding: 5px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 10px;
                background-color: #f8f9fa;
            }
        """)
        result_layout.addWidget(self.result_text)
        
        result_group.setLayout(result_layout)
        layout.addWidget(result_group)
        
    def add_result(self, message):
        """添加结果消息"""
        timestamp = time.strftime("%H:%M:%S")
        result_message = f"[{timestamp}] {message}"
        self.result_text.append(result_message)
        
        # 自动滚动到底部
        cursor = self.result_text.textCursor()
        cursor.movePosition(cursor.End)
        self.result_text.setTextCursor(cursor)
        
        print(result_message)  # 同时输出到控制台
    
    def update_memory_info(self):
        """更新内存信息"""
        try:
            memory_info = self.process.memory_info()
            current_memory = memory_info.rss / 1024 / 1024  # MB
            memory_growth = current_memory - self.initial_memory
            
            self.current_memory_label.setText(f"{current_memory:.1f} MB")
            self.memory_growth_label.setText(f"{memory_growth:+.1f} MB")
            
            # 根据内存增长设置颜色
            if memory_growth > 20:  # 超过20MB增长显示红色
                self.memory_growth_label.setStyleSheet("font-weight: bold; color: #e74c3c;")
            elif memory_growth > 10:  # 超过10MB增长显示橙色
                self.memory_growth_label.setStyleSheet("font-weight: bold; color: #f39c12;")
            else:
                self.memory_growth_label.setStyleSheet("font-weight: bold; color: #27ae60;")
                
        except Exception as e:
            print(f"更新内存信息时发生错误: {e}")
    
    def start_test(self):
        """开始测试"""
        self.add_result(f"开始内存泄漏测试，将进行 {self.max_test_count} 次操作")
        
        self.test_count = 0
        self.test_running = True
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.progress_bar.setVisible(True)
        self.progress_bar.setMaximum(self.max_test_count)
        self.progress_bar.setValue(0)
        
        # 启动测试定时器，每100ms执行一次
        self.test_timer.start(100)
        
        # 立即执行一次测试
        self.run_memory_test()
    
    def stop_test(self):
        """停止测试"""
        self.test_running = False
        self.test_timer.stop()
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.progress_bar.setVisible(False)
        self.status_label.setText("测试已停止")
        self.add_result("测试已停止")
    
    def run_memory_test(self):
        """运行内存测试"""
        if not self.test_running:
            return
        
        try:
            # 模拟创建大量数据（类似于通信数据累积）
            test_data = []
            for i in range(50):  # 减少数据量，加快测试速度
                test_data.append({
                    'time': time.strftime('%H:%M:%S'),
                    'data': f"test_data_{self.test_count}_{i}" * 5,
                    'type': 'memory_test'
                })
            
            # 模拟数据清理（测试我们的修复）
            if len(test_data) > 25:
                test_data = test_data[-25:]  # 只保留最新的25条
            
            # 模拟定时器创建和销毁
            temp_timer = QTimer()
            temp_timer.timeout.connect(lambda: None)
            temp_timer.start(1000)
            temp_timer.stop()
            temp_timer.deleteLater()
            
            # 更新计数器和进度
            self.test_count += 1
            self.progress_bar.setValue(self.test_count)
            
            # 更新状态
            progress_percent = int((self.test_count / self.max_test_count) * 100)
            self.status_label.setText(f"测试进行中... {self.test_count}/{self.max_test_count} ({progress_percent}%)")
            
            # 每20次输出一次进度
            if self.test_count % 20 == 0:
                self.add_result(f"已完成 {self.test_count}/{self.max_test_count} 次操作")
            
            # 检查是否完成
            if self.test_count >= self.max_test_count:
                self.complete_test()
                
        except Exception as e:
            self.add_result(f"测试过程中发生错误: {e}")
            print(f"内存测试错误: {e}")
    
    def complete_test(self):
        """完成测试"""
        self.stop_test()
        
        # 计算最终结果
        final_memory = self.process.memory_info().rss / 1024 / 1024
        memory_increase = final_memory - self.initial_memory
        
        self.add_result("=" * 50)
        self.add_result("内存泄漏测试完成！")
        self.add_result(f"初始内存: {self.initial_memory:.1f} MB")
        self.add_result(f"最终内存: {final_memory:.1f} MB")
        self.add_result(f"内存增长: {memory_increase:+.1f} MB")
        
        # 评估结果
        if memory_increase < 5:
            self.add_result("✅ 结果：内存使用正常，无明显泄漏")
            self.status_label.setText("测试完成 - 内存正常")
            self.status_label.setStyleSheet("font-size: 14px; padding: 10px; color: #27ae60;")
        elif memory_increase < 15:
            self.add_result("⚠️ 结果：内存增长较小，可能存在轻微泄漏")
            self.status_label.setText("测试完成 - 轻微增长")
            self.status_label.setStyleSheet("font-size: 14px; padding: 10px; color: #f39c12;")
        else:
            self.add_result("❌ 结果：内存增长明显，可能存在内存泄漏")
            self.status_label.setText("测试完成 - 内存增长明显")
            self.status_label.setStyleSheet("font-size: 14px; padding: 10px; color: #e74c3c;")
        
        self.add_result("=" * 50)
    
    def closeEvent(self, event):
        """关闭事件"""
        if self.test_running:
            self.stop_test()
        self.monitor_timer.stop()
        event.accept()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # 设置应用程序样式
    app.setStyle(QStyleFactory.create('Fusion'))
    
    window = SimpleMemoryTest()
    window.show()
    
    print("简化内存测试程序已启动")
    print("点击'开始测试'按钮开始内存泄漏测试")
    
    sys.exit(app.exec_())

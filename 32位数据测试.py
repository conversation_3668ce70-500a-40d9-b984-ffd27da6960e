#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
32位数据读写测试程序
验证PLC 32位数据的正确读写
"""

import sys
import math
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

class Data32BitTest(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("32位数据读写测试程序")
        self.setGeometry(100, 100, 900, 700)
        
        # 硬件参数
        self.wheel_diameter = 48.0  # mm
        self.pulses_per_revolution = 2000
        self.wheel_circumference = math.pi * self.wheel_diameter
        self.pulses_per_mm = self.pulses_per_revolution / self.wheel_circumference
        
        self.init_ui()
        
    def init_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title = QLabel("32位数据读写测试程序")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 18px; font-weight: bold; padding: 10px;")
        layout.addWidget(title)
        
        # 说明
        info_label = QLabel("PLC要求发送和接收的数据都是32位数，每个32位数据占用两个连续的16位寄存器")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("color: #7f8c8d; font-size: 12px; padding: 5px;")
        info_label.setWordWrap(True)
        layout.addWidget(info_label)
        
        # 32位数据拆分演示
        demo_group = QGroupBox("32位数据拆分演示")
        demo_layout = QGridLayout()
        
        # 输入32位数值
        demo_layout.addWidget(QLabel("32位数值:"), 0, 0)
        self.value_32bit_spin = QSpinBox()
        self.value_32bit_spin.setRange(0, 2147483647)  # 32位有符号整数最大值
        self.value_32bit_spin.setValue(1000000)
        self.value_32bit_spin.valueChanged.connect(self.update_split_demo)
        demo_layout.addWidget(self.value_32bit_spin, 0, 1)
        
        # 显示拆分结果
        demo_layout.addWidget(QLabel("低16位寄存器:"), 1, 0)
        self.low_16bit_label = QLabel("0")
        self.low_16bit_label.setStyleSheet("font-weight: bold; color: #3498db;")
        demo_layout.addWidget(self.low_16bit_label, 1, 1)
        
        demo_layout.addWidget(QLabel("高16位寄存器:"), 1, 2)
        self.high_16bit_label = QLabel("0")
        self.high_16bit_label.setStyleSheet("font-weight: bold; color: #e74c3c;")
        demo_layout.addWidget(self.high_16bit_label, 1, 3)
        
        # 显示十六进制
        demo_layout.addWidget(QLabel("十六进制:"), 2, 0)
        self.hex_label = QLabel("0x00000000")
        self.hex_label.setStyleSheet("font-weight: bold; color: #27ae60;")
        demo_layout.addWidget(self.hex_label, 2, 1)
        
        demo_group.setLayout(demo_layout)
        layout.addWidget(demo_group)
        
        # 长度到脉冲转换测试
        conversion_group = QGroupBox("长度到脉冲转换测试（32位）")
        conversion_layout = QGridLayout()
        
        # 第一根长度
        conversion_layout.addWidget(QLabel("第一根长度 (mm):"), 0, 0)
        self.first_length_spin = QDoubleSpinBox()
        self.first_length_spin.setRange(0, 99999.99)
        self.first_length_spin.setDecimals(2)
        self.first_length_spin.setValue(5000.00)  # 测试大数值
        self.first_length_spin.valueChanged.connect(self.update_pulse_conversion)
        conversion_layout.addWidget(self.first_length_spin, 0, 1)
        
        conversion_layout.addWidget(QLabel("脉冲数:"), 0, 2)
        self.first_pulses_label = QLabel("0")
        self.first_pulses_label.setStyleSheet("font-weight: bold; color: #2ecc71;")
        conversion_layout.addWidget(self.first_pulses_label, 0, 3)
        
        # 递增长度
        conversion_layout.addWidget(QLabel("递增长度 (mm):"), 1, 0)
        self.increment_spin = QDoubleSpinBox()
        self.increment_spin.setRange(-1000, 1000)
        self.increment_spin.setDecimals(2)
        self.increment_spin.setValue(50.00)
        self.increment_spin.valueChanged.connect(self.update_pulse_conversion)
        conversion_layout.addWidget(self.increment_spin, 1, 1)
        
        conversion_layout.addWidget(QLabel("脉冲数:"), 1, 2)
        self.increment_pulses_label = QLabel("0")
        self.increment_pulses_label.setStyleSheet("font-weight: bold; color: #3498db;")
        conversion_layout.addWidget(self.increment_pulses_label, 1, 3)
        
        conversion_group.setLayout(conversion_layout)
        layout.addWidget(conversion_group)
        
        # 寄存器占用情况
        register_group = QGroupBox("寄存器占用情况")
        register_layout = QGridLayout()
        
        register_layout.addWidget(QLabel("寄存器"), 0, 0)
        register_layout.addWidget(QLabel("HD地址"), 0, 1)
        register_layout.addWidget(QLabel("Modbus地址"), 0, 2)
        register_layout.addWidget(QLabel("占用情况"), 0, 3)
        
        # HD1004-HD1005 (第一根脉冲)
        register_layout.addWidget(QLabel("第一根脉冲"), 1, 0)
        register_layout.addWidget(QLabel("HD1004-HD1005"), 1, 1)
        register_layout.addWidget(QLabel("42092-42093"), 1, 2)
        self.first_register_status = QLabel("32位数据")
        self.first_register_status.setStyleSheet("color: #27ae60;")
        register_layout.addWidget(self.first_register_status, 1, 3)
        
        # HD1006-HD1007 (递增脉冲)
        register_layout.addWidget(QLabel("递增脉冲"), 2, 0)
        register_layout.addWidget(QLabel("HD1006-HD1007"), 2, 1)
        register_layout.addWidget(QLabel("42094-42095"), 2, 2)
        self.increment_register_status = QLabel("32位数据")
        self.increment_register_status.setStyleSheet("color: #3498db;")
        register_layout.addWidget(self.increment_register_status, 2, 3)
        
        register_group.setLayout(register_layout)
        layout.addWidget(register_group)
        
        # 数据范围检查
        range_group = QGroupBox("32位数据范围检查")
        range_layout = QGridLayout()
        
        range_layout.addWidget(QLabel("数据类型"), 0, 0)
        range_layout.addWidget(QLabel("最小值"), 0, 1)
        range_layout.addWidget(QLabel("最大值"), 0, 2)
        range_layout.addWidget(QLabel("当前状态"), 0, 3)
        
        # 无符号32位
        range_layout.addWidget(QLabel("无符号32位"), 1, 0)
        range_layout.addWidget(QLabel("0"), 1, 1)
        range_layout.addWidget(QLabel("4,294,967,295"), 1, 2)
        self.unsigned_status = QLabel("正常")
        self.unsigned_status.setStyleSheet("color: #27ae60;")
        range_layout.addWidget(self.unsigned_status, 1, 3)
        
        # 有符号32位
        range_layout.addWidget(QLabel("有符号32位"), 2, 0)
        range_layout.addWidget(QLabel("-2,147,483,648"), 2, 1)
        range_layout.addWidget(QLabel("2,147,483,647"), 2, 2)
        self.signed_status = QLabel("正常")
        self.signed_status.setStyleSheet("color: #27ae60;")
        range_layout.addWidget(self.signed_status, 2, 3)
        
        range_group.setLayout(range_layout)
        layout.addWidget(range_group)
        
        # 测试数据表格
        table_group = QGroupBox("常用长度的32位脉冲数据")
        table_layout = QVBoxLayout()
        
        self.data_table = QTableWidget()
        self.data_table.setColumnCount(5)
        self.data_table.setHorizontalHeaderLabels(["长度(mm)", "脉冲数", "低16位", "高16位", "十六进制"])
        self.data_table.setMaximumHeight(200)
        
        # 填充测试数据
        test_lengths = [100, 500, 1000, 2000, 5000, 10000, 15000]
        self.data_table.setRowCount(len(test_lengths))
        
        for i, length in enumerate(test_lengths):
            pulses = int(round(length * self.pulses_per_mm))
            high_16bit = (pulses >> 16) & 0xFFFF
            low_16bit = pulses & 0xFFFF
            hex_value = f"0x{pulses:08X}"
            
            self.data_table.setItem(i, 0, QTableWidgetItem(f"{length}"))
            self.data_table.setItem(i, 1, QTableWidgetItem(f"{pulses}"))
            self.data_table.setItem(i, 2, QTableWidgetItem(f"{low_16bit}"))
            self.data_table.setItem(i, 3, QTableWidgetItem(f"{high_16bit}"))
            self.data_table.setItem(i, 4, QTableWidgetItem(hex_value))
        
        self.data_table.resizeColumnsToContents()
        table_layout.addWidget(self.data_table)
        
        table_group.setLayout(table_layout)
        layout.addWidget(table_group)
        
        # 初始更新
        self.update_split_demo()
        self.update_pulse_conversion()
        
    def split_32bit_to_16bit(self, value_32bit):
        """将32位数值拆分为两个16位数值"""
        if value_32bit < 0 or value_32bit > 0xFFFFFFFF:
            return 0, 0
        
        high_16bit = (value_32bit >> 16) & 0xFFFF
        low_16bit = value_32bit & 0xFFFF
        
        return high_16bit, low_16bit
    
    def length_to_pulses(self, length_mm):
        """长度转换为脉冲数"""
        if length_mm <= 0:
            return 0
        return int(round(length_mm * self.pulses_per_mm))
    
    def update_split_demo(self):
        """更新32位数据拆分演示"""
        try:
            value_32bit = self.value_32bit_spin.value()
            high_16bit, low_16bit = self.split_32bit_to_16bit(value_32bit)
            
            self.low_16bit_label.setText(str(low_16bit))
            self.high_16bit_label.setText(str(high_16bit))
            self.hex_label.setText(f"0x{value_32bit:08X}")
            
            # 检查数据范围
            if 0 <= value_32bit <= 0xFFFFFFFF:
                self.unsigned_status.setText("正常")
                self.unsigned_status.setStyleSheet("color: #27ae60;")
            else:
                self.unsigned_status.setText("超出范围")
                self.unsigned_status.setStyleSheet("color: #e74c3c;")
            
            if -2147483648 <= value_32bit <= 2147483647:
                self.signed_status.setText("正常")
                self.signed_status.setStyleSheet("color: #27ae60;")
            else:
                self.signed_status.setText("超出范围")
                self.signed_status.setStyleSheet("color: #e74c3c;")
                
        except Exception as e:
            print(f"更新拆分演示时发生错误: {e}")
    
    def update_pulse_conversion(self):
        """更新脉冲转换显示"""
        try:
            first_length = self.first_length_spin.value()
            increment = self.increment_spin.value()
            
            first_pulses = self.length_to_pulses(first_length)
            increment_pulses = self.length_to_pulses(increment)
            
            self.first_pulses_label.setText(f"{first_pulses:,}")
            self.increment_pulses_label.setText(f"{increment_pulses:,}")
            
            # 更新寄存器状态
            first_high, first_low = self.split_32bit_to_16bit(first_pulses)
            increment_high, increment_low = self.split_32bit_to_16bit(abs(increment_pulses))
            
            self.first_register_status.setText(f"低位:{first_low}, 高位:{first_high}")
            self.increment_register_status.setText(f"低位:{increment_low}, 高位:{increment_high}")
            
        except Exception as e:
            print(f"更新脉冲转换时发生错误: {e}")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # 设置应用程序样式
    app.setStyle(QStyleFactory.create('Fusion'))
    
    window = Data32BitTest()
    window.show()
    
    print("32位数据测试程序已启动")
    print("这个程序演示了PLC 32位数据的拆分和转换")
    
    sys.exit(app.exec_())

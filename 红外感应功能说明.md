# 红外感应功能说明

## 功能概述

本次更新在剪切机自动化控制系统中添加了红外感应功能，用于检测进料口是否有物料。只有当红外感应器检测到有物料时，机器才允许启动；如果在运行过程中检测到无物料，机器会自动停止。

## 硬件连接

- **红外感应器连接端口**: X7 (PLC输入端口)
- **信号类型**: 数字输入信号
- **信号逻辑**: 
  - ON (1): 检测到物料
  - OFF (0): 未检测到物料

## 功能特性

### 1. 启动前检查
- 在启动剪切机之前，系统会自动检查红外感应器状态
- 只有检测到物料时才允许启动
- 如果未检测到物料，会显示警告信息并禁止启动

### 2. 运行中监控
- 在机器运行过程中，系统会持续监控红外感应器状态
- 如果检测到无物料状态，机器会自动停止
- 自动停止时会记录日志并可选择显示提示对话框

### 3. 状态显示
- 主界面状态区域新增"红外感应器"状态显示
- 实时显示当前感应器状态：
  - 🟢 有料: 检测到物料
  - 🔴 无料: 未检测到物料

### 4. 配置选项
- 可在端口配置界面配置红外感应器的输入地址
- 默认配置为X7 (地址7)
- 支持自动停止通知的开关控制

## 界面更新

### 主界面状态显示区域
```
连接状态: 已连接          当前长度: 0.00 mm
已完成数量: 0            设备状态: 待机
红外感应器: 🟢 有料      自动停止通知: ☑ 启用
任务进度: ████████████████████████████████ 0/100 (0%)
```

### 端口配置界面
在位地址配置区域新增：
```
红外感应器 (X7) - 只读    [    7    ]
检测到物料时为ON，无物料时为OFF
```

## 配置文件更新

在 `config.json` 文件的 `bit_map` 部分新增：
```json
{
    "bit_map": {
        "manual_auto_switch": 10,
        "host_reset_button": 11,
        "resetting": 12,
        "standby": 13,
        "auto_running": 14,
        "emergency_stop": 15,
        "start": 16,
        "stop": 17,
        "infrared_sensor": 7
    }
}
```

## 使用说明

### 1. 基本使用
1. 确保红外感应器正确连接到PLC的X7端口
2. 启动软件并连接到PLC
3. 在进料口放置物料
4. 点击"启动"按钮，系统会自动检查红外感应器状态
5. 如果检测到物料，机器正常启动；否则显示警告

### 2. 配置红外感应器地址
1. 切换到"端口配置"选项卡
2. 在位地址配置区域找到"红外感应器 (X7)"
3. 修改地址值（如果需要）
4. 点击"保存位地址配置"

### 3. 自动停止通知设置
1. 在主界面状态显示区域找到"自动停止通知"
2. 勾选"启用"可在自动停止时显示提示对话框
3. 取消勾选则只记录日志，不显示对话框

## 测试程序

提供了专门的测试程序 `test_infrared_sensor.py` 用于测试红外感应功能：

### 运行测试程序
```bash
python test_infrared_sensor.py
```

### 测试功能
1. **监控红外感应器状态**: 实时显示感应器状态变化
2. **测试启动逻辑**: 验证启动前的红外感应检查功能

## 技术实现

### 核心方法
- `check_infrared_sensor()`: 检查红外感应器状态
- `start_cutting()`: 修改后的启动方法，包含红外感应检查
- `update_status()`: 修改后的状态更新方法，包含自动停止逻辑

### 关键代码逻辑
```python
def check_infrared_sensor(self):
    """检查红外感应器状态"""
    # 读取X7输入状态
    data = current_modbus.read_coil(slave_addr, infrared_addr, 1)
    if data and len(data) > 0:
        return data[0]  # True=有料, False=无料
    return None  # 读取失败

def start_cutting(self):
    """启动剪切机（包含红外感应检查）"""
    # 检查红外感应器状态
    infrared_status = self.check_infrared_sensor()
    if not infrared_status:
        # 显示警告，禁止启动
        return
    # 允许启动...
```

## 注意事项

1. **硬件连接**: 确保红外感应器正确连接到PLC的X7端口
2. **信号逻辑**: 确认红外感应器的输出逻辑与系统预期一致
3. **响应时间**: 系统每3秒检查一次状态，可能有轻微延迟
4. **安全考虑**: 自动停止功能是安全保护措施，不应禁用
5. **维护**: 定期清洁红外感应器，确保检测准确性

## 故障排除

### 常见问题
1. **红外感应器状态显示异常**
   - 检查X7端口连接
   - 确认感应器电源供应
   - 验证PLC输入配置

2. **无法启动机器**
   - 确认红外感应器检测到物料
   - 检查感应器安装位置和角度
   - 验证物料是否在检测范围内

3. **机器意外停止**
   - 检查物料是否移出检测范围
   - 确认感应器工作正常
   - 查看系统日志了解详细信息

### 调试方法
1. 使用测试程序监控感应器状态
2. 查看通信日志了解数据交换情况
3. 检查PLC程序中X7端口的配置

## 版本信息

- **功能版本**: v3.3
- **添加日期**: 2024年
- **兼容性**: 向后兼容，不影响现有功能
- **依赖**: 需要PLC支持X7输入端口

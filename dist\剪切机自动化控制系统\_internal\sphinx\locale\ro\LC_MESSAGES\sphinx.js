Documentation.addTranslations({
    "locale": "ro",
    "messages": {
        "%(filename)s &#8212; %(docstitle)s": "",
        "&#169; %(copyright_prefix)s %(copyright)s.": "",
        ", in ": ", \u00een",
        "About these documents": "Despre aceste documente",
        "Automatically generated list of changes in version %(version)s": "Lista de schimb\u0103ri generat\u0103 automat pentru versiunea %(version)s",
        "C API changes": "Schimb\u0103ri \u00een API C",
        "Changes in Version %(version)s &#8212; %(docstitle)s": "",
        "Collapse sidebar": "Ascundere bar\u0103 lateral\u0103",
        "Complete Table of Contents": "Cuprinsul Complet",
        "Contents": "Cuprins",
        "Copyright": "Drepturi de autor",
        "Created using <a href=\"https://www.sphinx-doc.org/\">Sphinx</a> %(sphinx_version)s.": "",
        "Expand sidebar": "Expandare bar\u0103 lateral\u0103",
        "Full index on one page": "Index complet",
        "General Index": "Index General",
        "Global Module Index": "Index Module Globale",
        "Go": "Caut\u0103",
        "Hide Search Matches": "Ascunde Rezultatele C\u0103ut\u0103rii",
        "Index": "Index",
        "Index &#x2013; %(key)s": "",
        "Index pages by letter": "Indexeaz\u0103 paginile dupa liter\u0103",
        "Indices and tables:": "Indici \u0219i tabele:",
        "Last updated on %(last_updated)s.": "Ultima actualizare la %(last_updated)s.",
        "Library changes": "Schimb\u0103ri \u00een bibliotec\u0103",
        "Navigation": "Navigare",
        "Next topic": "Subiectul urm\u0103tor",
        "Other changes": "Alte schimb\u0103ri",
        "Overview": "Prezentare general\u0103",
        "Please activate JavaScript to enable the search\n    functionality.": "Activeaz\u0103 JavaScript pentru a permite\nfunc\u021bia de c\u0103utare.",
        "Preparing search...": "Se preg\u0103te\u0219te c\u0103utarea...",
        "Previous topic": "Subiectul precedent",
        "Quick search": "C\u0103utare rapid\u0103",
        "Search": "C\u0103utare",
        "Search Page": "Pagin\u0103 de C\u0103utare",
        "Search Results": "Rezultatele C\u0103ut\u0103rii",
        "Search finished, found one page matching the search query.": [
            "",
            "",
            ""
        ],
        "Search within %(docstitle)s": "Caut\u0103 \u00een %(docstitle)s",
        "Searching": "C\u0103utare",
        "Searching for multiple words only shows matches that contain\n    all words.": "",
        "Show Source": "Vezi Sursa",
        "Table of Contents": "",
        "This Page": "Aceast\u0103 Pagin\u0103",
        "Welcome! This is": "Bine ai venit! Acesta este",
        "Your search did not match any documents. Please make sure that all words are spelled correctly and that you've selected enough categories.": "C\u0103utarea nu a identificat nici un document. Te rog s\u0103 te asiguri c\u0103 toate cuvintele sunt scrise corect \u0219i c\u0103 ai selectat suficiente categorii.",
        "all functions, classes, terms": "toate func\u021biile, clasele, termenii",
        "can be huge": "poate fi extrem de mare",
        "last updated": "ultima actualizare",
        "lists all sections and subsections": "lista tuturor sec\u021biunilor si a subsec\u021biunilor",
        "next chapter": "capitolul urm\u0103tor",
        "previous chapter": "capitolul precedent",
        "quick access to all modules": "acces rapid la toate modulele",
        "search": "c\u0103utare",
        "search this documentation": "caut\u0103 \u00een aceast\u0103 documenta\u021bie",
        "the documentation for": "documenta\u021bia pentru"
    },
    "plural_expr": "(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1))"
});
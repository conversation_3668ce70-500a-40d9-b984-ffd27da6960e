# 剪切机自动化控制系统

这是一个用Python开发的剪切机自动化控制软件，支持Modbus RTU和Modbus TCP两种通讯协议，可通过串口或以太网与信捷XL5E-16T PLC直接通信，实现对剪切机参数的控制和监控。

## 功能特点

### 核心功能
- **双协议支持**：支持Modbus RTU（串口）和Modbus TCP（以太网）协议
- **灵活连接方式**：可通过串口或网络直接连接信捷XL5E-16T PLC
- **参数设置**：设置剪切参数（长度、数量、增移量、伺服速度和误差调整）
- **实时监控**：实时监控剪切机状态和进度
- **基本操作**：提供启动、停止和复位等基本操作功能
- **生产订单管理**：支持批量生产任务的创建和管理
- **任务规划**：智能任务调度和执行

### 高级功能
- **寄存器监控**：实时监控Modbus寄存器变化
- **地址扫描**：自动扫描活动的Modbus地址
- **数据记录**：详细的操作日志和数据变化记录
- **配置管理**：灵活的配置文件管理和参数保存
- **多设备支持**：支持多台设备的集中管理

## 系统要求

### 软件要求
- **Python**: 3.6或更高版本
- **操作系统**: Windows 7/10/11, Linux, macOS
- **内存**: 最少512MB RAM
- **存储**: 最少100MB可用空间

### Python依赖库
- **PyQt5**: GUI界面框架
- **pyserial**: 串口通讯支持
- **socket**: TCP/IP网络通讯（Python内置）

### 硬件要求
- **串口连接**: USB转串口适配器或内置串口（用于RTU协议）
- **网络连接**: 以太网接口（用于TCP协议）
- **目标设备**: 信捷XL5E-16T PLC（支持Modbus RTU和Modbus TCP协议）

## 安装步骤

### 1. 环境准备
确保已安装Python 3.6或更高版本：
```bash
python --version
```

### 2. 安装依赖库
```bash
pip install PyQt5 pyserial
```

### 3. 下载项目
```bash
git clone [项目地址]
cd 剪切机自动化3.2
```

### 4. 启动应用程序
```bash
python main.py
```

## 快速开始

### RTU协议连接（串口）
1. 连接串口线到信捷XL5E-16T PLC的通讯端口
2. 启动程序，选择"端口配置"选项卡
3. 选择"Modbus RTU"协议
4. 配置串口参数（端口、波特率等）
5. 点击"保存配置"
6. 返回主界面，点击"连接"

### TCP协议连接（以太网）
1. 确保信捷XL5E-16T PLC和PC在同一网络
2. 启动程序，选择"端口配置"选项卡
3. 选择"Modbus TCP"协议
4. 输入PLC的IP地址（如：*************）
5. 设置端口号（默认：502）
6. 点击"保存配置"
7. 返回主界面，点击"连接"

## 主要功能说明

### 连接设置
- **协议选择**: 在端口配置中选择Modbus RTU或TCP协议
- **参数配置**: 根据选择的协议配置相应参数
- **从站地址**: 设置目标设备的从站地址（默认为1）
- **连接**: 点击"连接"按钮建立通讯连接

### 参数设置
- **剪切长度**: 设置每次剪切的长度（单位：mm，精度：0.01mm）
- **剪切数量**: 设置需要剪切的总数量
- **增移量**: 设置每次剪切后的增量调整（单位：mm，精度：0.01mm）
- **伺服速度**: 设置伺服电机的运行速度（单位：%，范围：0-100）
- **误差调整**: 设置长度误差补偿值（单位：mm，精度：0.01mm）
- **根数**: 设置每次处理的根数
- **台数**: 设置参与作业的设备台数

### 控制操作
- **应用参数**: 将设置的参数发送到控制器
- **读取参数**: 从控制器读取当前参数设置
- **启动**: 开始剪切作业
- **停止**: 停止当前作业
- **复位**: 复位控制器状态

### 状态监控
- **当前长度**: 实时显示当前剪切长度
- **已完成数量**: 显示已完成的剪切数量
- **机器状态**: 显示设备当前运行状态
- **连接状态**: 显示通讯连接状态

## 高级工具

### 寄存器监控工具
运行 `python modbus_monitor.py` 启动监控工具，可以：
- 配置连接参数
- 选择要监控的寄存器地址
- 实时查看寄存器数值变化

### 地址扫描工具
运行 `python modbus_scanner.py` 启动扫描工具，可以：
- 设置扫描范围和参数
- 自动发现活动的Modbus地址
- 监控发现的地址变化

## 寄存器映射说明

本软件使用以下寄存器地址与信捷XL5E-16T PLC通信。系统支持直接读写32位数据，无需手动处理高低位分离：

### 参数寄存器（可读写）
| 地址 | 名称 | 数据类型 | 单位 | 说明 |
|------|------|----------|------|------|
| 1000 | 剪切长度 | 32位整数 | 0.01mm | 每次剪切的目标长度 |
| 1001 | 剪切数量 | 32位整数 | 个 | 总剪切数量 |
| 1002 | 增移量 | 32位整数 | 0.01mm | 每次剪切后的增量 |
| 1003 | 伺服速度 | 32位整数 | % | 伺服电机运行速度 |
| 1004 | 误差调整 | 32位整数 | 0.01mm | 长度误差补偿值 |
| 1005 | 根数 | 32位整数 | 根 | 每次处理的根数 |
| 1006 | 台数 | 32位整数 | 台 | 参与作业的设备台数 |

### 控制寄存器（只写）
| 地址 | 名称 | 数据类型 | 值 | 说明 |
|------|------|----------|-----|------|
| 2000 | 启动/停止控制 | 32位整数 | 0/1 | 0=停止，1=启动 |
| 2001 | 复位控制 | 32位整数 | 0/1 | 0=正常，1=复位 |

### 状态寄存器（只读）
| 地址 | 名称 | 数据类型 | 单位 | 说明 |
|------|------|----------|------|------|
| 3000 | 当前长度 | 32位整数 | 0.01mm | 当前剪切长度 |
| 3001 | 已完成数量 | 32位整数 | 个 | 已完成的剪切数量 |
| 3002 | 机器状态 | 32位整数 | - | 0=待机，1=运行，2=停止，3=错误，4=复位 |

### 位地址映射（线圈）
| 地址 | 名称 | 访问权限 | 说明 |
|------|------|----------|------|
| M0 | 启动信号 | 读写 | 启动剪切作业 |
| M1 | 停止信号 | 读写 | 停止剪切作业 |
| M10 | 运行状态 | 只读 | 设备运行状态指示 |
| M11 | 错误状态 | 只读 | 设备错误状态指示 |
| M12 | 完成状态 | 只读 | 作业完成状态指示 |

## 32位数据处理说明

### 技术特性
- **直接32位操作**: 信捷XL5E-16T PLC支持直接读写32位数据
- **自动数据处理**: 系统自动处理32位数据的分离和组合
- **无需手动操作**: PLC人员无需手动处理高低位数据
- **高精度支持**: 32位数据提供更大的数值范围和精度

### 数据范围
- **32位有符号整数**: -2,147,483,648 到 2,147,483,647
- **32位无符号整数**: 0 到 4,294,967,295
- **长度精度**: 0.01mm精度下可表示±21,474,836.48mm
- **数量范围**: 支持超过42亿的数量设置

### 实现优势
1. **简化编程**: PLC程序员可直接使用32位变量
2. **提高精度**: 避免16位数据的范围限制
3. **减少错误**: 无需手动处理数据分离和组合
4. **提升性能**: 减少通讯次数，提高数据传输效率

## 项目结构

```
剪切机自动化3.2/
├── main.py                    # 主程序入口
├── modbus_monitor.py          # Modbus寄存器监控工具
├── modbus_scanner.py          # Modbus地址扫描工具
├── production_order.py        # 生产订单管理
├── task_planner.py           # 任务规划器
├── config.json               # 配置文件
├── logs/                     # 日志文件目录
├── 1-项目说明.md             # 项目说明文档
├── 2-使用指南.md             # 详细使用指南
├── 3-技术文档.md             # 技术文档
├── 4-开发记录.md             # 开发记录
├── tcp_demo.py               # TCP功能演示脚本
└── simple_tcp_test.py        # TCP功能测试脚本
```

## 注意事项

### 连接注意事项
- **RTU协议**: 确保信捷XL5E-16T PLC已正确连接到计算机串口
- **TCP协议**: 确保PLC和PC在同一网络，且IP地址配置正确
- **参数设置**: 应在剪切机待机状态下进行参数配置
- **通信错误**: 如遇通信错误，请检查连接和通信参数设置

### 安全注意事项
- 操作前确保设备处于安全状态
- 参数修改前请备份原有配置
- 生产过程中避免频繁修改参数
- 紧急情况下使用停止或复位功能

### 网络安全（TCP协议）
- 确保网络环境安全，避免未授权访问
- 建议使用专用网络或VPN连接
- 定期检查网络连接状态
- 配置适当的防火墙规则

## 故障排除

### 常见问题
- **串口连接失败**: 检查串口线连接，确认端口号和波特率设置
- **TCP连接失败**: 检查网络连通性，确认PLC的IP地址和端口号
- **PLC无响应**: 检查PLC电源和通讯参数配置
- **读写超时**: 增加超时时间设置，检查网络延迟
- **数据错误**: 确认寄存器地址映射，检查数据格式

### 调试方法
1. 查看日志文件（logs目录）
2. 使用测试脚本验证功能
3. 检查配置文件格式
4. 使用网络工具测试连通性

## 技术支持

如遇到技术问题，请：
1. 查看日志文件获取详细错误信息
2. 参考2-使用指南.md获取详细说明
3. 运行测试脚本验证功能
4. 检查信捷XL5E-16T PLC和网络配置

## 许可证

本软件遵循MIT许可证开源。

---

**版本**: 3.2  
**最后更新**: 2024年  
**支持协议**: Modbus RTU, Modbus TCP

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试导入和基本功能
"""

print("开始测试导入...")

try:
    import sys
    print("✅ sys 导入成功")
except ImportError as e:
    print(f"❌ sys 导入失败: {e}")

try:
    import os
    print("✅ os 导入成功")
except ImportError as e:
    print(f"❌ os 导入失败: {e}")

try:
    import time
    print("✅ time 导入成功")
except ImportError as e:
    print(f"❌ time 导入失败: {e}")

try:
    import datetime
    print("✅ datetime 导入成功")
except ImportError as e:
    print(f"❌ datetime 导入失败: {e}")

try:
    import json
    print("✅ json 导入成功")
except ImportError as e:
    print(f"❌ json 导入失败: {e}")

try:
    import binascii
    print("✅ binascii 导入成功")
except ImportError as e:
    print(f"❌ binascii 导入失败: {e}")

try:
    import logging
    print("✅ logging 导入成功")
except ImportError as e:
    print(f"❌ logging 导入失败: {e}")

try:
    import serial
    print("✅ serial 导入成功")
except ImportError as e:
    print(f"❌ serial 导入失败: {e}")

try:
    import serial.tools.list_ports
    print("✅ serial.tools.list_ports 导入成功")
except ImportError as e:
    print(f"❌ serial.tools.list_ports 导入失败: {e}")

try:
    from PyQt5.QtWidgets import *
    print("✅ PyQt5.QtWidgets 导入成功")
except ImportError as e:
    print(f"❌ PyQt5.QtWidgets 导入失败: {e}")

try:
    from PyQt5.QtCore import *
    print("✅ PyQt5.QtCore 导入成功")
except ImportError as e:
    print(f"❌ PyQt5.QtCore 导入失败: {e}")

try:
    from PyQt5.QtGui import *
    print("✅ PyQt5.QtGui 导入成功")
except ImportError as e:
    print(f"❌ PyQt5.QtGui 导入失败: {e}")

print("\n测试基本Qt应用...")

try:
    app = QApplication(sys.argv)
    print("✅ QApplication 创建成功")
    
    # 创建一个简单的窗口
    window = QWidget()
    window.setWindowTitle("测试窗口")
    window.setGeometry(100, 100, 300, 200)
    
    layout = QVBoxLayout()
    label = QLabel("如果您看到这个窗口，说明Qt工作正常！")
    layout.addWidget(label)
    
    button = QPushButton("关闭")
    button.clicked.connect(window.close)
    layout.addWidget(button)
    
    window.setLayout(layout)
    window.show()
    
    print("✅ 测试窗口显示成功")
    print("请检查是否有窗口显示...")
    
    # 运行3秒后自动关闭
    QTimer.singleShot(3000, app.quit)
    
    app.exec_()
    print("✅ Qt应用运行完成")
    
except Exception as e:
    print(f"❌ Qt应用测试失败: {e}")

print("\n所有测试完成！")

# 红外感应功能演示

## 功能概览

红外感应功能现在支持**自由开关控制**，您可以根据实际需要启用或禁用该功能。

## 界面展示

### 主界面状态区域
```
连接状态: 已连接              当前长度: 0.00 mm
已完成数量: 0                设备状态: 待机
红外感应器: 🟢 有料          红外感应功能: [🟢 已启用]
自动停止通知: ☑ 启用
任务进度: ████████████████████████████████ 0/100 (0%)
```

### 红外感应功能按钮状态

#### 启用状态
- **按钮显示**: 🟢 已启用
- **按钮颜色**: 绿色
- **功能**: 启动前检查物料，运行中监控物料

#### 禁用状态  
- **按钮显示**: 🔴 已禁用
- **按钮颜色**: 红色
- **功能**: 跳过所有物料检查

### 红外感应器状态显示

#### 功能启用时
- **🟢 有料**: 检测到物料，可以启动
- **🔴 无料**: 未检测到物料，禁止启动

#### 功能禁用时
- **⚫ 已禁用**: 功能关闭，不进行检测

## 操作演示

### 1. 启用红外感应功能（默认状态）

**步骤**：
1. 确认红外感应功能按钮显示"🟢 已启用"
2. 在进料口放置物料
3. 观察红外感应器状态变为"🟢 有料"
4. 点击"▶️ 启动"按钮
5. 系统检查通过，机器正常启动

**日志输出**：
```
[14:30:15] 红外感应器检测到物料 (X7=ON)，允许启动
[14:30:15] 启动命令已发送到M16
```

### 2. 禁用红外感应功能

**步骤**：
1. 点击"🟢 已启用"按钮
2. 按钮变为"🔴 已禁用"
3. 红外感应器状态显示"⚫ 已禁用"
4. 现在可以直接启动机器，无需物料检查

**日志输出**：
```
[14:32:20] ⚠️ 红外感应功能已禁用
[14:32:25] 红外感应功能已禁用，跳过物料检查
[14:32:25] 启动命令已发送到M16
```

### 3. 重新启用红外感应功能

**步骤**：
1. 点击"🔴 已禁用"按钮
2. 按钮变为"🟢 已启用"
3. 红外感应器恢复正常状态检测
4. 系统重新开始物料监控

**日志输出**：
```
[14:35:10] ✅ 红外感应功能已启用
```

## 实际应用场景

### 场景A：正常生产
```
红外感应功能: 🟢 已启用
红外感应器: 🟢 有料
操作: 点击启动 → 成功启动
```

### 场景B：无物料时尝试启动
```
红外感应功能: 🟢 已启用  
红外感应器: 🔴 无料
操作: 点击启动 → 显示警告，禁止启动
警告信息: "红外感应器未检测到物料，无法启动机器"
```

### 场景C：调试模式
```
红外感应功能: 🔴 已禁用
红外感应器: ⚫ 已禁用
操作: 点击启动 → 直接启动，跳过检查
```

### 场景D：运行中物料用完（功能启用）
```
机器状态: 运行中
红外感应器: 🔴 无料 (物料用完)
系统动作: 自动发送停止命令
日志: "⚠️ 红外感应器检测到无料，自动停止机器"
```

### 场景E：运行中物料用完（功能禁用）
```
机器状态: 运行中
红外感应器: ⚫ 已禁用
系统动作: 继续运行，不自动停止
```

## 配置持久化

- **自动保存**: 每次切换开关状态时自动保存到配置文件
- **启动恢复**: 程序启动时自动恢复上次的开关状态
- **配置文件**: 设置保存在 `config.json` 的 `infrared_enabled` 字段

## 测试验证

### 使用测试程序
```bash
python test_infrared_sensor.py
```

**测试选项**：
1. 监控红外感应器状态
2. 测试启动逻辑  
3. **测试红外感应功能开关** ← 新增
4. 退出

### 功能开关测试
- 显示当前开关状态
- 支持切换开关状态
- 自动保存配置文件
- 验证配置生效

## 安全建议

### 推荐设置
- **生产环境**: 启用红外感应功能 🟢
- **调试环境**: 根据需要选择启用/禁用
- **维护时**: 可临时禁用，完成后重新启用

### 注意事项
1. **禁用功能时要格外小心**，确保人工监控安全
2. **调试完成后记得重新启用**红外感应功能
3. **定期检查功能状态**，确保符合安全要求
4. **培训操作人员**了解两种模式的区别

## 故障排除

### 问题：按钮点击无反应
**解决方法**：
1. 检查程序是否正常运行
2. 查看日志是否有错误信息
3. 重启程序

### 问题：配置不保存
**解决方法**：
1. 检查 config.json 文件权限
2. 确认磁盘空间充足
3. 查看程序运行日志

### 问题：状态显示异常
**解决方法**：
1. 重新连接设备
2. 检查X7端口连接
3. 使用测试程序验证

---

**版本**: v3.3  
**更新内容**: 新增红外感应功能开关控制  
**兼容性**: 完全向后兼容，不影响现有功能

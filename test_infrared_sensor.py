#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
红外感应器测试程序
用于测试X7输入端口的红外感应器功能
"""

import sys
import time
import json
from main import ModbusRTU, ModbusTCP

def load_config():
    """加载配置文件"""
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载配置文件失败: {e}")
        return None

def test_infrared_sensor():
    """测试红外感应器功能"""
    print("=" * 50)
    print("红外感应器测试程序")
    print("=" * 50)
    
    # 加载配置
    config = load_config()
    if not config:
        print("无法加载配置文件，使用默认配置")
        config = {
            'protocol_type': 'RTU',
            'port': 'COM1',
            'baudrate': 9600,
            'bit_map': {'infrared_sensor': 7}
        }
    
    # 获取红外感应器地址
    infrared_addr = config.get('bit_map', {}).get('infrared_sensor', 7)
    print(f"红外感应器地址: X{infrared_addr}")
    
    # 创建Modbus连接
    protocol_type = config.get('protocol_type', 'RTU')
    if protocol_type == 'TCP':
        modbus = ModbusTCP(
            host=config.get('tcp_host', '*************'),
            port=config.get('tcp_port', 502)
        )
        print(f"使用TCP协议连接: {modbus.host}:{modbus.port}")
    else:
        modbus = ModbusRTU(
            port=config.get('port', 'COM1'),
            baudrate=config.get('baudrate', 9600)
        )
        print(f"使用RTU协议连接: {modbus.port}, 波特率: {modbus.baudrate}")
    
    # 尝试连接
    print("\n正在连接...")
    if not modbus.connect():
        print("❌ 连接失败！请检查设备连接和配置")
        return False
    
    print("✅ 连接成功！")
    
    try:
        print("\n开始监控红外感应器状态...")
        print("按 Ctrl+C 停止监控")
        print("-" * 30)
        
        slave_addr = 1  # 默认从站地址
        last_status = None
        
        while True:
            # 读取红外感应器状态
            try:
                data = modbus.read_coil(slave_addr, infrared_addr, 1)
                if data and len(data) > 0:
                    current_status = data[0]
                    
                    # 只在状态变化时显示
                    if current_status != last_status:
                        timestamp = time.strftime('%H:%M:%S')
                        if current_status:
                            print(f"[{timestamp}] 🟢 检测到物料 (X{infrared_addr}=ON)")
                        else:
                            print(f"[{timestamp}] 🔴 未检测到物料 (X{infrared_addr}=OFF)")
                        last_status = current_status
                else:
                    print(f"[{time.strftime('%H:%M:%S')}] ⚠️ 读取失败")
                
            except Exception as e:
                print(f"[{time.strftime('%H:%M:%S')}] ❌ 读取错误: {e}")
            
            time.sleep(0.5)  # 每0.5秒检查一次
            
    except KeyboardInterrupt:
        print("\n\n监控已停止")
    
    finally:
        modbus.disconnect()
        print("连接已断开")
    
    return True

def test_startup_logic():
    """测试启动逻辑"""
    print("\n" + "=" * 50)
    print("启动逻辑测试")
    print("=" * 50)
    
    # 加载配置
    config = load_config()
    if not config:
        print("无法加载配置文件")
        return False
    
    # 获取地址配置
    infrared_addr = config.get('bit_map', {}).get('infrared_sensor', 7)
    start_addr = config.get('bit_map', {}).get('start', 16)
    
    print(f"红外感应器地址: X{infrared_addr}")
    print(f"启动位地址: M{start_addr}")
    
    # 创建Modbus连接
    protocol_type = config.get('protocol_type', 'RTU')
    if protocol_type == 'TCP':
        modbus = ModbusTCP(
            host=config.get('tcp_host', '*************'),
            port=config.get('tcp_port', 502)
        )
    else:
        modbus = ModbusRTU(
            port=config.get('port', 'COM1'),
            baudrate=config.get('baudrate', 9600)
        )
    
    # 尝试连接
    print("\n正在连接...")
    if not modbus.connect():
        print("❌ 连接失败！")
        return False
    
    print("✅ 连接成功！")
    
    try:
        slave_addr = 1
        
        # 检查红外感应器状态
        print("\n检查红外感应器状态...")
        data = modbus.read_coil(slave_addr, infrared_addr, 1)
        if data and len(data) > 0:
            infrared_status = data[0]
            if infrared_status:
                print("🟢 红外感应器检测到物料，允许启动")
                
                # 模拟启动命令
                print("发送启动命令...")
                if modbus.write_coil(slave_addr, start_addr, True):
                    print("✅ 启动命令发送成功")
                    
                    # 等待一下然后清除启动位
                    time.sleep(1)
                    modbus.write_coil(slave_addr, start_addr, False)
                    print("启动位已清除")
                else:
                    print("❌ 启动命令发送失败")
            else:
                print("🔴 红外感应器未检测到物料，禁止启动")
        else:
            print("❌ 无法读取红外感应器状态")
    
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
    
    finally:
        modbus.disconnect()
        print("连接已断开")
    
    return True

def test_infrared_switch():
    """测试红外感应功能开关"""
    print("\n" + "=" * 50)
    print("红外感应功能开关测试")
    print("=" * 50)

    # 加载配置
    config = load_config()
    if not config:
        print("无法加载配置文件")
        return False

    # 显示当前配置
    infrared_enabled = config.get('infrared_enabled', True)
    print(f"当前红外感应功能状态: {'启用' if infrared_enabled else '禁用'}")

    # 询问是否要切换状态
    while True:
        choice = input("\n是否要切换红外感应功能状态? (y/n): ").strip().lower()
        if choice in ['y', 'yes', '是']:
            # 切换状态
            new_state = not infrared_enabled
            config['infrared_enabled'] = new_state

            # 保存配置
            try:
                with open('config.json', 'w', encoding='utf-8') as f:
                    import json
                    json.dump(config, f, indent=4, ensure_ascii=False)
                print(f"✅ 红外感应功能已{'启用' if new_state else '禁用'}")
                print("配置已保存到 config.json")
                return True
            except Exception as e:
                print(f"❌ 保存配置失败: {e}")
                return False
        elif choice in ['n', 'no', '否']:
            print("未修改配置")
            return True
        else:
            print("请输入 y 或 n")

def main():
    """主函数"""
    print("红外感应器功能测试")
    print("1. 监控红外感应器状态")
    print("2. 测试启动逻辑")
    print("3. 测试红外感应功能开关")
    print("4. 退出")

    while True:
        try:
            choice = input("\n请选择测试项目 (1-4): ").strip()

            if choice == '1':
                test_infrared_sensor()
            elif choice == '2':
                test_startup_logic()
            elif choice == '3':
                test_infrared_switch()
            elif choice == '4':
                print("退出程序")
                break
            else:
                print("无效选择，请输入 1-4")

        except KeyboardInterrupt:
            print("\n\n程序已退出")
            break
        except Exception as e:
            print(f"程序错误: {e}")

if __name__ == "__main__":
    main()

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简单的TCP/IP协议功能测试脚本（不需要GUI）
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_modbus_tcp_import():
    """测试ModbusTCP类导入"""
    print("=== 测试ModbusTCP类导入 ===")
    
    try:
        from main import ModbusTCP
        print("✓ 成功导入ModbusTCP类")
        return True, ModbusTCP
    except ImportError as e:
        print(f"✗ 导入ModbusTCP类失败: {e}")
        return False, None

def test_modbus_tcp_creation(ModbusTCP):
    """测试ModbusTCP类创建"""
    print("\n=== 测试ModbusTCP类创建 ===")
    
    try:
        modbus_tcp = ModbusTCP(host='*************', port=502, timeout=5.0)
        print("✓ 成功创建ModbusTCP实例")
        print(f"  - 主机地址: {modbus_tcp.host}")
        print(f"  - 端口号: {modbus_tcp.port}")
        print(f"  - 超时设置: {modbus_tcp.timeout}")
        print(f"  - 连接状态: {modbus_tcp.is_connected()}")
        return True, modbus_tcp
    except Exception as e:
        print(f"✗ 创建ModbusTCP实例失败: {e}")
        return False, None

def test_modbus_tcp_methods(modbus_tcp):
    """测试ModbusTCP类方法"""
    print("\n=== 测试ModbusTCP类方法 ===")
    
    methods_to_test = [
        'connect', 'disconnect', 'is_connected',
        'read_register', 'write_register', 'write_multiple_registers',
        'read_coil', 'write_coil',
        'read_double_word_register', 'write_double_word_register'
    ]
    
    success_count = 0
    for method_name in methods_to_test:
        if hasattr(modbus_tcp, method_name):
            print(f"✓ 方法 {method_name} 存在")
            success_count += 1
        else:
            print(f"✗ 方法 {method_name} 不存在")
    
    print(f"\n方法测试结果: {success_count}/{len(methods_to_test)} 通过")
    return success_count == len(methods_to_test)

def test_tcp_header_methods(modbus_tcp):
    """测试TCP头部相关方法"""
    print("\n=== 测试TCP头部方法 ===")
    
    try:
        # 测试事务ID生成
        tid1 = modbus_tcp._get_next_transaction_id()
        tid2 = modbus_tcp._get_next_transaction_id()
        print(f"✓ 事务ID生成: {tid1} -> {tid2}")
        
        # 测试TCP头部构建
        header = modbus_tcp._build_tcp_header(5, 1)
        print(f"✓ TCP头部构建成功，长度: {len(header)} 字节")
        print(f"  头部内容: {header.hex()}")
        
        return True
    except Exception as e:
        print(f"✗ TCP头部方法测试失败: {e}")
        return False

def test_config_compatibility():
    """测试配置兼容性"""
    print("\n=== 测试配置兼容性 ===")
    
    try:
        # 测试配置文件结构
        config_template = {
            'protocol_type': 'TCP',
            'tcp_host': '*************',
            'tcp_port': 502,
            'tcp_timeout': 5.0,
            'port': 'COM1',
            'baudrate': 9600,
            'timeout': 0.5
        }
        
        print("✓ 配置文件模板结构正确")
        for key, value in config_template.items():
            print(f"  - {key}: {value}")
        
        return True
    except Exception as e:
        print(f"✗ 配置兼容性测试失败: {e}")
        return False

if __name__ == "__main__":
    print("开始简单TCP/IP协议功能测试...\n")
    
    # 测试导入
    import_success, ModbusTCP = test_modbus_tcp_import()
    if not import_success:
        print("\n❌ 导入测试失败，退出测试")
        sys.exit(1)
    
    # 测试创建
    creation_success, modbus_tcp = test_modbus_tcp_creation(ModbusTCP)
    if not creation_success:
        print("\n❌ 创建测试失败，退出测试")
        sys.exit(1)
    
    # 测试方法
    methods_success = test_modbus_tcp_methods(modbus_tcp)
    
    # 测试TCP头部方法
    header_success = test_tcp_header_methods(modbus_tcp)
    
    # 测试配置兼容性
    config_success = test_config_compatibility()
    
    # 总结
    print("\n" + "="*50)
    print("测试总结:")
    print(f"✓ 导入测试: {'通过' if import_success else '失败'}")
    print(f"✓ 创建测试: {'通过' if creation_success else '失败'}")
    print(f"✓ 方法测试: {'通过' if methods_success else '失败'}")
    print(f"✓ TCP头部测试: {'通过' if header_success else '失败'}")
    print(f"✓ 配置兼容性测试: {'通过' if config_success else '失败'}")
    
    all_passed = all([import_success, creation_success, methods_success, header_success, config_success])
    
    if all_passed:
        print("\n🎉 所有测试通过！TCP/IP协议功能已成功实现。")
        print("\n使用说明：")
        print("1. 运行 python main.py 启动主程序")
        print("2. 在'端口配置'选项卡中：")
        print("   - 选择'Modbus TCP'协议")
        print("   - 配置IP地址（默认*************）")
        print("   - 配置端口号（默认502）")
        print("   - 点击'保存配置'")
        print("3. 在主界面点击'连接'按钮")
        print("4. 现在可以使用TCP/IP协议进行Modbus通讯了")
        print("\n注意：需要确保目标设备支持Modbus TCP协议")
    else:
        print("\n❌ 部分测试失败，请检查代码实现。")
    
    print("\n测试完成。")

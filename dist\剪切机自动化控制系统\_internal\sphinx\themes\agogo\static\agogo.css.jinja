/*
 * Sphinx stylesheet -- agogo theme.
 */

* {
  margin: 0px;
  padding: 0px;
}

body {
  font-family: {{ theme_bodyfont }};
  line-height: 1.4em;
  color: black;
  background-color: {{ theme_bgcolor }};

  /* fix for background colors breaking at horizontal
    scrolling on smaller devices */
  min-width: fit-content;
}


/* Page layout */

div.header, div.content, div.footer {
  width: {{ theme_pagewidth }};
  margin-left: auto;
  margin-right: auto;
}

div.header-wrapper {
  background: {{ theme_headerbg }};
  border-bottom: 3px solid #2e3436;
}


/* Default body styles */
a {
  color: {{ theme_linkcolor }};
}

a:visited {
  color: #551a8b;
}

div.bodywrapper a, div.footer a {
  text-decoration: underline;
}

.clearer {
  clear: both;
}

.left {
  float: left;
}

.right {
  float: right;
}

.line-block {
    display: block;
    margin-top: 1em;
    margin-bottom: 1em;
}

.line-block .line-block {
    margin-top: 0;
    margin-bottom: 0;
    margin-left: 1.5em;
}

h1, h2, h3, h4 {
  font-family: {{ theme_headerfont }};
  font-weight: normal;
  color: {{ theme_headercolor2 }};
  margin-bottom: .8em;
}

h1 {
  color: {{ theme_headercolor1 }};
}

h2 {
  padding-bottom: .5em;
  border-bottom: 1px solid {{ theme_headercolor2 }};
}

a.headerlink {
  visibility: hidden;
  color: #dddddd;
  padding-left: .3em;
}

h1:hover > a.headerlink,
h2:hover > a.headerlink,
h3:hover > a.headerlink,
h4:hover > a.headerlink,
h5:hover > a.headerlink,
h6:hover > a.headerlink,
dt:hover > a.headerlink,
caption:hover > a.headerlink,
p.caption:hover > a.headerlink,
div.code-block-caption:hover > a.headerlink {
  visibility: visible;
}

img {
  border: 0;
}

div.admonition {
  margin-top: 10px;
  margin-bottom: 10px;
  padding: 2px 7px 1px 7px;
  border-left: 0.2em solid black;
}

p.admonition-title {
  margin: 0px 10px 5px 0px;
  font-weight: bold;
}

dt:target, .highlighted {
  background-color: #fbe54e;
}

/* Header */

div.header {
  padding: 1em;
}

div.header .headertitle {
  font-family: {{ theme_headerfont }};
  font-weight: normal;
  font-size: 180%;
  letter-spacing: .08em;
  margin-bottom: .8em;
}

div.header .headertitle a {
  color: white;
}

div.header div.rel {
  margin-top: 1em;
}

div.header div.rel a {
  color: {{ theme_headerlinkcolor }};
  letter-spacing: .1em;
  text-transform: uppercase;
}

p.logo {
    float: right;
}

img.logo {
    border: 0;
}


/* Content */
div.content-wrapper {
  background-color: white;
  padding: 1em;
}

div.document {
  width: {{ theme_documentwidth }};
  float: left;
}

div.body {
  {%- if theme_rightsidebar|tobool %}
  padding-right: 2em;
  {%- else %}
  padding-left: 2em;
  {% endif %}
  text-align: {{ theme_textalign }};
}

div.document h1 {
  line-height: 120%;
}

div.document ul {
  margin: 1.5em;
  list-style-type: square;
}

div.document dd {
  margin-left: 1.2em;
  margin-top: .4em;
  margin-bottom: 1em;
}

div.document .section {
  margin-top: 1.7em;
}
div.document .section:first-child {
  margin-top: 0px;
}

div.document div.highlight {
  padding: 3px;
  border-top: 2px solid #dddddd;
  border-bottom: 2px solid #dddddd;
  margin-top: .8em;
  margin-bottom: .8em;
}

div.document div.literal-block-wrapper {
  margin-top: .8em;
  margin-bottom: .8em;
}

div.document div.literal-block-wrapper div.highlight {
  margin: 0;
}

div.document div.code-block-caption span.caption-number {
    padding: 0.1em 0.3em;
    font-style: italic;
}

div.document div.code-block-caption span.caption-text {
}

div.document h2 {
  margin-top: .7em;
}

div.document p {
  margin-bottom: .5em;
}

div.document li.toctree-l1 {
  margin-bottom: 1em;
}

div.document .descname {
  font-weight: bold;
}

div.document .sig-paren {
    font-size: larger;
}

div.document .docutils.literal {
  background-color: #eeeeec;
  padding: 1px;
}

div.document .docutils.xref.literal {
  background-color: transparent;
  padding: 0px;
}

div.document blockquote {
  margin: 1em;
}

div.document ol {
  margin: 1.5em;
}


/* Sidebar */

div.sidebar,
aside.sidebar {
  width: {{ theme_sidebarwidth|todim }};
  {%- if theme_rightsidebar|tobool %}
  float: right;
  {%- else %}
  float: left;
  {%- endif %}
  font-size: .9em;
}

div.sidebar a, aside.sidebar a, div.header a {
  text-decoration: none;
}

div.sidebar a:hover, aside.sidebar a:hover, div.header a:hover {
  text-decoration: underline;
}

div.sidebar h3,
aside.sidebar h3 {
  color: #2e3436;
  text-transform: uppercase;
  font-size: 130%;
  letter-spacing: .1em;
}

div.sidebar ul,
aside.sidebar ul {
  list-style-type: none;
}

div.sidebar li.toctree-l1 a,
aside.sidebar li.toctree-l1 a {
  display: block;
  padding: 1px;
  border: 1px solid #dddddd;
  background-color: #eeeeec;
  margin-bottom: .4em;
  padding-left: 3px;
  color: #2e3436;
}

div.sidebar li.toctree-l2 a,
aside.sidebar li.toctree-l2 a {
  background-color: transparent;
  border: none;
  margin-left: 1em;
  border-bottom: 1px solid #dddddd;
}

div.sidebar li.toctree-l3 a,
aside.sidebar li.toctree-l3 a {
  background-color: transparent;
  border: none;
  margin-left: 2em;
  border-bottom: 1px solid #dddddd;
}

div.sidebar li.toctree-l2:last-child a,
aside.sidebar li.toctree-l2:last-child a {
  border-bottom: none;
}

div.sidebar li.toctree-l1.current a,
aside.sidebar li.toctree-l1.current a {
  border-right: 5px solid {{ theme_headerlinkcolor }};
}

div.sidebar li.toctree-l1.current li.toctree-l2 a,
aside.sidebar li.toctree-l1.current li.toctree-l2 a {
  border-right: none;
}

div.sidebar input[type="text"],
aside.sidebar input[type="text"] {
  width: 170px;
}

div.sidebar input[type="submit"],
aside.sidebar input[type="submit"] {
  width: 30px;
}


/* Footer */

div.footer-wrapper {
  background: {{ theme_footerbg }};
  border-top: 4px solid #babdb6;
  padding-top: 10px;
  padding-bottom: 10px;
  min-height: 80px;
}

div.footer, div.footer a {
  color: #888a85;
}

div.footer .right {
  text-align: right;
}

div.footer .left {
  text-transform: uppercase;
}


/* Styles copied from basic theme */

img.align-left, figure.align-left, .figure.align-left, object.align-left {
    clear: left;
    float: left;
    margin-right: 1em;
}

img.align-right, figure.align-right, .figure.align-right, object.align-right {
    clear: right;
    float: right;
    margin-left: 1em;
}

img.align-center, figure.align-center, .figure.align-center, object.align-center {
  display: block;
  margin-left: auto;
  margin-right: auto;
}

img.align-default, figure.align-default, .figure.align-default {
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.align-left {
    text-align: left;
}

.align-center {
    text-align: center;
}

.align-right {
    text-align: right;
}

table caption span.caption-number {
    font-style: italic;
}

table caption span.caption-text {
}

div.figure p.caption span.caption-number,
figcaption span.caption-number {
    font-style: italic;
}

div.figure p.caption span.caption-text,
figcaption span.caption-text {
}

/* -- search page ----------------------------------------------------------- */

ul.search {
    margin: 10px 0 0 20px;
    padding: 0;
}

ul.search li {
    padding: 5px 0 5px 20px;
    background-image: url(file.png);
    background-repeat: no-repeat;
    background-position: 0 7px;
}

ul.search li a {
    font-weight: bold;
}

ul.search li div.context {
    color: #888;
    margin: 2px 0 0 30px;
    text-align: left;
}

ul.keywordmatches li.goodmatch a {
    font-weight: bold;
}

/* -- index page ------------------------------------------------------------ */

table.contentstable {
    width: 90%;
}

table.contentstable p.biglink {
    line-height: 150%;
}

a.biglink {
    font-size: 1.3em;
}

span.linkdescr {
    font-style: italic;
    padding-top: 5px;
    font-size: 90%;
}

/* -- general index --------------------------------------------------------- */

table.indextable td {
    text-align: left;
    vertical-align: top;
}

table.indextable ul {
    margin-top: 0;
    margin-bottom: 0;
    list-style-type: none;
}

table.indextable > tbody > tr > td > ul {
    padding-left: 0em;
}

table.indextable tr.pcap {
    height: 10px;
}

table.indextable tr.cap {
    margin-top: 10px;
    background-color: #f2f2f2;
}

img.toggler {
    margin-right: 3px;
    margin-top: 3px;
    cursor: pointer;
}

/* -- domain module index --------------------------------------------------- */

table.modindextable td {
    padding: 2px;
    border-collapse: collapse;
}

/* -- viewcode extension ---------------------------------------------------- */

.viewcode-link {
    float: right;
}

.viewcode-back {
    float: right;
    font-family:: {{ theme_bodyfont }};
}

div.viewcode-block:target {
    margin: -1px -3px;
    padding: 0 3px;
    background-color: #f4debf;
    border-top: 1px solid #ac9;
    border-bottom: 1px solid #ac9;
}

div.code-block-caption {
    background-color: #ddd;
    color: #333;
    padding: 2px 5px;
    font-size: small;
}

/* -- math display ---------------------------------------------------------- */

div.body div.math p {
    text-align: center;
}

span.eqno {
    float: right;
}

<?xml version="1.0" encoding="UTF-8"?>
<package xmlns="http://www.idpf.org/2007/opf" version="{{ epub_version }}" xml:lang="{{ lang }}"
 unique-identifier="{{ uid }}"
 prefix="ibooks: http://vocabulary.itunes.apple.com/rdf/ibooks/vocabulary-extensions-1.0/">
  <metadata xmlns:opf="http://www.idpf.org/2007/opf"
        xmlns:dc="http://purl.org/dc/elements/1.1/">
    <dc:language>{{ lang }}</dc:language>
    <dc:title>{{ title }}</dc:title>
    <dc:description>{{ description }}</dc:description>
    <dc:creator>{{ author }}</dc:creator>
    <dc:contributor>{{ contributor }}</dc:contributor>
    <dc:publisher>{{ publisher }}</dc:publisher>
    <dc:rights>{{ copyright }}</dc:rights>
    {%- if epub_version == 3.1 %}
    <dc:identifier id="{{ uid }}" opf:scheme="{{ scheme }}">{{ id }}</dc:identifier>
    {%- else %}
    <dc:identifier id="{{ uid }}">{{ id }}</dc:identifier>
    {%- endif %}
    <dc:date>{{ date }}</dc:date>
    <meta property="dcterms:modified">{{ date }}</meta>
    <meta property="ibooks:version">{{ version }}</meta>
    <meta property="ibooks:specified-fonts">true</meta>
    <meta property="ibooks:binding">true</meta>
    <meta property="ibooks:scroll-axis">{{ ibook_scroll_axis }}</meta>
    {%- if cover %}
    <meta name="cover" content="{{ cover }}"/>
    {%- endif %}
  </metadata>
  <manifest>
    <item id="ncx" href="toc.ncx" media-type="application/x-dtbncx+xml" />
    <item id="nav" href="nav.xhtml" media-type="application/xhtml+xml" properties="nav"/>
    {%- for item in manifest_items %}
    <item id="{{ item.id }}" href="{{ item.href }}" media-type="{{ item.media_type }}" />
    {%- endfor %}
  </manifest>
  <spine toc="ncx" page-progression-direction="{{ page_progression_direction }}">
    {%- for spine in spines %}
    {%- if spine.linear %}
    <itemref idref="{{ spine.idref }}" />
    {%- else %}
    <itemref idref="{{ spine.idref }}" linear="no" />
    {%- endif %}
    {%- endfor %}
  </spine>
  <guide>
    {%- for guide in guides %}
    <reference type="{{ guide.type }}" title="{{ guide.title }}" href="{{ guide.uri }}" />
    {%- endfor %}
  </guide>
</package>

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
排产单管理模块
用于导入和管理排产单数据，支持从Excel和CSV文件导入
"""

import os
import re
import pandas as pd
import logging
from logging.handlers import TimedRotatingFileHandler

# 设置日志
logger = logging.getLogger("ProductionOrder")
if not logger.handlers:
    log_dir = os.path.join(os.getcwd(), "logs")
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    handler = TimedRotatingFileHandler(
        os.path.join(log_dir, "production_order.log"),
        when="midnight",
        backupCount=7,
        encoding="utf-8"
    )
    formatter = logging.Formatter('%(asctime)s %(levelname)s: %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)

class ProductionOrder:
    """排产单数据模型"""
    def __init__(self):
        self.orders = []  # 存储所有排产单记录
        self.current_file = None  # 当前加载的文件路径

    def import_from_file(self, file_path):
        """从文件导入排产单数据"""
        try:
            self.current_file = file_path
            file_ext = os.path.splitext(file_path)[1].lower()

            # 初始化DataFrame
            df = pd.DataFrame()

            if file_ext == '.csv':
                # 尝试不同的编码和分隔符
                encodings = ['utf-8', 'gbk', 'gb2312']
                separators = [',', ';', '\t']

                for encoding in encodings:
                    for sep in separators:
                        try:
                            df = pd.read_csv(file_path, encoding=encoding, sep=sep)
                            if not df.empty:
                                break
                        except Exception:
                            continue
                    if not df.empty:
                        break
            elif file_ext in ['.xlsx', '.xls']:
                try:
                    # 对于.xlsx文件，使用openpyxl引擎
                    if file_ext == '.xlsx':
                        df = pd.read_excel(file_path, engine='openpyxl')
                    # 对于.xls文件，使用xlrd引擎
                    else:
                        df = pd.read_excel(file_path, engine='xlrd')
                except Exception as e:
                    logger.error(f"读取Excel文件错误: {str(e)}")
                    return False, f"读取Excel文件错误: {str(e)}"
            else:
                logger.error(f"不支持的文件格式: {file_ext}")
                return False, f"不支持的文件格式: {file_ext}"

            # 检查是否成功读取数据
            if df.empty:
                logger.error("导入的文件没有数据")
                return False, "导入的文件没有数据"

            # 尝试识别必要的列
            required_columns = ['代码', '合同号', '做法', '台数']  # 必需的列
            optional_columns = ['型号']  # 可选的列
            column_mapping = self._identify_columns(df, required_columns + optional_columns)

            # 检查必需的列是否都存在
            required_cols = ['代码', '合同号', '做法', '台数']  # 必需的列
            if not all(col in column_mapping for col in required_cols):
                missing = [col for col in required_cols if col not in column_mapping]
                logger.error(f"缺少必要的列: {', '.join(missing)}")
                return False, f"缺少必要的列: {', '.join(missing)}"

            # 型号列是可选的，如果不存在，添加一个空映射
            if '型号' not in column_mapping:
                print("型号列不存在，将使用空值")
                column_mapping['型号'] = None

            # 提取数据
            self.orders = []
            for _, row in df.iterrows():
                # 获取型号，如果不存在则使用空字符串
                model = ""
                if '型号' in column_mapping and column_mapping['型号'] is not None:
                    try:
                        if pd.notna(row[column_mapping['型号']]):
                            model = str(row[column_mapping['型号']])
                    except Exception as e:
                        print(f"获取型号时出错: {e}")
                # 打印调试信息
                print(f"型号字段: {model}, 列映射: {'型号' in column_mapping and column_mapping['型号'] is not None}")

                order = {
                    'code': str(row[column_mapping['代码']]),
                    'contract': str(row[column_mapping['合同号']]),
                    'process': str(row[column_mapping['做法']]),
                    'model': model,  # 添加型号字段
                    'quantity': int(row[column_mapping['台数']]) if pd.notna(row[column_mapping['台数']]) else 0,
                    'remaining': int(row[column_mapping['台数']]) if pd.notna(row[column_mapping['台数']]) else 0,
                    'parameters': self._extract_parameters(str(row[column_mapping['做法']]))
                }
                self.orders.append(order)

            logger.info(f"成功从 {file_path} 导入 {len(self.orders)} 条排产单记录")
            return True, f"成功导入 {len(self.orders)} 条排产单记录"

        except Exception as e:
            logger.error(f"导入排产单时发生错误: {str(e)}")
            return False, f"导入排产单时发生错误: {str(e)}"

    def _identify_columns(self, df, required_columns):
        """识别数据框中的必要列"""
        column_mapping = {}

        # 打印所有列名，用于调试
        print(f"数据框列名: {list(df.columns)}")

        # 直接匹配
        for col in required_columns:
            if col in df.columns:
                column_mapping[col] = col
                print(f"直接匹配到列: {col}")

        # 如果没有直接匹配，尝试模糊匹配
        for req_col in required_columns:
            if req_col in column_mapping:
                continue

            for col in df.columns:
                # 转换为字符串进行比较
                col_str = str(col)
                if req_col in col_str or col_str in req_col:
                    column_mapping[req_col] = col
                    print(f"模糊匹配到列: {req_col} -> {col}")
                    break

        # 打印最终的列映射
        print(f"最终列映射: {column_mapping}")
        return column_mapping

    def _extract_parameters(self, process_str):
        """从做法字符串中提取加工参数"""
        parameters = {
            'length': None,
            'increment': None,
            'roots': None,  # 添加根数参数
            'copper_spec': None  # 添加铜带规格参数
        }

        # 打印调试信息
        print(f"正在从做法中提取参数: {process_str}")

        # 提取铜带规格 - 查找一次：后面的规格信息
        copper_spec_match = re.search(r'一次：\s*(\d+×\d+)', process_str)
        if copper_spec_match:
            try:
                parameters['copper_spec'] = copper_spec_match.group(1)
                print(f"提取到铜带规格: {parameters['copper_spec']}")
            except Exception:
                pass

        # 提取长度参数 - 新方法
        # 查找"长"后面的数字
        length_match = re.search(r'长\s*(\d+\.?\d*)', process_str)
        if length_match:
            try:
                parameters['length'] = float(length_match.group(1))
                print(f"提取到长度: {parameters['length']}")
            except ValueError:
                pass

        # 如果上面的方法没有找到长度，尝试其他模式
        if parameters['length'] is None:
            # 尝试查找格式如 "长度：100" 或 "L=100" 的模式
            length_patterns = [
                r'长度[：:]\s*(\d+\.?\d*)',
                r'长[：:]\s*(\d+\.?\d*)',
                r'L[：:=]\s*(\d+\.?\d*)',
                r'(\d+\.?\d*)\s*mm'
            ]

            for pattern in length_patterns:
                match = re.search(pattern, process_str)
                if match:
                    try:
                        parameters['length'] = float(match.group(1))
                        print(f"通过模式提取到长度: {parameters['length']}")
                        break
                    except ValueError:
                        continue

        # 提取递增量参数 - 新方法
        # 查找"+"后面的数字
        increment_match = re.search(r'\+\s*(\d+\.?\d*)', process_str)
        if increment_match:
            try:
                parameters['increment'] = float(increment_match.group(1))
                print(f"提取到递增量: {parameters['increment']}")
            except ValueError:
                pass

        # 如果上面的方法没有找到递增量，尝试其他模式
        if parameters['increment'] is None:
            # 尝试查找格式如 "递增：10" 或 "增量=10" 的模式
            increment_patterns = [
                r'递增[量]?[：:]\s*(\d+\.?\d*)',
                r'增量[：:]\s*(\d+\.?\d*)',
                r'增[：:]\s*(\d+\.?\d*)',
                r'[递增][：:=]\s*(\d+\.?\d*)'
            ]

            for pattern in increment_patterns:
                match = re.search(pattern, process_str)
                if match:
                    try:
                        parameters['increment'] = float(match.group(1))
                        print(f"通过模式提取到递增量: {parameters['increment']}")
                        break
                    except ValueError:
                        continue

        # 提取根数参数
        # 查找格式如 "4根" 或 "1×25×4根" 的模式
        roots_patterns = [
            r'(\d+)\s*根',
            r'×\s*(\d+)\s*根',
            r'x\s*(\d+)\s*根',
            r'[×x]\s*(\d+)\s*[根条]'
        ]

        for pattern in roots_patterns:
            match = re.search(pattern, process_str)
            if match:
                try:
                    parameters['roots'] = int(match.group(1))
                    print(f"提取到根数: {parameters['roots']}")
                    break
                except ValueError:
                    continue

        # 打印最终提取的参数
        print(f"最终提取的参数: {parameters}")
        return parameters

    def get_orders(self):
        """获取所有排产单记录"""
        return self.orders

    def get_order_by_index(self, index):
        """根据索引获取排产单记录"""
        if 0 <= index < len(self.orders):
            return self.orders[index]
        return None

    def update_quantity(self, index, new_quantity):
        """更新排产单的台数"""
        if 0 <= index < len(self.orders):
            self.orders[index]['quantity'] = new_quantity
            self.orders[index]['remaining'] = new_quantity
            return True
        return False

    def update_remaining(self, index, completed):
        """更新排产单的剩余台数"""
        if 0 <= index < len(self.orders):
            remaining = self.orders[index]['remaining'] - completed
            self.orders[index]['remaining'] = max(0, remaining)
            return self.orders[index]['remaining']
        return None

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PLC反馈数据测试程序
验证脉冲数据的正确转换
"""

import sys
import math
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

class PLCFeedbackTest(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("PLC反馈数据测试程序")
        self.setGeometry(100, 100, 800, 600)
        
        # 硬件参数
        self.wheel_diameter = 48.0  # mm
        self.pulses_per_revolution = 2000
        self.wheel_circumference = math.pi * self.wheel_diameter
        self.pulses_per_mm = self.pulses_per_revolution / self.wheel_circumference
        
        self.init_ui()
        
    def init_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title = QLabel("PLC反馈数据测试程序")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 18px; font-weight: bold; padding: 10px;")
        layout.addWidget(title)
        
        # 说明
        info_label = QLabel("PLC反馈给程序的数据都是脉冲数（除了数量），需要转换为长度显示")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("color: #7f8c8d; font-size: 12px; padding: 5px;")
        layout.addWidget(info_label)
        
        # 硬件参数显示
        params_group = QGroupBox("硬件参数")
        params_layout = QGridLayout()
        
        params_layout.addWidget(QLabel("滚轮直径:"), 0, 0)
        params_layout.addWidget(QLabel(f"{self.wheel_diameter} mm"), 0, 1)
        
        params_layout.addWidget(QLabel("每圈脉冲数:"), 1, 0)
        params_layout.addWidget(QLabel(f"{self.pulses_per_revolution} 脉冲"), 1, 1)
        
        params_layout.addWidget(QLabel("每毫米脉冲数:"), 2, 0)
        params_layout.addWidget(QLabel(f"{self.pulses_per_mm:.6f} 脉冲/mm"), 2, 1)
        
        params_group.setLayout(params_layout)
        layout.addWidget(params_group)
        
        # 模拟PLC反馈数据
        feedback_group = QGroupBox("模拟PLC反馈数据")
        feedback_layout = QGridLayout()
        
        # 当前长度脉冲数
        feedback_layout.addWidget(QLabel("当前长度脉冲数:"), 0, 0)
        self.current_pulse_spin = QSpinBox()
        self.current_pulse_spin.setRange(0, 999999)
        self.current_pulse_spin.setValue(13263)  # 对应1000mm
        self.current_pulse_spin.valueChanged.connect(self.update_conversions)
        feedback_layout.addWidget(self.current_pulse_spin, 0, 1)
        
        feedback_layout.addWidget(QLabel("转换为长度:"), 0, 2)
        self.current_length_label = QLabel("0.000 mm")
        self.current_length_label.setStyleSheet("font-weight: bold; color: #27ae60;")
        feedback_layout.addWidget(self.current_length_label, 0, 3)
        
        # 已完成数量（这个不需要转换）
        feedback_layout.addWidget(QLabel("已完成数量:"), 1, 0)
        self.completed_count_spin = QSpinBox()
        self.completed_count_spin.setRange(0, 9999)
        self.completed_count_spin.setValue(5)
        feedback_layout.addWidget(self.completed_count_spin, 1, 1)
        
        feedback_layout.addWidget(QLabel("显示:"), 1, 2)
        self.completed_count_label = QLabel("5 根")
        self.completed_count_label.setStyleSheet("font-weight: bold; color: #3498db;")
        feedback_layout.addWidget(self.completed_count_label, 1, 3)
        
        # 机器状态（这个也不需要转换）
        feedback_layout.addWidget(QLabel("机器状态码:"), 2, 0)
        self.status_spin = QSpinBox()
        self.status_spin.setRange(0, 4)
        self.status_spin.setValue(1)
        self.status_spin.valueChanged.connect(self.update_status_display)
        feedback_layout.addWidget(self.status_spin, 2, 1)
        
        feedback_layout.addWidget(QLabel("状态显示:"), 2, 2)
        self.status_label = QLabel("运行中")
        self.status_label.setStyleSheet("font-weight: bold; color: #e74c3c;")
        feedback_layout.addWidget(self.status_label, 2, 3)
        
        feedback_group.setLayout(feedback_layout)
        layout.addWidget(feedback_group)
        
        # 对比测试
        compare_group = QGroupBox("新旧处理方式对比")
        compare_layout = QGridLayout()
        
        # 输入脉冲数
        compare_layout.addWidget(QLabel("PLC反馈脉冲数:"), 0, 0)
        self.test_pulse_spin = QSpinBox()
        self.test_pulse_spin.setRange(0, 999999)
        self.test_pulse_spin.setValue(13263)
        self.test_pulse_spin.valueChanged.connect(self.update_comparison)
        compare_layout.addWidget(self.test_pulse_spin, 0, 1)
        
        # 旧的错误处理方式
        compare_layout.addWidget(QLabel("旧方式 (÷100):"), 1, 0)
        self.old_method_label = QLabel("132.63 mm")
        self.old_method_label.setStyleSheet("font-weight: bold; color: #e74c3c;")
        compare_layout.addWidget(self.old_method_label, 1, 1)
        
        compare_layout.addWidget(QLabel("❌ 错误"), 1, 2)
        
        # 新的正确处理方式
        compare_layout.addWidget(QLabel("新方式 (脉冲转换):"), 2, 0)
        self.new_method_label = QLabel("1000.00 mm")
        self.new_method_label.setStyleSheet("font-weight: bold; color: #27ae60;")
        compare_layout.addWidget(self.new_method_label, 2, 1)
        
        compare_layout.addWidget(QLabel("✅ 正确"), 2, 2)
        
        compare_group.setLayout(compare_layout)
        layout.addWidget(compare_group)
        
        # 常用脉冲数对照表
        table_group = QGroupBox("常用脉冲数对照表")
        table_layout = QVBoxLayout()
        
        self.pulse_table = QTableWidget()
        self.pulse_table.setColumnCount(4)
        self.pulse_table.setHorizontalHeaderLabels(["长度(mm)", "脉冲数", "旧方式显示", "新方式显示"])
        self.pulse_table.setMaximumHeight(200)
        
        # 填充对照表
        test_lengths = [100, 200, 500, 1000, 1500, 2000]
        self.pulse_table.setRowCount(len(test_lengths))
        
        for i, length in enumerate(test_lengths):
            pulses = int(round(length * self.pulses_per_mm))
            old_display = pulses / 100.0
            new_display = pulses / self.pulses_per_mm
            
            self.pulse_table.setItem(i, 0, QTableWidgetItem(f"{length}"))
            self.pulse_table.setItem(i, 1, QTableWidgetItem(f"{pulses}"))
            self.pulse_table.setItem(i, 2, QTableWidgetItem(f"{old_display:.2f} mm"))
            self.pulse_table.setItem(i, 3, QTableWidgetItem(f"{new_display:.2f} mm"))
        
        self.pulse_table.resizeColumnsToContents()
        table_layout.addWidget(self.pulse_table)
        
        table_group.setLayout(table_layout)
        layout.addWidget(table_group)
        
        # 初始更新
        self.update_conversions()
        self.update_comparison()
        
    def pulses_to_length(self, pulses):
        """脉冲数转换为长度"""
        if pulses <= 0:
            return 0.0
        return pulses / self.pulses_per_mm
    
    def update_conversions(self):
        """更新转换显示"""
        try:
            current_pulses = self.current_pulse_spin.value()
            current_length = self.pulses_to_length(current_pulses)
            self.current_length_label.setText(f"{current_length:.3f} mm")
            
            completed_count = self.completed_count_spin.value()
            self.completed_count_label.setText(f"{completed_count} 根")
            
        except Exception as e:
            print(f"更新转换显示时发生错误: {e}")
    
    def update_status_display(self):
        """更新状态显示"""
        try:
            status_code = self.status_spin.value()
            status_map = {
                0: ("待机", "#d35400"),
                1: ("运行中", "#27ae60"),
                2: ("已停止", "#e74c3c"),
                3: ("错误", "#e74c3c"),
                4: ("复位中", "#f39c12")
            }
            
            status_text, color = status_map.get(status_code, ("未知", "#7f8c8d"))
            self.status_label.setText(status_text)
            self.status_label.setStyleSheet(f"font-weight: bold; color: {color};")
            
        except Exception as e:
            print(f"更新状态显示时发生错误: {e}")
    
    def update_comparison(self):
        """更新对比显示"""
        try:
            test_pulses = self.test_pulse_spin.value()
            
            # 旧的错误方式：直接除以100
            old_result = test_pulses / 100.0
            self.old_method_label.setText(f"{old_result:.2f} mm")
            
            # 新的正确方式：脉冲转换
            new_result = self.pulses_to_length(test_pulses)
            self.new_method_label.setText(f"{new_result:.2f} mm")
            
        except Exception as e:
            print(f"更新对比显示时发生错误: {e}")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # 设置应用程序样式
    app.setStyle(QStyleFactory.create('Fusion'))
    
    window = PLCFeedbackTest()
    window.show()
    
    print("PLC反馈数据测试程序已启动")
    print("这个程序演示了PLC反馈脉冲数据的正确处理方式")
    
    sys.exit(app.exec_())

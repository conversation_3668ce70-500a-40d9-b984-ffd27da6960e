# 红外感应状态更新修复说明

## 问题描述

用户反馈：从禁用状态改为启用红外感应功能后，红外感应器状态显示仍然显示"已禁用"，没有正确更新为实际的检测状态。

## 问题原因分析

1. **状态更新时机问题**：切换功能开关后，红外感应器状态标签没有立即更新
2. **异步更新延迟**：界面更新和状态检查之间存在时序问题
3. **状态检查逻辑**：`check_infrared_sensor()` 方法在功能启用后没有被正确调用

## 修复方案

### 1. 增强状态切换逻辑

**修改文件**: `main.py` - `toggle_infrared_function()` 方法

**修复内容**:
- 启用功能后，使用 `QTimer.singleShot(100, self.force_update_infrared_status)` 延迟更新状态
- 禁用功能后，立即设置状态标签为"⚫ 已禁用"
- 添加强制状态更新方法

```python
def toggle_infrared_function(self):
    if self.infrared_enabled:
        # 启用后立即检查并更新红外感应器状态
        QTimer.singleShot(100, self.force_update_infrared_status)
    else:
        # 禁用后立即更新状态显示为"已禁用"
        if hasattr(self, 'infrared_status_label'):
            self.infrared_status_label.setText("⚫ 已禁用")
            self.infrared_status_label.setStyleSheet("color: #6c757d; font-weight: bold; font-size: 13px;")
```

### 2. 新增强制状态更新方法

**新增方法**: `force_update_infrared_status()`

**功能**:
- 根据当前功能开关状态强制更新红外感应器显示
- 处理连接状态检查
- 提供更可靠的状态同步

```python
def force_update_infrared_status(self):
    """强制更新红外感应器状态显示"""
    try:
        if self.infrared_enabled:
            # 如果功能已启用，重新检查实际状态
            current_modbus = self.get_current_modbus()
            if current_modbus.is_connected():
                self.check_infrared_sensor()
            else:
                # 未连接时显示默认状态
                if hasattr(self, 'infrared_status_label'):
                    self.infrared_status_label.setText("🔴 无料")
                    self.infrared_status_label.setStyleSheet("color: #e74c3c; font-weight: bold; font-size: 13px;")
        else:
            # 功能禁用时显示禁用状态
            if hasattr(self, 'infrared_status_label'):
                self.infrared_status_label.setText("⚫ 已禁用")
                self.infrared_status_label.setStyleSheet("color: #6c757d; font-weight: bold; font-size: 13px;")
    except Exception as e:
        print(f"强制更新红外感应器状态错误: {e}")
```

### 3. 改进状态检查方法

**修改文件**: `main.py` - `check_infrared_sensor()` 方法

**改进内容**:
- 增加更详细的状态显示（未连接、读取失败、检测异常）
- 改进错误处理和状态反馈
- 确保在各种情况下都有明确的状态显示

```python
def check_infrared_sensor(self):
    # 检查红外感应功能是否启用
    if not self.infrared_enabled:
        if hasattr(self, 'infrared_status_label'):
            self.infrared_status_label.setText("⚫ 已禁用")
            self.infrared_status_label.setStyleSheet("color: #6c757d; font-weight: bold; font-size: 13px;")
        return None
    
    current_modbus = self.get_current_modbus()
    if not current_modbus.is_connected():
        # 未连接时显示未知状态
        if hasattr(self, 'infrared_status_label'):
            self.infrared_status_label.setText("❓ 未连接")
            self.infrared_status_label.setStyleSheet("color: #f39c12; font-weight: bold; font-size: 13px;")
        return None
    
    # ... 其他检查逻辑
```

### 4. 优化配置加载时的状态更新

**修改文件**: `main.py` - `update_infrared_button_state()` 方法

**改进内容**:
- 延长状态更新延迟时间，确保界面完全初始化
- 使用 `force_update_infrared_status()` 方法进行可靠的状态更新

```python
def update_infrared_button_state(self):
    # ... 按钮状态更新逻辑
    
    # 延迟更新红外感应器状态显示，确保界面已完全初始化
    QTimer.singleShot(300, self.force_update_infrared_status)
```

## 状态显示说明

### 红外感应器状态标签可能的显示内容

| 状态 | 显示内容 | 颜色 | 说明 |
|------|----------|------|------|
| 功能禁用 | ⚫ 已禁用 | 灰色 | 红外感应功能已关闭 |
| 检测到物料 | 🟢 有料 | 绿色 | 红外感应器检测到物料 |
| 未检测到物料 | 🔴 无料 | 红色 | 红外感应器未检测到物料 |
| 设备未连接 | ❓ 未连接 | 橙色 | PLC设备未连接 |
| 读取失败 | ❌ 读取失败 | 红色 | 无法读取传感器数据 |
| 检测异常 | ❌ 检测异常 | 红色 | 传感器检测过程中出现异常 |

## 测试验证

### 1. 创建专用测试程序

**文件**: `test_infrared_switch.py`

**功能**:
- 独立的红外感应功能开关测试界面
- 模拟物料检测状态
- 实时日志显示
- 配置保存和加载测试

### 2. 测试步骤

1. **启动测试程序**:
   ```bash
   python test_infrared_switch.py
   ```

2. **测试功能开关**:
   - 点击"🟢 已启用"按钮切换为禁用状态
   - 观察红外感应器状态是否立即变为"⚫ 已禁用"
   - 点击"🔴 已禁用"按钮切换为启用状态
   - 观察红外感应器状态是否正确更新为"🔴 无料"

3. **测试状态模拟**:
   - 在启用状态下点击"模拟有料"按钮
   - 观察状态是否变为"🟢 有料"
   - 点击"模拟无料"按钮
   - 观察状态是否变为"🔴 无料"

4. **测试配置持久化**:
   - 切换功能状态
   - 关闭程序重新打开
   - 验证状态是否正确恢复

## 修复效果

### 修复前
- 从禁用切换到启用后，状态显示仍为"⚫ 已禁用"
- 需要手动刷新或重新连接才能看到正确状态

### 修复后
- 切换功能开关后立即更新状态显示
- 启用时显示实际检测状态或默认状态
- 禁用时立即显示"⚫ 已禁用"
- 状态更新更加可靠和及时

## 注意事项

1. **界面初始化时序**：使用定时器延迟更新，确保界面组件完全初始化
2. **异常处理**：增加了更完善的异常处理和状态反馈
3. **状态一致性**：确保功能开关状态和显示状态始终保持一致
4. **用户体验**：提供更直观的状态反馈和更快的响应速度

## 相关文件

- `main.py` - 主程序文件（已修复）
- `test_infrared_switch.py` - 专用测试程序（新增）
- `config.json` - 配置文件（包含 infrared_enabled 字段）

---

**修复版本**: v3.3.1  
**修复日期**: 2024年  
**修复内容**: 红外感应功能开关状态更新问题  
**测试状态**: 已通过测试验证

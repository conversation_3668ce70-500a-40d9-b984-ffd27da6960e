/*
:Author: <PERSON>
:Contact: <EMAIL>
:date: $Date: 2022-01-29 23:26:10 +0100 (Sa, 29. Jän 2022) $
:version: $Revision: 8995 $
:copyright: This stylesheet has been placed in the public domain.

Default cascading style sheet for the PEP HTML output of Docutils.
*/

/* "! important" is used here to override other ``margin-top`` and
   ``margin-bottom`` styles that are later in the stylesheet or
   more specific.  See http://www.w3.org/TR/CSS1#the-cascade */
.first {
  margin-top: 0 ! important }

.last, .with-subtitle {
  margin-bottom: 0 ! important }

.hidden {
  display: none }

.navigation {
  width: 100% ;
  background: #99ccff ;
  margin-top: 0px ;
  margin-bottom: 0px }

.navigation .navicon {
  width: 150px ;
  height: 35px }

.navigation .textlinks {
  padding-left: 1em ;
  text-align: left }

.navigation td, .navigation th {
  padding-left: 0em ;
  padding-right: 0em ;
  vertical-align: middle }

.rfc2822 {
  margin-top: 0.5em ;
  margin-left: 0.5em ;
  margin-right: 0.5em ;
  margin-bottom: 0em }

.rfc2822 td {
  text-align: left }

.rfc2822 th.field-name {
  text-align: right ;
  font-family: sans-serif ;
  padding-right: 0.5em ;
  font-weight: bold ;
  margin-bottom: 0em }

a.toc-backref {
  text-decoration: none ;
  color: black }

blockquote.epigraph {
  margin: 2em 5em ; }

body {
  margin: 0px ;
  margin-bottom: 1em ;
  padding: 0px }

dl.docutils dd {
  margin-bottom: 0.5em }

div.section {
  margin-left: 1em ;
  margin-right: 1em ;
  margin-bottom: 1.5em }

div.section div.section {
  margin-left: 0em ;
  margin-right: 0em ;
  margin-top: 1.5em }

div.abstract {
  margin: 2em 5em }

div.abstract p.topic-title {
  font-weight: bold ;
  text-align: center }

div.admonition, div.attention, div.caution, div.danger, div.error,
div.hint, div.important, div.note, div.tip, div.warning {
  margin: 2em ;
  border: medium outset ;
  padding: 1em }

div.admonition p.admonition-title, div.hint p.admonition-title,
div.important p.admonition-title, div.note p.admonition-title,
div.tip p.admonition-title {
  font-weight: bold ;
  font-family: sans-serif }

div.attention p.admonition-title, div.caution p.admonition-title,
div.danger p.admonition-title, div.error p.admonition-title,
div.warning p.admonition-title {
  color: red ;
  font-weight: bold ;
  font-family: sans-serif }

/* Uncomment (and remove this text!) to get reduced vertical space in
   compound paragraphs.
div.compound .compound-first, div.compound .compound-middle {
  margin-bottom: 0.5em }

div.compound .compound-last, div.compound .compound-middle {
  margin-top: 0.5em }
*/

div.dedication {
  margin: 2em 5em ;
  text-align: center ;
  font-style: italic }

div.dedication p.topic-title {
  font-weight: bold ;
  font-style: normal }

div.figure {
  margin-left: 2em ;
  margin-right: 2em }

div.footer, div.header {
  clear: both;
  font-size: smaller }

div.footer {
  margin-left: 1em ;
  margin-right: 1em }

div.line-block {
  display: block ;
  margin-top: 1em ;
  margin-bottom: 1em }

div.line-block div.line-block {
  margin-top: 0 ;
  margin-bottom: 0 ;
  margin-left: 1.5em }

div.sidebar {
  margin-left: 1em ;
  border: medium outset ;
  padding: 1em ;
  background-color: #ffffee ;
  width: 40% ;
  float: right ;
  clear: right }

div.sidebar p.rubric {
  font-family: sans-serif ;
  font-size: medium }

div.system-messages {
  margin: 5em }

div.system-messages h1 {
  color: red }

div.system-message {
  border: medium outset ;
  padding: 1em }

div.system-message p.system-message-title {
  color: red ;
  font-weight: bold }

div.topic {
  margin: 2em }

h1.section-subtitle, h2.section-subtitle, h3.section-subtitle,
h4.section-subtitle, h5.section-subtitle, h6.section-subtitle {
  margin-top: 0.4em }

h1 {
  font-family: sans-serif ;
  font-size: large }

h2 {
  font-family: sans-serif ;
  font-size: medium }

h3 {
  font-family: sans-serif ;
  font-size: small }

h4 {
  font-family: sans-serif ;
  font-style: italic ;
  font-size: small }

h5 {
  font-family: sans-serif;
  font-size: x-small }

h6 {
  font-family: sans-serif;
  font-style: italic ;
  font-size: x-small }

hr.docutils {
  width: 75% }

img.align-left {
  clear: left }

img.align-right {
  clear: right }

img.borderless {
  border: 0 }

ol.simple, ul.simple {
  margin-bottom: 1em }

ol.arabic {
  list-style: decimal }

ol.loweralpha {
  list-style: lower-alpha }

ol.upperalpha {
  list-style: upper-alpha }

ol.lowerroman {
  list-style: lower-roman }

ol.upperroman {
  list-style: upper-roman }

p.attribution {
  text-align: right ;
  margin-left: 50% }

p.caption {
  font-style: italic }

p.credits {
  font-style: italic ;
  font-size: smaller }

p.label {
  white-space: nowrap }

p.rubric {
  font-weight: bold ;
  font-size: larger ;
  color: maroon ;
  text-align: center }

p.sidebar-title {
  font-family: sans-serif ;
  font-weight: bold ;
  font-size: larger }

p.sidebar-subtitle {
  font-family: sans-serif ;
  font-weight: bold }

p.topic-title {
  font-family: sans-serif ;
  font-weight: bold }

pre.address {
  margin-bottom: 0 ;
  margin-top: 0 ;
  font-family: serif ;
  font-size: 100% }

pre.literal-block, pre.doctest-block {
  margin-left: 2em ;
  margin-right: 2em }

span.classifier {
  font-family: sans-serif ;
  font-style: oblique }

span.classifier-delimiter {
  font-family: sans-serif ;
  font-weight: bold }

span.interpreted {
  font-family: sans-serif }

span.option {
  white-space: nowrap }

span.option-argument {
  font-style: italic }

span.pre {
  white-space: pre }

span.problematic {
  color: red }

span.section-subtitle {
  /* font-size relative to parent (h1..h6 element) */
  font-size: 80% }

table.citation {
  border-left: solid 1px gray;
  margin-left: 1px }

table.docinfo {
  margin: 2em 4em }

table.docutils {
  margin-top: 0.5em ;
  margin-bottom: 0.5em }

table.footnote {
  border-left: solid 1px black;
  margin-left: 1px }

table.docutils td, table.docutils th,
table.docinfo td, table.docinfo th {
  padding-left: 0.5em ;
  padding-right: 0.5em ;
  vertical-align: top }

td.num {
  text-align: right }

th.field-name {
  font-weight: bold ;
  text-align: left ;
  white-space: nowrap ;
  padding-left: 0 }

h1 tt.docutils, h2 tt.docutils, h3 tt.docutils,
h4 tt.docutils, h5 tt.docutils, h6 tt.docutils {
  font-size: 100% }

ul.auto-toc {
  list-style-type: none }

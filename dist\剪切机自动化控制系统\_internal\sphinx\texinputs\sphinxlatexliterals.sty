%% LITERAL BLOCKS
%
% change this info string if making any custom modification
\ProvidesPackage{sphinxlatexliterals}[2024/07/01 v7.4.0 code-blocks and parsed literals]

% Provides support for this output mark-up from Sphinx latex writer:
%
% - macros:
%   - \sphinxLiteralBlockLabel
%   - \sphinxSetupCaptionForVerbatim
%   - \sphinxSetupCodeBlockInFootnote
%   - \sphinxhref
%   - \sphinxnolinkurl
%   - \sphinxresetverbatimhllines
%   - \sphinxunactivateextrasandspace
%   - \sphinxupquote
%   - \sphinxurl
%
% - environments:
%   - sphinxVerbatim
%   - sphinxVerbatimintable
%   - sphinxalltt
%
% Dependency:
%
% - hyperref (for \phantomsection and \capstart) (loaded later)
%
% Executes \RequirePackage for:
%
% - framed
% - fancyvrb
% - alltt
% - upquote
% - needspace
% - sphinxpackageboxes
\RequirePackage{sphinxpackageboxes}
% 7.4.0 removes unneeded usage of \spx@boxes@border

% also in sphinxlatexadmonitions.sty:
% This is a workaround to a "feature" of French lists, when literal block
% follows immediately; usable generally (does only \par then), a priori...
\providecommand*\sphinxvspacefixafterfrenchlists{%
   \ifvmode\ifdim\lastskip<\z@ \vskip\parskip\fi\else\par\fi
}

% For framing allowing pagebreaks
\RequirePackage{framed}
% For source code
% MEMO: fancyvrb is used mainly to
% 1- control horizontal and vertical spacing
% 2- optional line numbering
% 3- optional line emphasizing
% 4- while still allowing expansion of Pygments latex mark-up
% Other aspects such as framing, caption handling, codeline wrapping are
% added on top of it. We should stop using fancyvrb and implement
% 1, 2, 3, 4 by own Sphinx fully native Verbatim. This would greatly simplify
% in particular wrapping long code lines in a way allowing page breaks.
\RequirePackage{fancyvrb}
% For parsed-literal blocks.
\RequirePackage{alltt}
% Display "real" single quotes in literal blocks.
\RequirePackage{upquote}
% Skip to next page if not enough space at bottom
\RequirePackage{needspace}

% Based on use of "fancyvrb.sty"'s Verbatim.
% - with framing allowing page breaks ("framed.sty")
% - with breaking of long lines (exploits Pygments mark-up),
% - with possibly of a top caption, non-separable by pagebreak.
% - and usable inside tables or footnotes ("sphinxpackagefootnote.sty").

% for emphasizing lines
\define@key{FV}{hllines}{\def\sphinx@verbatim@checkifhl##1{\in@{, ##1,}{#1}}}
% sphinxVerbatim must be usable by third party without requiring hllines set-up
\def\sphinxresetverbatimhllines{\def\sphinx@verbatim@checkifhl##1{\in@false}}
\sphinxresetverbatimhllines

% Prior to Sphinx 1.5, \Verbatim and \endVerbatim were modified by Sphinx.
% The aliases defined here are used in sphinxVerbatim environment and can
% serve as hook-points with no need to modify \Verbatim itself.
\let\OriginalVerbatim   \Verbatim
\let\endOriginalVerbatim\endVerbatim

% for captions of literal blocks
% at start of caption title
\newcommand*{\fnum@literalblock}{\literalblockname\nobreakspace\theliteralblock}
% this will be overwritten in document preamble by Babel translation
\newcommand*{\literalblockname}{Listing }
% file extension needed for \caption's good functioning, the file is created
% only if a \listof{literalblock}{foo} command is encountered, which is
% analogous to \listoffigures, but for the code listings (foo = chosen title.)
\newcommand*{\ext@literalblock}{lol}

% if forced use of minipage encapsulation is needed (e.g. table cells)
\newif\ifsphinxverbatimwithminipage   \sphinxverbatimwithminipagefalse

% Framing macro for use with framed.sty's \FrameCommand
% MEMO: the sophisticated code in \spx@fcolorbox/\spx@CustomFBox
%       is here for good reasons
%       - be responsive to indented list environments in the manner of
%         the "framed" (\fbox) and "shaded" (\colorbox) environments of
%         framed.sty; indeed code here is an evolution related to \fcolorbox
%       - attach non-detachable continuation hints above/below frame
%       - draw the frame and fill the background color in a manner avoiding
%         problems in some pdf viewers
%       - do background coloring differently from color.sty/xcolor.sty macros
%         (even core internal ones) to work around issues at page breaks
%         as the framed contents are split into chunks with possibly unpaired
%         "color push" or "color pop"
% About the produced output:
%  - it obeys current indentation,
%  - frame with 4 padding parameters and 4 border-width parameters
%  - the contents use the full available text width, limited by indentation,
%  - #1 = will be typeset above frame, in a non detachable way,
%  - #2 = will be typeset below frame, in a non detachable way,
%  - #3 = will be typeset within the frame.
% #1 and #2 are expected to be already typeset \hbox'es.
% #3 are the contents, and in the context of usage of fancyvrb+framed,
% it will arrive here already transformed into horizontal boxes,
% interline penalties and glues.
%
\long\def\spx@verb@FrameCommand #1#2#3{%
  % The \spx@verb@boxes@fcolorbox@setup MUST have been executed beforehand.
  % These \hskips are for fancyvrb.sty measuring and will make the
  % framing "adapt" to an indented context.
  \hskip\@totalleftmargin
  \hskip-\spx@boxes@border@left\hskip-\spx@boxes@padding@left
  \spx@verb@fcolorbox {#1}{#2}{#3}%
  \hskip-\spx@boxes@padding@right\hskip-\spx@boxes@border@right
  \hskip-\linewidth \hskip-\@totalleftmargin \hskip\columnwidth
}%
\long\def\spx@verb@fcolorbox #1#2#3{%
  % The \spx@verb@boxes@fcolorbox@setup MUST have been executed beforehand.
%
  % MEMO: in the context of framed.sty this will always expand inside some
  % \hbox isolated from other code, so we can use \box\z@, \box\tw@,...
  % with no need of extra group.
%
  % MEMO: this code was originally using \color@b@x but the latter has
  % problematic features regarding color in a context like here where #3
  % may contain an unbalanced "color push".
%
  \setbox\z@\hbox{#3}%
  \edef\spx@verb@fcolorbox@width@sp
      {\number\dimexpr\wd\z@+\spx@boxes@border@left
                            +\spx@boxes@padding@left
                            +\spx@boxes@padding@right
                            +\spx@boxes@border@right\relax sp}%
  \vbox{#1% continuation hint attached above frame, uses \spx@verb@fcolorbox@width@sp
        % the boxes@fcolorbox constructs an \hbox with bbox containing the border
        % \spx@verb@boxes@fcolorbox@setup MUST have been executed beforehand.
        \spx@boxes@fcolorbox{\box\z@}%
        % This \nointerlineskip to maintain legacy spacing when a \hrule was
        % formerly last prior item in vertical list.  TODO: remove this at 6.0.0 ?
        \nointerlineskip
        #2% continuation hint attached below frame, uses \spx@verb@fcolorbox@width@sp
       }% end of \vbox
}%
\def\spx@verb@fcolorbox@put@c#1{% hide width from framed.sty measuring
    \moveright.5\dimexpr\spx@verb@fcolorbox@width@sp\hb@xt@\z@{\hss#1\hss}%
}%
\def\spx@verb@fcolorbox@put@r#1{% right align with contents, width hidden
    \moveright\dimexpr\spx@verb@fcolorbox@width@sp-%
                      \spx@boxes@padding@right-%
                      \spx@boxes@border@right\hb@xt@\z@{\hss#1}%
}%
\def\spx@verb@fcolorbox@put@l#1{% left align with contents, width hidden
    \moveright\dimexpr\spx@boxes@border@left+%
                      \spx@boxes@padding@left\hb@xt@\z@{#1\hss}%
}%
%
\def\sphinxVerbatim@Continued{%
    \csname spx@verb@fcolorbox@put@\spx@opt@verbatimcontinuedalign\endcsname
    {{\normalcolor\sphinxstylecodecontinued\literalblockcontinuedname}}%
}%
\def\sphinxVerbatim@Continues{%
    \csname spx@verb@fcolorbox@put@\spx@opt@verbatimcontinuesalign\endcsname
    {{\normalcolor\sphinxstylecodecontinues\literalblockcontinuesname}}%
}%
\def\sphinxVerbatim@Title{%
    \spx@verb@fcolorbox@put@c{\unhcopy\sphinxVerbatim@TitleBox}%
}%
\let\sphinxVerbatim@Before\@empty
\let\sphinxVerbatim@After\@empty
% Defaults are redefined in document preamble according to language
\newcommand*\literalblockcontinuedname{continued from previous page}%
\newcommand*\literalblockcontinuesname{continues on next page}%
%
\def\sphinxVerbatim@FrameCommand{%
    \spx@verb@FrameCommand\sphinxVerbatim@Before\sphinxVerbatim@After
}%
\def\sphinxVerbatim@FirstFrameCommand{%
    \ifspx@pre@border@open
      \spx@boxes@fcolorbox@setup@openbottom
    \fi
    \spx@verb@FrameCommand\sphinxVerbatim@Before\sphinxVerbatim@Continues
}%
\def\sphinxVerbatim@MidFrameCommand{%
    \ifspx@pre@border@open
      \spx@boxes@fcolorbox@setup@openboth
    \fi
    \spx@verb@FrameCommand\sphinxVerbatim@Continued\sphinxVerbatim@Continues
}%
\def\sphinxVerbatim@LastFrameCommand{%
    \ifspx@pre@border@open
      \spx@boxes@fcolorbox@setup@opentop
    \fi
    \spx@verb@FrameCommand\sphinxVerbatim@Continued\sphinxVerbatim@After
}%
%
\def\spx@verb@boxes@fcolorbox@setup{%
    % Prepares usage of \spx@boxes@fcolorbox
    % Extras to remap legacy color names VerbatimBorderColor and VerbatimColor
    % to a common naming scheme with admonitions (and topic directive), as
    % expected by \spx@boxes@fcolorbox@setup from sphinxpackageboxes.sty.
    \sphinxcolorlet{sphinxpreBorderColor}{VerbatimBorderColor}%
    \sphinxcolorlet{sphinxpreBgColor}{VerbatimColor}%
    % This VerbatimShadowColor is not a legacy name nor user documented but is
    % an outcome of sphinx.sty batch definitions for CSS option support.
    \sphinxcolorlet{sphinxpreShadowColor}{VerbatimShadowColor}%
    \spx@boxes@fcolorbox@setup{pre}%
    \ifspx@opt@verbatimwithframe
    \else
       \spx@boxes@border@top\z@
       \spx@boxes@border@right\z@
       \spx@boxes@border@bottom\z@
       \spx@boxes@border@left\z@
       % MEMO: rounded corners still make sense in presence of a background
       % color, so we do not force the fcolorbox@rectangle here
    \fi
}%

% For linebreaks inside Verbatim environment from package fancyvrb.
\newbox\sphinxcontinuationbox
\newbox\sphinxvisiblespacebox
\newcommand*\sphinxafterbreak {\copy\sphinxcontinuationbox}

% Take advantage of the already applied Pygments mark-up to insert
% potential linebreaks for TeX processing.
%        {, <, #, %, $, ' and ": go to next line.
%        _, }, ^, &, >, -, ~, and \: stay at end of broken line.
% Use of \textquotesingle for straight quote.
% FIXME: convert this to package options ?
\newcommand*\sphinxbreaksbeforelist {%
    \do\PYGZob\{\do\PYGZlt\<\do\PYGZsh\#\do\PYGZpc\%% {, <, #, %,
    \do\PYGZdl\$\do\PYGZdq\"% $, "
    \def\PYGZsq
     {\discretionary{}{\sphinxafterbreak\textquotesingle}{\textquotesingle}}% '
}
\newcommand*\sphinxbreaksafterlist {%
    \do\PYGZus\_\do\PYGZcb\}\do\PYGZca\^\do\PYGZam\&% _, }, ^, &,
    \do\PYGZgt\>\do\PYGZhy\-\do\PYGZti\~% >, -, ~
    \do\PYGZbs\\% \
}
\newcommand*\sphinxbreaksatspecials {%
  \def\do##1##2%
     {\def##1{\discretionary{}{\sphinxafterbreak\char`##2}{\char`##2}}}%
  \sphinxbreaksbeforelist
  \def\do##1##2%
     {\def##1{\discretionary{\char`##2}{\sphinxafterbreak}{\char`##2}}}%
  \sphinxbreaksafterlist
}

\def\sphinx@verbatim@nolig@list {\do \`}%
% Some characters . , ; ? ! / are neither pygmentized nor "tex-escaped".
% This macro makes them "active" and they will insert potential linebreaks.
% Not compatible with math mode (cf \sphinxunactivateextras, which uses
% these lists to make sure activated characters get de-activated).
\newcommand*\sphinxbreaksbeforeactivelist {}% none
\newcommand*\sphinxbreaksafteractivelist  {\do\.\do\,\do\;\do\?\do\!\do\/}
\newcommand*\sphinxbreaksviaactive {%
   \def\do##1{\lccode`\~`##1%
    \lowercase{\def~}{\discretionary{}{\sphinxafterbreak\char`##1}{\char`##1}}%
    \catcode`##1\active}%
   \sphinxbreaksbeforeactivelist
   \def\do##1{\lccode`\~`##1%
    \lowercase{\def~}{\discretionary{\char`##1}{\sphinxafterbreak}{\char`##1}}%
    \catcode`##1\active}%
   \sphinxbreaksafteractivelist
   \lccode`\~`\~
}

% If the linebreak is at a space, the latter will be displayed as visible
% space at end of first line, and a continuation symbol starts next line.
\def\spx@verbatim@space {%
     \nobreak\hskip\z@skip
     \discretionary{\copy\sphinxvisiblespacebox}{\sphinxafterbreak}
                   {\kern\fontdimen2\font}%
}%

% if the available space on page is less than \literalblockneedspace, insert pagebreak
\newcommand{\sphinxliteralblockneedspace}{5\baselineskip}
\newcommand{\sphinxliteralblockwithoutcaptionneedspace}{1.5\baselineskip}
% The title (caption) is specified from outside as macro \sphinxVerbatimTitle.
% \sphinxVerbatimTitle is reset to empty after each use of Verbatim.
\newcommand*\sphinxVerbatimTitle {}
% This box to typeset the caption before framed.sty multiple passes for framing.
\newbox\sphinxVerbatim@TitleBox
% This box to measure contents if nested as inner \MakeFramed requires then
% minipage encapsulation but too long contents then break outer \MakeFramed
\newbox\sphinxVerbatim@ContentsBox
% Holder macro for labels of literal blocks. Set-up by LaTeX writer.
\newcommand*\sphinxLiteralBlockLabel {}
\newcommand*\sphinxSetupCaptionForVerbatim [1]
{%
    \sphinxvspacefixafterfrenchlists
    \needspace{\sphinxliteralblockneedspace}%
% insert a \label via \sphinxLiteralBlockLabel
% reset to normal the color for the literal block caption
    \def\sphinxVerbatimTitle
       {\py@NormalColor\sphinxcaption{\sphinxLiteralBlockLabel #1}}%
}
\newcommand*\sphinxSetupCodeBlockInFootnote {%
  \fvset{fontsize=\footnotesize}\let\caption\sphinxfigcaption
  \sphinxverbatimwithminipagetrue % reduces vertical spaces
  % we counteract (this is in a group) the \@normalsize from \caption
  \let\normalsize\footnotesize\let\@parboxrestore\relax
  \def\spx@abovecaptionskip{\sphinxverbatimsmallskipamount}%
}
\newcommand*{\sphinxverbatimsmallskipamount}{\smallskipamount}
% serves to implement line highlighting
\newcommand\sphinxFancyVerbFormatLine[1]{%
  \expandafter\sphinx@verbatim@checkifhl\expandafter{\the\FV@CodeLineNo}%
  \ifin@
     \sphinxVerbatimHighlightLine{#1}%
  \else
     \sphinxVerbatimFormatLine{#1}%
  \fi
}%
\let\spx@original@set@color\set@color
\newcommand\sphinxVerbatimHighlightLine[1]{%
% This is morally a \colorbox (with a \fboxsep which would be 0pt)
% but some issues of potential colour disappearance at pagebreaks
% require workaround such as the one done here.
  \leavevmode
  % MEMO: usage of original \colorbox would insert a \set@color here
  %       and this then places a "color pop" at the end of the \box\z@.
  %       But this could pair erroneously with an unmatched "color push"
  %       as #1 is maybe only a part (already hboxed) of a codeline
  %       if (default) verbatimwrapslines=true
  %       (cf \spx@verb@@PreProcessLine; refs: #8686)
  % MEMO: formerly we did something with \fboxsep in relation to the LaTeX
  %       bug graphics/4524 for \colorbox, but as we don't use \colorbox...
  \setbox\z@\hb@xt@\linewidth{\strut#1\hss}%
  % MEMO: \colorbox would lead to \color{sphinxVerbatimHighlightColor}
  % plus \color@block, which results in doubled (a color.sty feature)
  % color command send to device driver and more importantly has
  % a "color pop" which will be after \box\z@.  We avoid that for reasons
  % mentioned above.
  {%
  \def\set@color{\let\set@color\spx@original@set@color}%
  % will only set \current@color and delay the \set@color to \color@block
  % as this all happens inside fancyvrb nested \hbox'es.
  \color{sphinxVerbatimHighlightColor}%
  % will use \current@color and pop it **before** \box\z@
  \color@block{\wd\z@}{\ht\z@}{\dp\z@}\box\z@
  }%
  % we added a group only for \FV@RightListNumber not be influenced by the
  % \current@color, if \fvset has been used to set numbers to the right.
}%
% MEMO: fancyvrb has options obeytabs and tabsize.  Anyhow tab characters
% do not make it to the tex file, they have been converted to spaces earlier.
% But, if this was not the case, the support would be implemented here via
% \newcommand\sphinxVerbatimFormatLine[1]{\FV@ObeyTabs{\strut #1}}%
\newcommand\sphinxVerbatimFormatLine[1]{\strut#1}%
% MEMO: if verbatimwrapslines is set to true (default) the #1 above is
% simply \box\spx@tempboxb, from the next two macros.
% The next two macros are a deep hack of fancyvrb.sty core line processing in
% order to wrap too long lines, either at spaces and natural break-points,
% (soft wrap) or optionally at any character (hard wrap).  This requires deep
% hack to work around the \hbox'es wrappers of fancyvrb.sty as they would
% prevent page breaks.  Formerly Sphinx obtained wrapping by inserting the
% material into a vertical box (which was later again boxed -- twice -- by
% fancyvrb thinking it was a single line...) but this was incompatible with
% allowing page breaks (refs: #8686).
% We use core TeX techniques to pre-process a paragraph then recover its
% constituents lines (as boxes, not as tokens) and hand them over to original
% fancyvrb line process.  It is mandatory to update \FV@ProcessLine and
% \@tempboxa globally to get fancyvrb internals into working to our
% satisfaction.
% This will get disrupted if anything adding vertical penalties or glues
% is activated via some \vadjust from inside the Pygmentized code lines.
\def\spx@verb@@ProcessLines{%
   \unskip
   \unpenalty
   \setbox\spx@tempboxb\lastbox
\ifvoid\spx@tempboxb\else
   {\spx@verb@@ProcessLines}%
   \FV@ProcessLine{\box\spx@tempboxb}%
   \global\let\FV@ProcessLine\FV@ProcessLine
   \global\setbox\@tempboxa=\box\@tempboxa
   \aftergroup\spx@verb@@InhibitLineNumber
\fi
}%
\def\spx@verb@@InhibitLineNumber{%
    \let\FV@LeftListNumber\relax
    \let\FV@RightListNumber\relax
}%
% This will replace fancyvrb's \FV@@PreProcessLine
% Instead of boxing \FV@Line (which contains the Pygmentized line tokens), we
% first typeset it in a vertical box of the suitable width (taking into
% account nested lists) to activate the TeX built-in paragraph builder, then
% we recover individual lines as horizontal boxes and feed them to fancyvrb
% native line processing (which may add line numbers).  The interline
% penalties and vertical glue to maintain baseline distance will be added
% again by this process so in recursive \spx@verb@@ProcessLines which starts
% from bottom and makes its way up to first part of the wrapped line we do not
% need to worry about them.  An additional initial measuring step is needed if
% user issued verbatimforcewraps=true, which elaborates on the same technique.
% If hard wraps get activated, they get implemented via hacked \PYG macros.
\def\spx@verb@@PreProcessLine{%
  \FV@StepLineNo
  \FV@Gobble
     \def\spx@verb@FV@Line{\FV@Line}%
     \ifspx@opt@verbatimforcewraps
       \spx@verb@DecideIfWillDoForceWrap
     \fi
% MEMO: \everypar{} was issued earlier (and due to \@setminipage
% would have been only \@minipagefalse\everypar{} otherwise).
     \setbox\spx@tempboxa=\vtop{\hsize\linewidth
          \raggedright\hyphenpenalty\z@\exhyphenpenalty\z@
          \doublehyphendemerits\z@\finalhyphendemerits\z@
% MEMO: fancyvrb has options obeytabs and tabsize.  Anyhow tab characters
% do not make it to the tex file, they have been converted to spaces earlier.
% But, if this was not the case, the support would be implemented here via
%     \FV@ObeyTabs{\strut\spx@verb@FV@Line\strut}%
% And one would need a similar change in the measuring phase done by
% \spx@verb@DecideIfWillDoForceWrap
                   \strut\spx@verb@FV@Line\strut
% MEMO: since LaTeX 2021-06-01, there might be some hooks executed at
% start and end of paragraphs (in future: PDF tagging), but we need an
% explicit \par here for that.  Else the kernel hooks at start of paragraph
% are executed but not the ones at its end.
          \par
          }%
     \setbox\spx@tempboxa=\vtop{\unvbox\spx@tempboxa
          \setbox\spx@tempboxb\lastbox
          {\spx@verb@@ProcessLines}%
          \FV@ProcessLine{\box\spx@tempboxb}%
          \global\let\FV@ProcessLine\FV@ProcessLine
          \global\setbox\@tempboxa=\box\@tempboxa
          }%
  \unvbox\spx@tempboxa
}%
%
% The normal line wrapping allows breaks at spaces and ascii non
% letters, non digits. The \raggedright above means there will be
% an overfilled line only if some non-breakable "word" was
% encountered, which is longer than a line (it is moved always to
% be on its own on a new line).
% 
% The "forced" line wrapping will parse the tokens to add potential
% breakpoints at each character. As some strings are highlighted,
% we have to apply the highlighting character per character, which
% requires to manipulate the output of the Pygments LaTeXFormatter.
%
% Doing this at latex level is complicated. The contents should
% be as expected: i.e. some active characters from
% \sphinxbreaksviaactive, some Pygments character escapes such as
% \PYGZdl{}, and the highlighting \PYG macro with always 2
% arguments. No other macros should be there, except perhaps
% zero-parameter macros. In particular:
% - the texcomments Pygments option must be set to False
%
% With pdflatex, Unicode input gives multi-bytes characters
% where the first byte is active. We support the "utf8" macros
% only. "utf8x" is not supported.
%
% The highlighting macro \PYG will be applied character per
% character. Highlighting via a colored background gives thus a
% chain of small colored boxes which may cause some artefact in
% some pdf viewers. Can't do anything here if we do want the line
% break to be possible.
%
% First a measurement step is done of what would the standard line
% wrapping give (i.e line breaks only at spaces and non-letter,
% non-digit ascii characters), cf TeX by Topic for the basic
% dissecting technique: TeX unfortunately when building a vertical
% box does not store in an accessible way what was the maximal
% line-width during paragraph building.
%
% MEMO: in future use perhaps rather \RawNoindent/\RawParEnd, but
% ltpara (LaTeX 2021-06-01) is not yet in final form (June 2022).
%
% Avoid LaTeX 2021 alteration of \@@par which potentially could break our
% measurement step (typically if the para/after hook is configured to use
% \vspace).  Of course, breakage could happen only from user or package
% adding things to basic Sphinx latex.  And perhaps spring LaTeX 2021 will
% provide a non-hooked \@@par, but this should work anyway and can't be
% beaten for speed.
\ltx@ifundefined{tex_par:D}
% We could use \@ifl@t@r\fmtversion{2020/02/02}{use \tex_par:D}{use \@@par}.
  {\let\spx@par\@@par}% \@@par is then expected to be TeX's original \par
  {\expandafter\let\expandafter\spx@par\csname tex_par:D\endcsname}
% More hesitation for avoiding the at-start-of-par hooks for our
% measurement : 1. with old LaTeX, we can not avoid hooks from everyhook
% or similar packages, 2. and perhaps the hooks add stuff which we should
% actually measure.  Ideally, hooks are for inserting things in margin
% which do not change spacing.  Most everything else in fact should not be
% executed in our scratch box for measurement, such as counter stepping.
\ltx@ifundefined{tex_everypar:D}
  {\let\spx@everypar\everypar}
  {\expandafter\let\expandafter\spx@everypar\csname tex_everypar:D\endcsname}
%
% If the max width exceeds the linewidth by more than verbatimmaxoverfull
% character widths, or if the min width plus verbatimmaxunderfull character
% widths is inferior to linewidth, then we apply the "force wrapping" with
% potential line break at each character, else we don't.
\long\def\spx@verb@DecideIfWillDoForceWrap{%
    \global\let\spx@verb@maxwidth\z@
    \global\let\spx@verb@minwidth\linewidth
    \setbox\spx@tempboxa
    \vtop{\raggedright\hyphenpenalty\z@\exhyphenpenalty\z@
          \doublehyphendemerits\z@\finalhyphendemerits\z@
% Avoid TeX reporting Overfull \hbox'es during this measuring phase. Setting
% \hbadness to \@M to avoid Underfull reports is unneeded due to \raggedright.
          \hfuzz\maxdimen
          \spx@everypar{}\noindent\strut\FV@Line\strut\spx@par
          \spx@verb@getwidths}%
    \ifdim\spx@verb@maxwidth>
          \dimexpr\linewidth+\spx@opt@verbatimmaxoverfull\fontcharwd\font`X \relax
% The \expandafter is due to \spx@verb@wrapPYG requiring to "see" the TeX tokens
% from the pygmentize output.
      \def\spx@verb@FV@Line{\expandafter\spx@verb@wrapPYG\FV@Line\spx@verb@wrapPYG}%
    \else
      \ifdim\spx@verb@minwidth<
            \dimexpr\linewidth-\spx@opt@verbatimmaxunderfull\fontcharwd\font`X \relax
        \def\spx@verb@FV@Line{\expandafter\spx@verb@wrapPYG\FV@Line\spx@verb@wrapPYG}%
      \fi
    \fi
}%
% auxiliary paragraph dissector to get max and min widths
% but minwidth must not take into account the last line
\def\spx@verb@getwidths {%
    \unskip\unpenalty
    \setbox\spx@tempboxb\lastbox
    \ifvoid\spx@tempboxb
    \else
       \setbox\spx@tempboxb\hbox{\unhbox\spx@tempboxb}%
       \ifdim\spx@verb@maxwidth<\wd\spx@tempboxb
          \xdef\spx@verb@maxwidth{\number\wd\spx@tempboxb sp}%
       \fi
       \expandafter\spx@verb@getwidths@loop
    \fi
}%
\def\spx@verb@getwidths@loop {%
    \unskip\unpenalty
    \setbox\spx@tempboxb\lastbox
    \ifvoid\spx@tempboxb
    \else
       \setbox\spx@tempboxb\hbox{\unhbox\spx@tempboxb}%
       \ifdim\spx@verb@maxwidth<\wd\spx@tempboxb
          \xdef\spx@verb@maxwidth{\number\wd\spx@tempboxb sp}%
       \fi
       \ifdim\spx@verb@minwidth>\wd\spx@tempboxb
          \xdef\spx@verb@minwidth{\number\wd\spx@tempboxb sp}%
       \fi
       \expandafter\spx@verb@getwidths@loop
    \fi
}%
% auxiliary macros to implement "cut long line even in middle of word"
\catcode`Z=3 % safe delimiter
\def\spx@verb@wrapPYG{%
    \futurelet\spx@nexttoken\spx@verb@wrapPYG@i
}%
\def\spx@verb@wrapPYG@i{%
    \ifx\spx@nexttoken\spx@verb@wrapPYG\let\next=\@gobble\else
    \ifx\spx@nexttoken\PYG\let\next=\spx@verb@wrapPYG@PYG@onebyone\else
      \discretionary{}{\sphinxafterbreak}{}%
      \let\next\spx@verb@wrapPYG@ii
    \fi\fi
    \next
}%
% Let's recognize active characters. We don't support utf8x only utf8.
% And here #1 should not have picked up (non empty) braced contents
\long\def\spx@verb@wrapPYG@ii#1{%
    \ifcat\noexpand~\noexpand#1\relax% active character
      \expandafter\spx@verb@wrapPYG@active
    \else % non-active character, control sequence such as \PYGZdl, or empty
      \expandafter\spx@verb@wrapPYG@one
    \fi {#1}%
}%
\long\def\spx@verb@wrapPYG@active#1{%
% Let's hope expansion of active character does not really require arguments,
% as we certainly don't want to go into expanding upfront token stream anyway.
    \expandafter\spx@verb@wrapPYG@iii#1{}{}{}{}{}{}{}{}{}Z#1%
}%
\long\def\spx@verb@wrapPYG@iii#1#2Z{%
    \ifx\UTFviii@four@octets#1\let\next=\spx@verb@wrapPYG@four\else
    \ifx\UTFviii@three@octets#1\let\next=\spx@verb@wrapPYG@three\else
    \ifx\UTFviii@two@octets#1\let\next=\spx@verb@wrapPYG@two\else
    \let\next=\spx@verb@wrapPYG@one
    \fi\fi\fi
    \next
}%
\long\def\spx@verb@wrapPYG@one   #1{#1\futurelet\spx@nexttoken\spx@verb@wrapPYG@i}%
\long\def\spx@verb@wrapPYG@two   #1#2{#1#2\futurelet\spx@nexttoken\spx@verb@wrapPYG@i}%
\long\def\spx@verb@wrapPYG@three #1#2#3{#1#2#3\futurelet\spx@nexttoken\spx@verb@wrapPYG@i}%
\long\def\spx@verb@wrapPYG@four  #1#2#3#4{#1#2#3#4\futurelet\spx@nexttoken\spx@verb@wrapPYG@i}%
% Replace \PYG by itself applied one character at a time! This way breakpoints
% can be inserted.
\def\spx@verb@wrapPYG@PYG@onebyone#1#2#3{% #1 = \PYG, #2 = highlight spec, #3 = tokens
    \def\spx@verb@wrapPYG@PYG@spec{{#2}}%
    \futurelet\spx@nexttoken\spx@verb@wrapPYG@PYG@i#3Z%
}%
\def\spx@verb@wrapPYG@PYG@i{%
    \ifx\spx@nexttokenZ\let\next=\spx@verb@wrapPYG@PYG@done\else
      \discretionary{}{\sphinxafterbreak}{}%
      \let\next\spx@verb@wrapPYG@PYG@ii
    \fi
    \next
}%
\def\spx@verb@wrapPYG@PYG@doneZ{\futurelet\spx@nexttoken\spx@verb@wrapPYG@i}%
\long\def\spx@verb@wrapPYG@PYG@ii#1{%
    \ifcat\noexpand~\noexpand#1\relax% active character
      \expandafter\spx@verb@wrapPYG@PYG@active
    \else % non-active character, control sequence such as \PYGZdl, or empty
      \expandafter\spx@verb@wrapPYG@PYG@one
    \fi {#1}%
}%
\long\def\spx@verb@wrapPYG@PYG@active#1{%
% Let's hope expansion of active character does not really require arguments,
% as we certainly don't want to go into expanding upfront token stream anyway.
    \expandafter\spx@verb@wrapPYG@PYG@iii#1{}{}{}{}{}{}{}{}{}Z#1%
}%
\long\def\spx@verb@wrapPYG@PYG@iii#1#2Z{%
    \ifx\UTFviii@four@octets#1\let\next=\spx@verb@wrapPYG@PYG@four\else
    \ifx\UTFviii@three@octets#1\let\next=\spx@verb@wrapPYG@PYG@three\else
    \ifx\UTFviii@two@octets#1\let\next=\spx@verb@wrapPYG@PYG@two\else
    \let\next=\spx@verb@wrapPYG@PYG@one
    \fi\fi\fi
    \next
}%
\long\def\spx@verb@wrapPYG@PYG@one#1{%
    \expandafter\PYG\spx@verb@wrapPYG@PYG@spec{#1}%
    \futurelet\spx@nexttoken\spx@verb@wrapPYG@PYG@i
}%
\long\def\spx@verb@wrapPYG@PYG@two#1#2{%
    \expandafter\PYG\spx@verb@wrapPYG@PYG@spec{#1#2}%
    \futurelet\spx@nexttoken\spx@verb@wrapPYG@PYG@i
}%
\long\def\spx@verb@wrapPYG@PYG@three#1#2#3{%
    \expandafter\PYG\spx@verb@wrapPYG@PYG@spec{#1#2#3}%
    \futurelet\spx@nexttoken\spx@verb@wrapPYG@PYG@i
}%
\long\def\spx@verb@wrapPYG@PYG@four#1#2#3#4{%
    \expandafter\PYG\spx@verb@wrapPYG@PYG@spec{#1#2#3#4}%
    \futurelet\spx@nexttoken\spx@verb@wrapPYG@PYG@i
}%
\catcode`Z 11 %
%
\g@addto@macro\FV@SetupFont{%
    \sbox\sphinxcontinuationbox {\spx@opt@verbatimcontinued}%
    \sbox\sphinxvisiblespacebox {\spx@opt@verbatimvisiblespace}%
}%
\newenvironment{sphinxVerbatim}{%
  % first, let's check if there is a caption
  \ifx\sphinxVerbatimTitle\empty
      \sphinxvspacefixafterfrenchlists
      \parskip\z@skip
      \vskip\sphinxverbatimsmallskipamount
      % there was no caption. Check if nevertheless a label was set.
      \ifx\sphinxLiteralBlockLabel\empty\else
      % we require some space to be sure hyperlink target from \phantomsection
      % will not be separated from upcoming verbatim by a page break
          \needspace{\sphinxliteralblockwithoutcaptionneedspace}%
          \phantomsection\sphinxLiteralBlockLabel
      \fi
  \else
     \parskip\z@skip
     \if t\spx@opt@literalblockcappos
       \vskip\spx@abovecaptionskip
       \def\sphinxVerbatim@Before
           {\sphinxVerbatim@Title\nointerlineskip
            \kern\dimexpr-\dp\strutbox+\sphinxbelowcaptionspace
                 % MEMO: prior to 7.4.0 a test was done for presence or
                 % not of a frame and if not top padding was removed if
                 % no background color.  A background color is now always
                 % assumed, so this got removed.
                 % caption package adds \abovecaptionskip vspace, remove it
            \spx@ifcaptionpackage{-\abovecaptionskip}{}\relax}%
     \else
       \vskip\sphinxverbatimsmallskipamount
       \def\sphinxVerbatim@After
          {\nointerlineskip\kern\dimexpr\dp\strutbox
           % MEMO: 7.4.0 removes here too an optional removal of bottom padding
           \spx@ifcaptionpackage{-\abovecaptionskip}{}\relax
           \sphinxVerbatim@Title}%
     \fi
     \def\@captype{literalblock}%
     \capstart
     % \sphinxVerbatimTitle must reset color
     \setbox\sphinxVerbatim@TitleBox
            \hbox{\begin{minipage}{\linewidth}%
     % caption package may detect wrongly if top or bottom, so we help it
                    \spx@ifcaptionpackage
                      {\caption@setposition{\spx@opt@literalblockcappos}}{}%
                    \sphinxVerbatimTitle
                  \end{minipage}}%
  \fi
  \global\let\sphinxLiteralBlockLabel\empty
  \global\let\sphinxVerbatimTitle\empty
  % the "FrameCommand"'s are also responsible to attach the "Title".
  \let\FrameCommand     \sphinxVerbatim@FrameCommand
  % those will also check status of the pre_box-decoration-break option
  \let\FirstFrameCommand\sphinxVerbatim@FirstFrameCommand
  \let\MidFrameCommand  \sphinxVerbatim@MidFrameCommand
  \let\LastFrameCommand \sphinxVerbatim@LastFrameCommand
  %
  \ifspx@opt@verbatimhintsturnover\else
      \let\sphinxVerbatim@Continued\@empty
      \let\sphinxVerbatim@Continues\@empty
  \fi
  % initialization for \spx@boxes@fcolorbox from sphinxpackageboxes.sty
  % it will take into account status of verbatimwithframe Boolean
  \spx@verb@boxes@fcolorbox@setup
  \ifspx@opt@verbatimwrapslines
    % deep hack into fancyvrb's internal processing of input lines
    \let\FV@@PreProcessLine\spx@verb@@PreProcessLine
    % space character will allow line breaks
    \let\FV@Space\spx@verbatim@space
    % allow breaks at special characters using \PYG... macros.
    \sphinxbreaksatspecials
    % breaks at punctuation characters . , ; ? ! and / (needs catcode activation)
    \fvset{codes*=\sphinxbreaksviaactive}%
  \fi
  \let\FancyVerbFormatLine\sphinxFancyVerbFormatLine
  \VerbatimEnvironment
  % workaround to fancyvrb's check of current list depth
  \def\@toodeep {\advance\@listdepth\@ne}%
  % The list environment is needed to control perfectly the vertical space.
  % Note: \OuterFrameSep used by framed.sty is later set to \topsep hence 0pt.
  % - if caption: distance from last text baseline to caption baseline is
  %   A+(B-F)+\ht\strutbox, A = \abovecaptionskip (default 10pt), B =
  %   \baselineskip, F is the framed.sty \FrameHeightAdjust macro, default 6pt.
  %   Formula valid for F < 10pt.
  % - distance of baseline of caption to top of frame is like for tables:
  %   \sphinxbelowcaptionspace (=0.5\baselineskip)
  % - if no caption: distance of last text baseline to code frame is S+(B-F),
  %   with S = \sphinxverbatimtopskip (=\smallskip)
  % - and distance from bottom of frame to next text baseline is
  %   \baselineskip+\parskip.
  % The \trivlist is used to avoid possible "too deeply nested" error.
  \itemsep   \z@skip
  \topsep    \z@skip
  \partopsep \z@skip
  % trivlist will set \parsep to \parskip (which itself is set to zero above)
  % \leftmargin will be set to zero by trivlist
  \rightmargin\z@
  \parindent  \z@% becomes \itemindent. Default zero, but perhaps overwritten.
  \trivlist\item\relax
     \ifspx@inframed\setbox\sphinxVerbatim@ContentsBox\vbox\bgroup
        \@setminipage\hsize\linewidth
        % use bulk of minipage paragraph shape restores (this is needed
        % in indented contexts, at least for some)
        \textwidth\hsize \columnwidth\hsize \@totalleftmargin\z@
        \leftskip\z@skip \rightskip\z@skip  \@rightskip\z@skip
     \else
       \ifsphinxverbatimwithminipage\noindent\begin{minipage}{\linewidth}\fi
       \MakeFramed {% adapted over from framed.sty's snugshade environment
        \advance\hsize-\width\@totalleftmargin\z@\linewidth\hsize\@setminipage
        }%
     \fi
     % For grid placement from \strut's in \FancyVerbFormatLine
     \lineskip\z@skip
     % active comma should not be overwritten by \@noligs
     \ifspx@opt@verbatimwrapslines
       \let\verbatim@nolig@list \sphinx@verbatim@nolig@list
     \fi
     % optimization: as codelines will be handled inside boxes, \everypar is
     % never reset, and it issues \@minipagefalse repeatedly (from \@setminipage).
     % As fancyvrb Verbatim will do \@minipagefalse itself, let's simplify things.
     \everypar{}%
     \color@begingroup % protect against color leaks (upstream framed.sty bug)
     \ifspx@pre@withtextcolor\color{VerbatimTextColor}\fi % mostly shadowed by
                                % Pygments highlighting anyhow
     \spx@pre@TeXextras
     % will fetch its optional arguments if any
     \OriginalVerbatim
}%
{%
  \endOriginalVerbatim
  \color@endgroup % matches the \color@begingroup
  \ifspx@inframed
   \egroup % finish \sphinxVerbatim@ContentsBox vbox
   \nobreak % update page totals
%%%%
% MEMO (2022/07/09, while preparing 5.1.0 LaTeX CSS-style sphinxsetup options)
% This test will systematically cause to abandon framing if the code-block
% is near bottom of a warning-type notice which TeX has not yet decided whether
% it fits on current page and which is near bottom of page.  Indeed the
% \pagetotal will already be very near \pagegoal. This is probably a not
% intended behaviour, and perhaps the whole thing should be removed?  Indeed
% the result is surprising then because the notice will be split, code-block
% will be on page 2 and will have no background-color, no border.
   \ifdim\dimexpr
         \ht\sphinxVerbatim@ContentsBox+
         \dp\sphinxVerbatim@ContentsBox+
         \ht\sphinxVerbatim@TitleBox+
         \dp\sphinxVerbatim@TitleBox+
         % 6.2.0 uses here the dimen registers from sphinxpackageboxes.sty,
         % they got setup by \spx@verb@boxes@fcolorbox@setup
         \spx@boxes@padding@top+
         \spx@boxes@padding@bottom+
         \ifspx@opt@verbatimwithframe \spx@boxes@border@top+
                                      \spx@boxes@border@bottom+\fi
         % try to account for external frame parameters
         % MEMO: this is because the sphinxheavybox (for warning admonitions)
         %       environment sets \FrameSep and \FrameRule
         % TODO: fix this bad implicit dependency
         \FrameSep+\FrameRule+
         % Usage here of 2 baseline distances is empirical.
         % In border case where code-block fits barely in remaining space,
         % it gets framed and looks good but the outer frame may continue
         % on top of next page and give (if no contents after code-block)
         % an empty framed line, as testing showed.
         2\baselineskip+
         % now add all to accumulated page totals and compare to \pagegoal
         \pagetotal+\pagedepth>\pagegoal
    % long contents: do not \MakeFramed. Do make a caption (either before or
    % after) if title exists. Continuation hints across pagebreaks dropped.
    % FIXME? a bottom caption may end up isolated at top of next page
    %        (no problem with a top caption, which is default)
    \spx@opt@verbatimwithframefalse
    \def\sphinxVerbatim@Title{\noindent\box\sphinxVerbatim@TitleBox\par}%
    \sphinxVerbatim@Before
    \noindent\unvbox\sphinxVerbatim@ContentsBox\par
    \sphinxVerbatim@After
   \else
    % short enough contents: use \MakeFramed. As it is nested, this requires
    % minipage encapsulation.
    \noindent\begin{minipage}{\linewidth}%
     \MakeFramed {% Use it now with the fetched contents
      \advance\hsize-\width\@totalleftmargin\z@\linewidth\hsize\@setminipage
      }%
     \unvbox\sphinxVerbatim@ContentsBox
     % the \@minipagefalse is superfluous, actually.
     \par\unskip\@minipagefalse\endMakeFramed
    \end{minipage}%
   \fi
  \else % non-nested \MakeFramed
    \par\unskip\@minipagefalse\endMakeFramed % from framed.sty snugshade
    \ifsphinxverbatimwithminipage\end{minipage}\fi
  \fi
  \endtrivlist
}
\newenvironment {sphinxVerbatimNoFrame}
  {\spx@opt@verbatimwithframefalse
   \VerbatimEnvironment
   \begin{sphinxVerbatim}}
  {\end{sphinxVerbatim}}
\newenvironment {sphinxVerbatimintable}
  {% don't use a frame if in a table cell
   \spx@opt@verbatimwithframefalse
   \sphinxverbatimwithminipagetrue
   % the literal block caption uses \sphinxcaption which is wrapper of \caption,
   % but \caption must be modified because longtable redefines it to work only
   % for the own table caption, and tabulary has multiple passes
   \let\caption\sphinxfigcaption
   % reduce above caption skip
   \def\spx@abovecaptionskip{\sphinxverbatimsmallskipamount}%
   \VerbatimEnvironment
   \begin{sphinxVerbatim}}
  {\end{sphinxVerbatim}}


%% PARSED LITERALS
% allow long lines to wrap like they do in code-blocks

% this should be kept in sync with definitions in sphinx.util.texescape
\newcommand*\sphinxbreaksattexescapedchars{%
  \def\do##1##2% put potential break point before character
     {\def##1{\discretionary{}{\sphinxafterbreak\char`##2}{\char`##2}}}%
  \do\{\{\do\textless\<\do\#\#\do\%\%\do\$\$% {, <, #, %, $
  \def\do##1##2% put potential break point after character
     {\def##1{\discretionary{\char`##2}{\sphinxafterbreak}{\char`##2}}}%
  \do\_\_\do\}\}\do\textasciicircum\^\do\&\&% _, }, ^, &,
  \do\textgreater\>\do\textasciitilde\~% >, ~
  \do\textbackslash\\% \
}
\newcommand*\sphinxbreaksviaactiveinparsedliteral{%
  \sphinxbreaksviaactive % by default handles . , ; ? ! /
  \lccode`\~`\~ %
  % update \dospecials as it is used by \url
  % but deactivation will already have been done hence this is unneeded:
  %  \expandafter\def\expandafter\dospecials\expandafter{\dospecials
  %   \sphinxbreaksbeforeactivelist\sphinxbreaksafteractivelist\do\-}%
}
\newcommand*\sphinxbreaksatspaceinparsedliteral{%
  \lccode`~32 \lowercase{\let~}\spx@verbatim@space\lccode`\~`\~
}
\newcommand*{\sphinxunactivateextras}{\let\do\@makeother
      \sphinxbreaksbeforeactivelist\sphinxbreaksafteractivelist}%
% the \catcode13=5\relax (deactivate end of input lines) is left to callers
\newcommand*{\sphinxunactivateextrasandspace}{\catcode32=10\relax
      \sphinxunactivateextras}%
% alltt uses a monospace font and linebreaks at dashes (which are escaped
% to \sphinxhyphen{} which expands to -\kern\z@) are inhibited with pdflatex.
% Not with xelatex (cf \defaultfontfeatures in latex writer), so:
\newcommand*{\sphinxhypheninparsedliteral}{\sphinxhyphennobreak}
% now for the modified alltt environment
\newenvironment{sphinxalltt}
{% at start of next line to workaround Emacs/AUCTeX issue with this file
\begin{alltt}%
 \ifspx@opt@parsedliteralwraps
   \sbox\sphinxcontinuationbox {\spx@opt@verbatimcontinued}%
   \sbox\sphinxvisiblespacebox {\spx@opt@verbatimvisiblespace}%
   \let\sphinxhyphen\sphinxhypheninparsedliteral
   \sphinxbreaksattexescapedchars
   \sphinxbreaksviaactiveinparsedliteral
   \sphinxbreaksatspaceinparsedliteral
% alltt takes care of the ' as derivative ("prime") in math mode
   \everymath\expandafter{\the\everymath\sphinxunactivateextrasandspace
             \catcode`\<=12\catcode`\>=12\catcode`\^=7\catcode`\_=8 }%
% not sure if displayed math (align,...) can end up in parsed-literal, anyway
   \everydisplay\expandafter{\the\everydisplay
             \catcode13=5 \sphinxunactivateextrasandspace
             \catcode`\<=12\catcode`\>=12\catcode`\^=7\catcode`\_=8 }%
 \fi }
{\end{alltt}}


%% INLINE MARK-UP
%

% Protect \href's first argument in contexts such as sphinxalltt (or
% \sphinxcode). Sphinx uses \#, \%, \& ... always inside \sphinxhref.
\protected\def\sphinxhref#1#2{{%
    \sphinxunactivateextrasandspace % never do \scantokens with active space!
% for the \endlinechar business, https://github.com/latex3/latex2e/issues/286
    \endlinechar\m@ne\everyeof{{\endlinechar13 #2}}% keep catcode regime for #2
    \scantokens{\href{#1}}% normalise it for #1 during \href expansion
}}
% Same for \url. And also \nolinkurl for coherence.
\protected\def\sphinxurl#1{{%
    \sphinxunactivateextrasandspace\everyeof{}% (<- precaution for \scantokens)
    \endlinechar\m@ne\scantokens{\url{#1}}%
}}
\protected\def\sphinxnolinkurl#1{{%
    \sphinxunactivateextrasandspace\everyeof{}%
    \endlinechar\m@ne\scantokens{\nolinkurl{#1}}%
}}

% \sphinxupquote
% to obtain straight quotes we execute \@noligs as patched by upquote, and
% \scantokens is needed in cases where it would be too late for the macro to
% first set catcodes and then fetch its argument. We also make the contents
% breakable at non-escaped . , ; ? ! / using \sphinxbreaksviaactive,
% and also at \ character (which is escaped to \textbackslash{}).
\protected\def\sphinxtextbackslashbreakbefore
 {\discretionary{}{\sphinxafterbreak\sphinx@textbackslash}{\sphinx@textbackslash}}
\protected\def\sphinxtextbackslashbreakafter
 {\discretionary{\sphinx@textbackslash}{\sphinxafterbreak}{\sphinx@textbackslash}}
\let\sphinxtextbackslash\sphinxtextbackslashbreakafter
% - is escaped to \sphinxhyphen{} and this default ensures no linebreak
% behaviour (also with a non monospace font, or with xelatex)
\newcommand*{\sphinxhyphenininlineliteral}{\sphinxhyphennobreak}
% the macro must be protected if it ends up used in moving arguments,
% in 'alltt' \@noligs is done already, and the \scantokens must be avoided.
\protected\def\sphinxupquote#1{{\def\@tempa{alltt}%
  \ifx\@tempa\@currenvir\else
   \let\sphinxhyphen\sphinxhyphenininlineliteral
   \ifspx@opt@inlineliteralwraps
      % break at . , ; ? ! /
      \sphinxbreaksviaactive
      % break also at \
      \setbox8=\hbox{\textbackslash}%
      \def\sphinx@textbackslash{\copy8}%
      \let\textbackslash\sphinxtextbackslash
      % by default, no continuation symbol on next line but may be added
      \let\sphinxafterbreak\sphinxafterbreakofinlineliteral
      % do not overwrite the comma set-up
      \let\verbatim@nolig@list\sphinx@literal@nolig@list
   \fi
   % fix a space-gobbling issue due to LaTeX's original \do@noligs
% TODO: using \@noligs as patched by upquote.sty is now unneeded because
% either ` and ' are escaped (non-unicode engines) or they don't build
% ligatures (unicode engines). Thus remove this and unify handling of `, <, >,
% ' and - with the characters . , ; ? ! / as handled via
% \sphinxbreaksviaactive.
% Hence \sphinx@do@noligs will be removed, or rather replaced  with code
% inserting discretionaries, as they allow a continuation symbol on start of
% next line to achieve common design with code-blocks.
% TODO: do the above TODO!
% Extend \sphinxunactivateextras for \sphinxhref as the latter may
% actually be in the scope of \sphinxupquote and does a \scantokens
% of its own.
   \expandafter\def\expandafter\sphinxunactivateextras\expandafter
      {\sphinxunactivateextras\verbatim@nolig@list}%
   \let\do@noligs\sphinx@do@noligs
   \@noligs\endlinechar\m@ne\everyeof{}% (<- in case inside \sphinxhref)
   \expandafter\scantokens
  \fi {{#1}}}}% extra brace pair to fix end-space gobbling issue...
\def\sphinx@do@noligs #1{\catcode`#1\active\begingroup\lccode`\~`#1\relax
    \lowercase{\endgroup\def~{\leavevmode\kern\z@\char`#1 }}}
\def\sphinx@literal@nolig@list {\do\`\do\<\do\>\do\'\do\-}%
\let\sphinxafterbreakofinlineliteral\empty


\endinput

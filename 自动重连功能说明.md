# 自动重连功能说明

## 功能概述

为剪切机自动化控制系统添加了自动重连功能，当通信连接意外断开时，系统能够自动尝试重新建立连接，提高系统的稳定性和可靠性。

## 新增功能

### 1. 自动重连开关
- 在连接区域新增了"自动重连"复选框
- 用户可以选择是否启用自动重连功能
- 默认状态为关闭，需要用户手动启用

### 2. 智能重连机制
- **触发条件**：仅在连接意外断开时触发，手动断开不会启动自动重连
- **重连次数**：最多尝试5次重连
- **重连间隔**：每次重连间隔5秒
- **连接参数**：自动使用上次成功连接的参数（串口/TCP设置）

### 3. 状态提示
- 实时显示重连状态和进度
- 在通信监控窗口显示详细的重连日志
- 状态栏显示当前重连状态

## 使用方法

### 启用自动重连
1. 在主界面连接区域找到"自动重连"复选框
2. 勾选该复选框启用自动重连功能
3. 建立正常连接（串口RTU或TCP）

### 测试自动重连
1. 确保已启用自动重连功能
2. 建立连接后，人为制造连接断开：
   - 串口连接：拔掉串口线或关闭设备
   - TCP连接：关闭TCP服务器或断开网络
3. 观察系统自动重连过程

## 技术实现

### 核心组件
- **重连定时器**：`QTimer`实现延时重连
- **连接参数保存**：自动保存最后成功的连接配置
- **状态管理**：跟踪重连次数和状态

### 关键参数
```python
self.max_reconnect_attempts = 5  # 最大重连次数
self.reconnect_interval = 5      # 重连间隔（秒）
```

### 重连流程
1. 检测到连接断开
2. 检查是否满足自动重连条件
3. 启动重连定时器
4. 使用保存的连接参数尝试重连
5. 重连成功则恢复所有功能，失败则继续重试
6. 达到最大重试次数后停止

## 注意事项

### 不会触发自动重连的情况
- 手动点击"断开"按钮
- 从未建立过连接
- 未启用自动重连功能
- 已达到最大重连次数

### 重连成功后的恢复
- 自动恢复所有控制按钮状态
- 重新启动状态更新定时器（如果启用了自动更新）
- 恢复任务计划界面功能

## 配置选项

用户可以根据需要调整以下参数（需要修改代码）：

```python
# 在 __init__ 方法中修改这些参数
self.max_reconnect_attempts = 5  # 最大重连次数
self.reconnect_interval = 5      # 重连间隔（秒）
```

## 日志记录

自动重连过程中的所有操作都会记录在通信监控窗口中：
- 连接断开检测
- 重连尝试开始
- 重连成功/失败
- 重连次数统计
- 功能恢复状态

## 兼容性

- 支持串口RTU和TCP两种连接方式
- 与现有的手动连接功能完全兼容
- 不影响原有的状态更新和监控功能

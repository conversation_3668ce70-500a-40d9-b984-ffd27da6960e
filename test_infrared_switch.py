#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
红外感应功能开关测试程序
用于测试红外感应功能开关的状态更新
"""

import sys
import time
import json
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QPushButton, QLabel
from PyQt5.QtCore import QTimer, Qt
from PyQt5.QtGui import QFont

class InfraredSwitchTest(QMainWindow):
    """红外感应开关测试界面"""
    
    def __init__(self):
        super().__init__()
        self.infrared_enabled = True  # 默认启用
        self.init_ui()
        self.load_config()
        
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("红外感应功能开关测试")
        self.setGeometry(300, 300, 500, 300)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("红外感应功能开关测试")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        layout.addWidget(title_label)
        
        # 状态显示区域
        status_layout = QHBoxLayout()
        
        # 红外感应器状态
        infrared_label = QLabel("红外感应器:")
        infrared_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 14px;")
        self.infrared_status_label = QLabel("🔴 无料")
        self.infrared_status_label.setStyleSheet("color: #e74c3c; font-weight: bold; font-size: 14px;")
        status_layout.addWidget(infrared_label)
        status_layout.addWidget(self.infrared_status_label)
        
        # 红外感应功能开关
        infrared_control_label = QLabel("红外感应功能:")
        infrared_control_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 14px;")
        self.infrared_enable_btn = QPushButton("🟢 已启用")
        self.infrared_enable_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-size: 14px;
                font-weight: bold;
                min-width: 100px;
                min-height: 30px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
        """)
        self.infrared_enable_btn.clicked.connect(self.toggle_infrared_function)
        status_layout.addWidget(infrared_control_label)
        status_layout.addWidget(self.infrared_enable_btn)
        
        layout.addLayout(status_layout)
        
        # 测试按钮区域
        button_layout = QHBoxLayout()
        
        # 模拟有料按钮
        self.simulate_material_btn = QPushButton("模拟有料")
        self.simulate_material_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        self.simulate_material_btn.clicked.connect(lambda: self.simulate_infrared_status(True))
        button_layout.addWidget(self.simulate_material_btn)
        
        # 模拟无料按钮
        self.simulate_no_material_btn = QPushButton("模拟无料")
        self.simulate_no_material_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        self.simulate_no_material_btn.clicked.connect(lambda: self.simulate_infrared_status(False))
        button_layout.addWidget(self.simulate_no_material_btn)
        
        layout.addLayout(button_layout)
        
        # 日志显示区域
        self.log_label = QLabel("日志:")
        self.log_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 12px;")
        layout.addWidget(self.log_label)
        
        self.log_text = QLabel("程序启动")
        self.log_text.setStyleSheet("color: #34495e; font-size: 11px; padding: 10px; background-color: #f8f9fa; border-radius: 4px;")
        self.log_text.setWordWrap(True)
        self.log_text.setMinimumHeight(100)
        layout.addWidget(self.log_text)
        
        # 定时器用于状态更新
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_status)
        self.timer.start(1000)  # 每秒更新一次
        
    def toggle_infrared_function(self):
        """切换红外感应功能开关"""
        try:
            self.infrared_enabled = not self.infrared_enabled
            
            if self.infrared_enabled:
                # 启用红外感应功能
                self.infrared_enable_btn.setText("🟢 已启用")
                self.infrared_enable_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #27ae60;
                        color: white;
                        border: none;
                        border-radius: 6px;
                        padding: 8px 16px;
                        font-size: 14px;
                        font-weight: bold;
                        min-width: 100px;
                        min-height: 30px;
                    }
                    QPushButton:hover {
                        background-color: #229954;
                    }
                    QPushButton:pressed {
                        background-color: #1e8449;
                    }
                """)
                self.add_log("✅ 红外感应功能已启用")
                
                # 启用后立即检查并更新红外感应器状态
                QTimer.singleShot(100, self.force_update_infrared_status)
            else:
                # 禁用红外感应功能
                self.infrared_enable_btn.setText("🔴 已禁用")
                self.infrared_enable_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #e74c3c;
                        color: white;
                        border: none;
                        border-radius: 6px;
                        padding: 8px 16px;
                        font-size: 14px;
                        font-weight: bold;
                        min-width: 100px;
                        min-height: 30px;
                    }
                    QPushButton:hover {
                        background-color: #c0392b;
                    }
                    QPushButton:pressed {
                        background-color: #a93226;
                    }
                """)
                self.add_log("⚠️ 红外感应功能已禁用")
                
                # 禁用后立即更新状态显示为"已禁用"
                self.infrared_status_label.setText("⚫ 已禁用")
                self.infrared_status_label.setStyleSheet("color: #6c757d; font-weight: bold; font-size: 14px;")
            
            # 保存配置
            self.save_config()
            
        except Exception as e:
            self.add_log(f"❌ 切换红外感应功能错误: {e}")
    
    def force_update_infrared_status(self):
        """强制更新红外感应器状态显示"""
        try:
            if self.infrared_enabled:
                # 如果功能已启用，显示默认状态
                self.infrared_status_label.setText("🔴 无料")
                self.infrared_status_label.setStyleSheet("color: #e74c3c; font-weight: bold; font-size: 14px;")
                self.add_log("🔄 红外感应器状态已更新")
            else:
                # 功能禁用时显示禁用状态
                self.infrared_status_label.setText("⚫ 已禁用")
                self.infrared_status_label.setStyleSheet("color: #6c757d; font-weight: bold; font-size: 14px;")
        except Exception as e:
            self.add_log(f"❌ 强制更新红外感应器状态错误: {e}")
    
    def simulate_infrared_status(self, has_material):
        """模拟红外感应器状态"""
        if not self.infrared_enabled:
            self.add_log("⚠️ 红外感应功能已禁用，无法模拟状态")
            return
        
        if has_material:
            self.infrared_status_label.setText("🟢 有料")
            self.infrared_status_label.setStyleSheet("color: #27ae60; font-weight: bold; font-size: 14px;")
            self.add_log("🟢 模拟检测到物料")
        else:
            self.infrared_status_label.setText("🔴 无料")
            self.infrared_status_label.setStyleSheet("color: #e74c3c; font-weight: bold; font-size: 14px;")
            self.add_log("🔴 模拟未检测到物料")
    
    def update_status(self):
        """定时更新状态"""
        current_time = time.strftime("%H:%M:%S")
        # 这里可以添加定期状态检查逻辑
    
    def add_log(self, message):
        """添加日志"""
        current_time = time.strftime("%H:%M:%S")
        current_log = self.log_text.text()
        new_log = f"[{current_time}] {message}\n{current_log}"
        # 限制日志长度
        lines = new_log.split('\n')
        if len(lines) > 10:
            lines = lines[:10]
        self.log_text.setText('\n'.join(lines))
    
    def save_config(self):
        """保存配置"""
        try:
            config = {
                'infrared_enabled': self.infrared_enabled
            }
            with open('test_config.json', 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=4, ensure_ascii=False)
            self.add_log("💾 配置已保存")
        except Exception as e:
            self.add_log(f"❌ 保存配置失败: {e}")
    
    def load_config(self):
        """加载配置"""
        try:
            with open('test_config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
                self.infrared_enabled = config.get('infrared_enabled', True)
                self.update_button_state()
                self.add_log("📂 配置已加载")
        except FileNotFoundError:
            self.add_log("📂 配置文件不存在，使用默认配置")
        except Exception as e:
            self.add_log(f"❌ 加载配置失败: {e}")
    
    def update_button_state(self):
        """更新按钮状态"""
        if self.infrared_enabled:
            self.infrared_enable_btn.setText("🟢 已启用")
            self.infrared_enable_btn.setStyleSheet("""
                QPushButton {
                    background-color: #27ae60;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 8px 16px;
                    font-size: 14px;
                    font-weight: bold;
                    min-width: 100px;
                    min-height: 30px;
                }
                QPushButton:hover {
                    background-color: #229954;
                }
                QPushButton:pressed {
                    background-color: #1e8449;
                }
            """)
        else:
            self.infrared_enable_btn.setText("🔴 已禁用")
            self.infrared_enable_btn.setStyleSheet("""
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 8px 16px;
                    font-size: 14px;
                    font-weight: bold;
                    min-width: 100px;
                    min-height: 30px;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                }
                QPushButton:pressed {
                    background-color: #a93226;
                }
            """)
        
        # 延迟更新红外感应器状态
        QTimer.singleShot(100, self.force_update_infrared_status)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    window = InfraredSwitchTest()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()

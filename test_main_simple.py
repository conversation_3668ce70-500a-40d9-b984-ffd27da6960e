#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版主程序测试
"""

import sys
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

class SimpleTest(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("剪切机自动化控制系统 - 简化测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 添加标签
        label = QLabel("剪切机自动化控制系统")
        label.setAlignment(Qt.AlignCenter)
        label.setStyleSheet("font-size: 24px; font-weight: bold; color: #2c3e50; padding: 20px;")
        layout.addWidget(label)
        
        # 添加状态标签
        status_label = QLabel("系统正常运行")
        status_label.setAlignment(Qt.AlignCenter)
        status_label.setStyleSheet("font-size: 16px; color: #27ae60; padding: 10px;")
        layout.addWidget(status_label)
        
        # 添加按钮
        button = QPushButton("测试按钮")
        button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        button.clicked.connect(self.test_click)
        layout.addWidget(button)
        
        # 添加退出按钮
        exit_button = QPushButton("退出")
        exit_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        exit_button.clicked.connect(self.close)
        layout.addWidget(exit_button)
        
        print("简化测试窗口初始化完成")
    
    def test_click(self):
        QMessageBox.information(self, "测试", "按钮点击测试成功！")
        print("测试按钮被点击")

if __name__ == "__main__":
    print("启动简化测试程序...")
    
    app = QApplication(sys.argv)
    
    # 设置应用程序样式
    app.setStyle(QStyleFactory.create('Fusion'))
    
    window = SimpleTest()
    window.show()
    
    print("窗口已显示，等待用户交互...")
    
    sys.exit(app.exec_())

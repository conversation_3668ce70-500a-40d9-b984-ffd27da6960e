#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
界面配置文件
包含界面优化的样式和配置
"""

# 主题颜色配置
THEME_COLORS = {
    'primary': '#3498db',
    'success': '#27ae60',
    'warning': '#f39c12',
    'danger': '#e74c3c',
    'info': '#17a2b8',
    'light': '#f8f9fa',
    'dark': '#343a40',
    'secondary': '#6c757d',
    'background': '#ffffff',
    'border': '#e0e0e0',
    'text': '#2c3e50',
    'text_muted': '#6c757d'
}

# 字体配置
FONT_CONFIG = {
    'family': "'Microsoft YaHei', 'SimHei', sans-serif",
    'size_small': '12px',
    'size_normal': '13px',
    'size_medium': '14px',
    'size_large': '16px',
    'weight_normal': 'normal',
    'weight_bold': 'bold'
}

# 间距配置
SPACING_CONFIG = {
    'small': 5,
    'normal': 10,
    'medium': 15,
    'large': 20,
    'xlarge': 25
}

# 圆角配置
BORDER_RADIUS = {
    'small': '4px',
    'normal': '6px',
    'medium': '8px',
    'large': '12px'
}

# 阴影配置
SHADOW_CONFIG = {
    'light': '0 1px 3px rgba(0,0,0,0.1)',
    'normal': '0 2px 4px rgba(0,0,0,0.1)',
    'medium': '0 4px 8px rgba(0,0,0,0.15)',
    'heavy': '0 8px 16px rgba(0,0,0,0.2)'
}

def get_button_style(color_type='success', size='normal'):
    """
    获取按钮样式

    Args:
        color_type: 颜色类型 ('primary', 'success', 'warning', 'danger', 'info')
        size: 尺寸 ('small', 'normal', 'large')

    Returns:
        str: CSS样式字符串
    """
    colors = {
        'primary': ('#3498db', '#2980b9', '#1f618d'),
        'success': ('#27ae60', '#229954', '#1e8449'),
        'warning': ('#f39c12', '#e67e22', '#d35400'),
        'danger': ('#e74c3c', '#c0392b', '#a93226'),
        'info': ('#17a2b8', '#138496', '#0f6674'),
        'secondary': ('#6c757d', '#5a6268', '#495057')
    }

    sizes = {
        'small': ('8px 14px', '12px', '70px'),
        'normal': ('12px 24px', '14px', '120px'),
        'large': ('16px 32px', '16px', '150px')
    }

    bg_color, hover_color, pressed_color = colors.get(color_type, colors['success'])
    padding, font_size, min_width = sizes.get(size, sizes['normal'])

    return f"""
        QPushButton {{
            background-color: {bg_color};
            color: white;
            border: none;
            border-radius: {BORDER_RADIUS['medium']};
            padding: {padding};
            font-size: {font_size};
            font-weight: {FONT_CONFIG['weight_bold']};
            min-width: {min_width};
            font-family: {FONT_CONFIG['family']};
        }}
        QPushButton:hover {{
            background-color: {hover_color};
        }}
        QPushButton:pressed {{
            background-color: {pressed_color};
        }}
        QPushButton:disabled {{
            background-color: {THEME_COLORS['secondary']};
            color: {THEME_COLORS['text_muted']};
        }}
    """

def get_input_style():
    """获取输入框样式"""
    return f"""
        QDoubleSpinBox, QSpinBox, QLineEdit {{
            border: 2px solid {THEME_COLORS['border']};
            border-radius: {BORDER_RADIUS['normal']};
            padding: 8px 12px;
            font-size: {FONT_CONFIG['size_normal']};
            font-family: {FONT_CONFIG['family']};
            background-color: {THEME_COLORS['background']};
            color: {THEME_COLORS['text']};
            min-height: 20px;
        }}
        QDoubleSpinBox:focus, QSpinBox:focus, QLineEdit:focus {{
            border-color: {THEME_COLORS['primary']};
            background-color: #f8fcff;
        }}
        QDoubleSpinBox::up-button, QSpinBox::up-button,
        QDoubleSpinBox::down-button, QSpinBox::down-button {{
            width: 20px;
            border: none;
            background-color: {THEME_COLORS['light']};
            border-radius: 2px;
        }}
        QDoubleSpinBox::up-button:hover, QSpinBox::up-button:hover,
        QDoubleSpinBox::down-button:hover, QSpinBox::down-button:hover {{
            background-color: {THEME_COLORS['primary']};
        }}
    """

def get_groupbox_style(color_type='primary'):
    """获取分组框样式"""
    border_colors = {
        'primary': '#e8f4fd',
        'success': '#e8f6f3',
        'warning': '#fff2e6',
        'info': '#e8f4fd'
    }

    bg_colors = {
        'primary': '#f8fcff',
        'success': '#f0fdf4',
        'warning': '#fffaf7',
        'info': '#f8fcff'
    }

    border_color = border_colors.get(color_type, border_colors['primary'])
    bg_color = bg_colors.get(color_type, bg_colors['primary'])

    return f"""
        QGroupBox {{
            border: 2px solid {border_color};
            border-radius: {BORDER_RADIUS['medium']};
            margin-top: {SPACING_CONFIG['normal']}px;
            margin-bottom: {SPACING_CONFIG['normal']}px;
            background-color: {bg_color};
            font-size: {FONT_CONFIG['size_medium']};
            font-weight: {FONT_CONFIG['weight_bold']};
            color: {THEME_COLORS['text']};
            font-family: {FONT_CONFIG['family']};
        }}
        QGroupBox::title {{
            subcontrol-origin: margin;
            left: 15px;
            padding: 5px 10px;
            background-color: {THEME_COLORS['background']};
            border-radius: {BORDER_RADIUS['small']};
            border: 1px solid {THEME_COLORS['border']};
        }}
    """

def get_table_style():
    """获取表格样式"""
    return f"""
        QTableWidget {{
            border: 2px solid {THEME_COLORS['border']};
            border-radius: {BORDER_RADIUS['normal']};
            background-color: {THEME_COLORS['background']};
            gridline-color: {THEME_COLORS['light']};
            font-size: {FONT_CONFIG['size_normal']};
            font-family: {FONT_CONFIG['family']};
            selection-background-color: #e8f4fd;
            alternate-background-color: {THEME_COLORS['light']};
        }}
        QTableWidget::item {{
            padding: 10px 8px;
            border-bottom: 1px solid {THEME_COLORS['light']};
            border-right: 1px solid #f8f9fa;
        }}
        QTableWidget::item:selected {{
            background-color: #e8f4fd;
            color: {THEME_COLORS['text']};
        }}
        QTableWidget::item:hover {{
            background-color: #f1f8ff;
        }}
        QHeaderView::section {{
            background-color: {THEME_COLORS['light']};
            border: 1px solid {THEME_COLORS['border']};
            padding: 12px 8px;
            font-weight: {FONT_CONFIG['weight_bold']};
            color: {THEME_COLORS['text']};
            font-size: {FONT_CONFIG['size_normal']};
            font-family: {FONT_CONFIG['family']};
        }}
        QHeaderView::section:hover {{
            background-color: #e9ecef;
        }}
    """

def get_tab_style():
    """获取选项卡样式"""
    return f"""
        QTabWidget::pane {{
            border: 1px solid {THEME_COLORS['border']};
            background-color: {THEME_COLORS['background']};
            border-radius: {BORDER_RADIUS['small']};
        }}
        QTabBar::tab {{
            background-color: {THEME_COLORS['light']};
            border: 1px solid {THEME_COLORS['border']};
            border-bottom: none;
            padding: 12px 20px;
            margin-right: 2px;
            border-top-left-radius: {BORDER_RADIUS['normal']};
            border-top-right-radius: {BORDER_RADIUS['normal']};
            font-size: {FONT_CONFIG['size_medium']};
            font-weight: {FONT_CONFIG['weight_bold']};
            color: #495057;
            min-width: 100px;
            font-family: {FONT_CONFIG['family']};
        }}
        QTabBar::tab:selected {{
            background-color: {THEME_COLORS['background']};
            color: {THEME_COLORS['text']};
            border-bottom: 2px solid {THEME_COLORS['primary']};
        }}
        QTabBar::tab:hover:!selected {{
            background-color: #e9ecef;
            color: {THEME_COLORS['text']};
        }}
    """

def get_statusbar_style():
    """获取状态栏样式"""
    return f"""
        QStatusBar {{
            background-color: {THEME_COLORS['light']};
            border-top: 1px solid {THEME_COLORS['border']};
            color: #495057;
            font-size: {FONT_CONFIG['size_normal']};
            font-family: {FONT_CONFIG['family']};
        }}
        QStatusBar::item {{
            border: none;
        }}
    """

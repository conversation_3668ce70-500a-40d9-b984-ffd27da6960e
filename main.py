import sys
import time
import serial
import serial.tools.list_ports
import json
import os
import datetime
import socket
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                             QLabel, QLineEdit, QPushButton, QComboBox, QGroupBox,
                             QGridLayout, QMessageBox, QSpinBox, QDoubleSpinBox,
                             QTextEdit, QTabWidget, QFrame, QSplitter, QStyleFactory,
                             QTableWidget, QTableWidgetItem, QHeaderView, QFileDialog,
                             QAbstractItemView, QCheckBox, QProgressBar, QInputDialog,
                             QSplashScreen, QDialog, QFormLayout, QScrollArea, QSizePolicy)
# 自定义按钮文本将在各个对话框中直接设置
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont, QIcon, QColor, QPalette, QPixmap
import binascii  # 用于十六进制数据显示
import logging
from logging.handlers import TimedRotatingFileHandler
import math

# 导入自定义模块
from production_order import ProductionOrder
from task_planner import TaskPlanner

class PulseCalculator:
    """脉冲计算器类，用于长度和脉冲之间的转换"""

    def __init__(self, wheel_diameter=48.0, pulses_per_revolution=2000):
        """
        初始化脉冲计算器

        参数:
            wheel_diameter: 滚轮直径(mm)，默认48mm
            pulses_per_revolution: 每圈脉冲数，默认2000
        """
        self.wheel_diameter = wheel_diameter
        self.pulses_per_revolution = pulses_per_revolution
        self.wheel_circumference = math.pi * wheel_diameter  # 滚轮周长
        self.pulses_per_mm = pulses_per_revolution / self.wheel_circumference  # 每毫米脉冲数

        print(f"脉冲计算器初始化:")
        print(f"  滚轮直径: {wheel_diameter}mm")
        print(f"  每圈脉冲数: {pulses_per_revolution}")
        print(f"  滚轮周长: {self.wheel_circumference:.3f}mm")
        print(f"  每毫米脉冲数: {self.pulses_per_mm:.6f}")

    def length_to_pulses(self, length_mm):
        """
        长度转换为脉冲数

        参数:
            length_mm: 长度(毫米)

        返回:
            32位整数脉冲数
        """
        if length_mm <= 0:
            return 0

        pulses = round(length_mm * self.pulses_per_mm)
        return int(pulses)

    def pulses_to_length(self, pulses):
        """
        脉冲数转换为长度

        参数:
            pulses: 脉冲数

        返回:
            长度(毫米)
        """
        if pulses <= 0:
            return 0.0

        return pulses / self.pulses_per_mm

    def calculate_cutting_data(self, first_length, increment_length):
        """
        计算裁剪数据

        参数:
            first_length: 第一根长度(mm)
            increment_length: 递增长度(mm)

        返回:
            包含脉冲数据的字典
        """
        first_pulses = self.length_to_pulses(first_length)
        increment_pulses = self.length_to_pulses(increment_length)

        return {
            'first_length_pulses': first_pulses,
            'increment_pulses': increment_pulses,
            'first_length_mm': first_length,
            'increment_mm': increment_length,
            'first_length_actual': self.pulses_to_length(first_pulses),
            'increment_actual': self.pulses_to_length(increment_pulses)
        }

    def split_32bit_to_16bit(self, value_32bit):
        """
        将32位数值拆分为两个16位数值（高位和低位）

        参数:
            value_32bit: 32位数值

        返回:
            (high_16bit, low_16bit) 元组
        """
        if value_32bit < 0 or value_32bit > 0xFFFFFFFF:
            raise ValueError(f"32位数值超出范围: {value_32bit}")

        high_16bit = (value_32bit >> 16) & 0xFFFF  # 高16位
        low_16bit = value_32bit & 0xFFFF           # 低16位

        return high_16bit, low_16bit

    def combine_16bit_to_32bit(self, high_16bit, low_16bit):
        """
        将两个16位数值合并为32位数值

        参数:
            high_16bit: 高16位
            low_16bit: 低16位

        返回:
            32位数值
        """
        return (high_16bit << 16) | low_16bit

class ModbusRTU:
    """Modbus RTU通信类，用于与DOP-107BV通信"""
    def __init__(self, port=None, baudrate=9600, bytesize=serial.EIGHTBITS,
                 parity=serial.PARITY_NONE, stopbits=serial.STOPBITS_ONE, timeout=0.5):
        # 日志设置
        self.logger = logging.getLogger("ModbusRTU")
        if not self.logger.handlers:
            log_dir = os.path.join(os.getcwd(), "logs")
            if not os.path.exists(log_dir):
                os.makedirs(log_dir)
            handler = TimedRotatingFileHandler(os.path.join(log_dir, "modbus.log"), when="midnight", backupCount=7, encoding="utf-8")
            formatter = logging.Formatter('%(asctime)s %(levelname)s: %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
        self.serial = None
        self.port = port
        self.baudrate = baudrate
        self.bytesize = bytesize
        self.parity = parity
        self.stopbits = stopbits
        self.timeout = timeout
        self.connected = False
        self.rx_data = []  # 接收的数据
        self.tx_data = []  # 发送的数据
        self.error_count = 0  # 错误计数
        self.success_count = 0  # 成功计数
        self.max_retries = 3  # 最大重试次数
        self.max_data_history = 1000  # 最大数据历史记录数量，防止内存泄漏

    def connect(self):
        """连接串口"""
        try:
            if self.is_connected():
                self.disconnect()
            self.serial = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                bytesize=self.bytesize,
                parity=self.parity,
                stopbits=self.stopbits,
                timeout=self.timeout
            )
            self.connected = True
            self.logger.info(f"串口连接成功: {self.port}, 波特率: {self.baudrate}")
            return True
        except Exception as e:
            self.logger.error(f"连接错误: {e}")
            print(f"连接错误: {e}")
            self.connected = False
            return False

    def disconnect(self):
        """断开串口连接"""
        try:
            if self.serial and self.serial.is_open:
                self.serial.close()
                self.logger.info("串口已断开")
        except Exception as e:
            self.logger.error(f"断开连接错误: {e}")
            print(f"断开连接错误: {e}")
        finally:
            self.connected = False

    def is_connected(self):
        """检查是否已连接"""
        try:
            return self.connected and self.serial and self.serial.is_open
        except:
            self.connected = False
            return False

    def calculate_crc(self, data):
        """计算CRC校验码"""
        crc = 0xFFFF
        for byte in data:
            crc ^= byte
            for _ in range(8):
                if crc & 0x0001:
                    crc = (crc >> 1) ^ 0xA001
                else:
                    crc = crc >> 1
        return crc.to_bytes(2, byteorder='little')

    def _manage_data_history(self):
        """管理数据历史记录，防止内存泄漏"""
        try:
            # 如果发送数据超过限制，删除最旧的记录
            if len(self.tx_data) > self.max_data_history:
                excess = len(self.tx_data) - self.max_data_history
                self.tx_data = self.tx_data[excess:]
                self.logger.info(f"清理了 {excess} 条发送数据记录")

            # 如果接收数据超过限制，删除最旧的记录
            if len(self.rx_data) > self.max_data_history:
                excess = len(self.rx_data) - self.max_data_history
                self.rx_data = self.rx_data[excess:]
                self.logger.info(f"清理了 {excess} 条接收数据记录")
        except Exception as e:
            self.logger.error(f"管理数据历史记录时发生错误: {e}")
            print(f"数据历史管理错误: {e}")

    def read_register(self, slave_addr, register_addr, register_count=1):
        """读取寄存器数据"""
        if not self.is_connected():
            self.logger.warning("尝试读取寄存器但串口未连接")
            self.error_count += 1
            return None
        request = bytearray([
            slave_addr, 0x03, register_addr >> 8, register_addr & 0xFF, 0x00, register_count
        ])
        request += self.calculate_crc(request)
        self.tx_data.append({
            'time': time.strftime('%H:%M:%S'),
            'data': binascii.hexlify(request).decode('ascii'),
            'type': '读寄存器'
        })
        self._manage_data_history()  # 管理数据历史，防止内存泄漏
        self.logger.info(f"发送: {binascii.hexlify(request).decode('ascii')}")
        try:
            self.serial.write(request)
            response = self.serial.read(5 + register_count * 2)
            self.rx_data.append({
                'time': time.strftime('%H:%M:%S'),
                'data': binascii.hexlify(response).decode('ascii') if response else 'No response',
                'type': '读寄存器响应'
            })
            self._manage_data_history()  # 管理数据历史，防止内存泄漏
            self.logger.info(f"接收: {binascii.hexlify(response).decode('ascii') if response else 'No response'}")
            if len(response) < 5:
                self.logger.warning("响应数据不完整")
                print("响应数据不完整")
                self.error_count += 1
                return None
            if response[0] != slave_addr or response[1] != 0x03:
                self.logger.warning("响应格式错误")
                print("响应格式错误")
                self.error_count += 1
                return None
            data_length = response[2]
            self.success_count += 1
            data = []
            for i in range(0, data_length, 2):
                if 3 + i + 1 < len(response):
                    value = (response[3 + i] << 8) | response[3 + i + 1]
                    data.append(value)
            return data
        except Exception as e:
            self.logger.error(f"读取寄存器错误: {e}")
            print(f"读取寄存器错误: {e}")
            self.error_count += 1
            return None

    def read_large_value(self, slave_addr, register_addr):
        """读取大数值（32位），从两个连续的寄存器中读取

        与write_large_value方法对应，用于读取跨两个寄存器的大数值
        例如：对于长度值1500000 (15米)，将从两个寄存器读取：
        - 高16位从register_addr读取
        - 低16位从register_addr+1读取
        """
        try:
            # 读取两个连续的寄存器
            data = self.read_register(slave_addr, register_addr, 2)
            if not data or len(data) < 2:
                self.logger.warning("读取大数值失败：数据不完整")
                return None

            # 组合高16位和低16位
            high_word = data[0]
            low_word = data[1]
            value = (high_word << 16) | low_word

            self.logger.info(f"读取大数值: 高16位={high_word}, 低16位={low_word}, 组合值={value}")
            return value
        except Exception as e:
            self.logger.error(f"读取大数值错误: {e}")
            print(f"读取大数值错误: {e}")
            self.error_count += 1
            return None

    def write_register(self, slave_addr, register_addr, value):
        """写入单个寄存器"""
        if not self.is_connected():
            self.logger.warning("尝试写入寄存器但串口未连接")
            self.error_count += 1
            return False

        # 处理大数值 - 如果值超过16位无符号整数范围，使用write_large_value方法
        if isinstance(value, int) and (value < 0 or value > 65535):
            return self.write_large_value(slave_addr, register_addr, value)

        # 确保值在有效范围内（0-65535，16位无符号整数）
        try:
            value = int(value) & 0xFFFF  # 截断为16位无符号整数
            if value < 0 or value > 65535:
                self.logger.warning(f"寄存器值超出范围: {value}，已截断为0-65535")
                value = max(0, min(value, 65535))  # 确保在0-65535范围内
        except Exception as e:
            self.logger.error(f"寄存器值转换错误: {e}")
            print(f"寄存器值转换错误: {e}")
            self.error_count += 1
            return False

        request = bytearray([
            slave_addr, 0x06, register_addr >> 8, register_addr & 0xFF, value >> 8, value & 0xFF
        ])
        request += self.calculate_crc(request)
        self.tx_data.append({
            'time': time.strftime('%H:%M:%S'),
            'data': binascii.hexlify(request).decode('ascii'),
            'type': '写寄存器'
        })
        self._manage_data_history()  # 管理数据历史，防止内存泄漏
        self.logger.info(f"发送: {binascii.hexlify(request).decode('ascii')}")
        try:
            self.serial.write(request)
            response = self.serial.read(8)
            self.rx_data.append({
                'time': time.strftime('%H:%M:%S'),
                'data': binascii.hexlify(response).decode('ascii') if response else 'No response',
                'type': '写寄存器响应'
            })
            self._manage_data_history()  # 管理数据历史，防止内存泄漏
            self.logger.info(f"接收: {binascii.hexlify(response).decode('ascii') if response else 'No response'}")
            if len(response) < 8:
                self.logger.warning("响应数据不完整")
                print("响应数据不完整")
                self.error_count += 1
                return False
            if response[0] != slave_addr or response[1] != 0x06:
                self.logger.warning("响应格式错误")
                print("响应格式错误")
                self.error_count += 1
                return False
            self.success_count += 1
            return True
        except Exception as e:
            self.logger.error(f"写入寄存器错误: {e}")
            print(f"写入寄存器错误: {e}")
            self.error_count += 1
            return False

    def write_large_value(self, slave_addr, register_addr, value):
        """处理大数值，将其拆分为多个寄存器写入

        对于大于65535的值，将其拆分为高16位和低16位分别写入两个连续的寄存器
        例如：对于长度值1500000 (15米)，将拆分为：
        - 高16位: 1500000 >> 16 = 22 写入 register_addr
        - 低16位: 1500000 & 0xFFFF = 53392 写入 register_addr+1
        """
        try:
            # 确保值为整数
            value = int(value)

            # 拆分为高16位和低16位
            high_word = (value >> 16) & 0xFFFF
            low_word = value & 0xFFFF

            self.logger.info(f"大数值 {value} 拆分为: 高16位={high_word}, 低16位={low_word}")

            # 写入高16位到指定寄存器
            high_success = self.write_register(slave_addr, register_addr, high_word)

            # 写入低16位到下一个寄存器
            low_success = self.write_register(slave_addr, register_addr + 1, low_word)

            return high_success and low_success
        except Exception as e:
            self.logger.error(f"写入大数值错误: {e}")
            print(f"写入大数值错误: {e}")
            self.error_count += 1
            return False

    def write_multiple_registers(self, slave_addr, register_addr, values):
        """写入多个寄存器"""
        if not self.is_connected():
            self.logger.warning("尝试写入多个寄存器但串口未连接")
            self.error_count += 1
            return False
        register_count = len(values)
        byte_count = register_count * 2
        request = bytearray([
            slave_addr, 0x10, register_addr >> 8, register_addr & 0xFF, register_count >> 8, register_count & 0xFF, byte_count
        ])
        for value in values:
            request.append(value >> 8)
            request.append(value & 0xFF)
        request += self.calculate_crc(request)
        self.tx_data.append({
            'time': time.strftime('%H:%M:%S'),
            'data': binascii.hexlify(request).decode('ascii'),
            'type': '写多个寄存器'
        })
        self._manage_data_history()  # 管理数据历史，防止内存泄漏
        self.logger.info(f"发送: {binascii.hexlify(request).decode('ascii')}")
        try:
            self.serial.write(request)
            response = self.serial.read(8)
            self.rx_data.append({
                'time': time.strftime('%H:%M:%S'),
                'data': binascii.hexlify(response).decode('ascii') if response else 'No response',
                'type': '写多个寄存器响应'
            })
            self._manage_data_history()  # 管理数据历史，防止内存泄漏
            self.logger.info(f"接收: {binascii.hexlify(response).decode('ascii') if response else 'No response'}")
            if len(response) < 8:
                self.logger.warning("响应数据不完整")
                print("响应数据不完整")
                self.error_count += 1
                return False
            if response[0] != slave_addr or response[1] != 0x10:
                self.logger.warning("响应格式错误")
                print("响应格式错误")
                self.error_count += 1
                return False
            self.success_count += 1
            return True
        except Exception as e:
            self.logger.error(f"写入多个寄存器错误: {e}")
            print(f"写入多个寄存器错误: {e}")
            self.error_count += 1
            return False

    def read_coil(self, slave_addr, coil_addr, coil_count=1):
        """读取线圈状态（位操作）"""
        if not self.is_connected():
            self.logger.warning("尝试读取线圈但串口未连接")
            self.error_count += 1
            return None
        request = bytearray([
            slave_addr, 0x01, coil_addr >> 8, coil_addr & 0xFF, coil_count >> 8, coil_count & 0xFF
        ])
        request += self.calculate_crc(request)
        self.tx_data.append({
            'time': time.strftime('%H:%M:%S'),
            'data': binascii.hexlify(request).decode('ascii'),
            'type': '读线圈'
        })
        self._manage_data_history()  # 管理数据历史，防止内存泄漏
        self.logger.info(f"发送: {binascii.hexlify(request).decode('ascii')}")
        try:
            self.serial.write(request)
            byte_count = (coil_count + 7) // 8  # 计算需要的字节数
            response = self.serial.read(5 + byte_count)
            self.rx_data.append({
                'time': time.strftime('%H:%M:%S'),
                'data': binascii.hexlify(response).decode('ascii') if response else 'No response',
                'type': '读线圈响应'
            })
            self._manage_data_history()  # 管理数据历史，防止内存泄漏
            self.logger.info(f"接收: {binascii.hexlify(response).decode('ascii') if response else 'No response'}")
            if len(response) < 5:
                self.logger.warning("响应数据不完整")
                print("响应数据不完整")
                self.error_count += 1
                return None
            if response[0] != slave_addr or response[1] != 0x01:
                self.logger.warning("响应格式错误")
                print("响应格式错误")
                self.error_count += 1
                return None
            data_length = response[2]
            self.success_count += 1
            # 解析位数据
            coil_values = []
            for i in range(coil_count):
                byte_index = i // 8
                bit_index = i % 8
                if 3 + byte_index < len(response):
                    bit_value = (response[3 + byte_index] >> bit_index) & 1
                    coil_values.append(bit_value)
            return coil_values
        except Exception as e:
            self.logger.error(f"读取线圈错误: {e}")
            print(f"读取线圈错误: {e}")
            self.error_count += 1
            return None

    def write_coil(self, slave_addr, coil_addr, value):
        """写入单个线圈（位操作）"""
        if not self.is_connected():
            self.logger.warning("尝试写入线圈但串口未连接")
            self.error_count += 1
            return False

        # 线圈值：0x0000 = OFF, 0xFF00 = ON
        coil_value = 0xFF00 if value else 0x0000

        request = bytearray([
            slave_addr, 0x05, coil_addr >> 8, coil_addr & 0xFF,
            coil_value >> 8, coil_value & 0xFF
        ])
        request += self.calculate_crc(request)
        self.tx_data.append({
            'time': time.strftime('%H:%M:%S'),
            'data': binascii.hexlify(request).decode('ascii'),
            'type': '写线圈'
        })
        self._manage_data_history()  # 管理数据历史，防止内存泄漏
        self.logger.info(f"发送: {binascii.hexlify(request).decode('ascii')}")
        try:
            self.serial.write(request)
            response = self.serial.read(8)
            self.rx_data.append({
                'time': time.strftime('%H:%M:%S'),
                'data': binascii.hexlify(response).decode('ascii') if response else 'No response',
                'type': '写线圈响应'
            })
            self._manage_data_history()  # 管理数据历史，防止内存泄漏
            self.logger.info(f"接收: {binascii.hexlify(response).decode('ascii') if response else 'No response'}")
            if len(response) < 8:
                self.logger.warning("响应数据不完整")
                print("响应数据不完整")
                self.error_count += 1
                return False
            if response[0] != slave_addr or response[1] != 0x05:
                self.logger.warning("响应格式错误")
                print("响应格式错误")
                self.error_count += 1
                return False
            self.success_count += 1
            return True
        except Exception as e:
            self.logger.error(f"写入线圈错误: {e}")
            print(f"写入线圈错误: {e}")
            self.error_count += 1
            return False

    def read_double_word_register(self, slave_addr, register_addr):
        """读取双字寄存器（32位）- 直接读取32位数据"""
        try:
            # 读取两个连续的寄存器来获取32位数据
            data = self.read_register(slave_addr, register_addr, 2)
            if not data or len(data) < 2:
                self.logger.warning("读取双字寄存器失败：数据不完整")
                return None

            # 组合高16位和低16位（高位在前）
            high_word = data[0]
            low_word = data[1]
            value = (high_word << 16) | low_word

            self.logger.info(f"读取32位寄存器 {register_addr}: 高16位={high_word}, 低16位={low_word}, 32位值={value}")
            return value
        except Exception as e:
            self.logger.error(f"读取32位寄存器错误: {e}")
            print(f"读取32位寄存器错误: {e}")
            self.error_count += 1
            return None

    def write_double_word_register(self, slave_addr, register_addr, value):
        """写入双字寄存器（32位）- 直接写入32位数据"""
        try:
            # 确保值为整数
            value = int(value)

            # 拆分为高16位和低16位
            high_word = (value >> 16) & 0xFFFF
            low_word = value & 0xFFFF

            self.logger.info(f"写入32位寄存器 {register_addr}: 32位值={value}, 高16位={high_word}, 低16位={low_word}")

            # 使用写多个寄存器功能一次性写入两个寄存器
            return self.write_multiple_registers(slave_addr, register_addr, [high_word, low_word])
        except Exception as e:
            self.logger.error(f"写入32位寄存器错误: {e}")
            print(f"写入32位寄存器错误: {e}")
            self.error_count += 1
            return False


class ModbusTCP:
    """Modbus TCP通信类，用于TCP/IP网络通信"""
    def __init__(self, host='*************', port=502, timeout=5.0):
        # 日志设置
        self.logger = logging.getLogger("ModbusTCP")
        if not self.logger.handlers:
            log_dir = os.path.join(os.getcwd(), "logs")
            if not os.path.exists(log_dir):
                os.makedirs(log_dir)
            handler = TimedRotatingFileHandler(os.path.join(log_dir, "modbus_tcp.log"), when="midnight", backupCount=7, encoding="utf-8")
            formatter = logging.Formatter('%(asctime)s %(levelname)s: %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)

        self.socket = None
        self.host = host
        self.port = port
        self.timeout = timeout
        self.connected = False
        self.rx_data = []  # 接收的数据
        self.tx_data = []  # 发送的数据
        self.error_count = 0  # 错误计数
        self.success_count = 0  # 成功计数
        self.max_retries = 3  # 最大重试次数
        self.max_data_history = 1000  # 最大数据历史记录数量，防止内存泄漏
        self.transaction_id = 0  # 事务标识符
        self.last_activity_time = time.time()  # 最后活动时间，用于心跳检测
        self.connection_time = None  # 连接建立时间，用于稳定性检查
        self.min_stable_time = 2.0  # 连接后的最小稳定时间（秒）

    def connect(self):
        """连接TCP服务器"""
        try:
            if self.is_connected():
                self.disconnect()

            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(self.timeout)

            # 设置socket选项，提高连接稳定性
            self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)
            if hasattr(socket, 'TCP_KEEPIDLE'):
                self.socket.setsockopt(socket.IPPROTO_TCP, socket.TCP_KEEPIDLE, 30)
            if hasattr(socket, 'TCP_KEEPINTVL'):
                self.socket.setsockopt(socket.IPPROTO_TCP, socket.TCP_KEEPINTVL, 10)
            if hasattr(socket, 'TCP_KEEPCNT'):
                self.socket.setsockopt(socket.IPPROTO_TCP, socket.TCP_KEEPCNT, 3)

            self.socket.connect((self.host, self.port))
            self.connected = True
            self.connection_time = time.time()  # 记录连接建立时间
            self.last_activity_time = self.connection_time  # 更新活动时间

            self.logger.info(f"TCP连接成功: {self.host}:{self.port}")
            return True
        except Exception as e:
            self.logger.error(f"TCP连接错误: {e}")
            print(f"TCP连接错误: {e}")
            self.connected = False
            self.connection_time = None
            return False

    def disconnect(self):
        """断开TCP连接"""
        try:
            if self.socket:
                self.socket.close()
                self.logger.info("TCP连接已断开")
        except Exception as e:
            self.logger.error(f"断开TCP连接错误: {e}")
            print(f"断开TCP连接错误: {e}")
        finally:
            self.connected = False

    def is_connected(self):
        """检查是否已连接 - 增强版本"""
        try:
            # 多重检查确保连接状态准确
            if not self.connected:
                return False

            if not self.socket:
                self.connected = False
                return False

            # 检查socket文件描述符是否有效
            try:
                if self.socket.fileno() == -1:
                    self.connected = False
                    self.logger.warning("Socket文件描述符无效，标记为未连接")
                    return False
            except Exception as fd_error:
                self.connected = False
                self.logger.warning(f"检查socket文件描述符时发生错误: {fd_error}")
                return False

            return True

        except Exception as e:
            self.logger.error(f"检查TCP连接状态时发生错误: {e}")
            self.connected = False
            return False

    def _get_next_transaction_id(self):
        """获取下一个事务ID"""
        self.transaction_id = (self.transaction_id + 1) % 65536
        return self.transaction_id

    def _manage_data_history(self):
        """管理数据历史记录，防止内存泄漏"""
        try:
            # 如果发送数据超过限制，删除最旧的记录
            if len(self.tx_data) > self.max_data_history:
                excess = len(self.tx_data) - self.max_data_history
                self.tx_data = self.tx_data[excess:]
                self.logger.info(f"清理了 {excess} 条发送数据记录")

            # 如果接收数据超过限制，删除最旧的记录
            if len(self.rx_data) > self.max_data_history:
                excess = len(self.rx_data) - self.max_data_history
                self.rx_data = self.rx_data[excess:]
                self.logger.info(f"清理了 {excess} 条接收数据记录")
        except Exception as e:
            self.logger.error(f"管理数据历史记录时发生错误: {e}")
            print(f"数据历史管理错误: {e}")

    def check_connection_health(self):
        """检查TCP连接健康状态 - 优化版本，避免干扰正常通信"""
        try:
            if not self.socket:
                return False

            # 简化连接检查，只检查socket状态，不发送测试数据
            try:
                # 使用socket的内置方法检查连接状态
                if self.socket.fileno() == -1:
                    self.logger.warning("Socket文件描述符无效，连接可能已断开")
                    return False

                # 检查是否超过活动间隔（60秒，比之前更宽松）
                current_time = time.time()
                if current_time - self.last_activity_time > 60:
                    self.logger.warning("TCP连接长时间无活动，可能已断开")
                    return False

                return True

            except Exception as socket_error:
                self.logger.warning(f"Socket状态检查失败: {socket_error}")
                return False

        except Exception as e:
            self.logger.error(f"检查连接健康状态时发生错误: {e}")
            return False

    def update_activity_time(self):
        """更新最后活动时间"""
        self.last_activity_time = time.time()

    def is_connection_stable(self):
        """检查连接是否已稳定（连接后需要等待一段时间）"""
        try:
            if not self.connection_time:
                return False

            # 连接后等待最小稳定时间
            elapsed = time.time() - self.connection_time
            is_stable = elapsed >= self.min_stable_time

            if not is_stable:
                self.logger.debug(f"连接尚未稳定，已等待{elapsed:.1f}秒，需要{self.min_stable_time}秒")

            return is_stable

        except Exception as e:
            self.logger.error(f"检查连接稳定性时发生错误: {e}")
            return True  # 出错时假设已稳定，避免阻塞通信

    def validate_register_data(self, data, expected_length=None):
        """验证寄存器数据的有效性"""
        try:
            if not data:
                return False, "数据为空"

            if not isinstance(data, (list, tuple)):
                return False, "数据格式错误，应为列表或元组"

            if expected_length and len(data) != expected_length:
                return False, f"数据长度错误，期望{expected_length}，实际{len(data)}"

            # 检查数据范围（16位无符号整数）
            for i, value in enumerate(data):
                if not isinstance(value, (int, float)):
                    return False, f"数据项{i}类型错误，应为数字"

                if value < 0 or value > 65535:
                    return False, f"数据项{i}超出范围(0-65535): {value}"

            return True, "数据有效"

        except Exception as e:
            return False, f"数据验证异常: {e}"

    def safe_data_conversion(self, value, conversion_factor=1.0, default_value=0.0):
        """安全的数据转换，包含异常处理"""
        try:
            if value is None:
                return default_value

            # 转换为浮点数
            converted = float(value) * conversion_factor

            # 检查结果是否为有效数字
            if not isinstance(converted, (int, float)) or converted != converted:  # NaN检查
                return default_value

            return converted

        except (ValueError, TypeError, OverflowError) as e:
            print(f"数据转换错误: {e}, 使用默认值: {default_value}")
            return default_value

    def _build_tcp_header(self, pdu_length, unit_id=1):
        """构建TCP头部"""
        transaction_id = self._get_next_transaction_id()
        protocol_id = 0  # Modbus协议标识符
        length = pdu_length + 1  # PDU长度 + 单元标识符
        return bytearray([
            transaction_id >> 8, transaction_id & 0xFF,  # 事务标识符
            protocol_id >> 8, protocol_id & 0xFF,       # 协议标识符
            length >> 8, length & 0xFF,                 # 长度
            unit_id                                      # 单元标识符
        ])

    def read_register(self, slave_addr, register_addr, register_count=1):
        """读取寄存器数据"""
        if not self.is_connected():
            self.logger.warning("尝试读取寄存器但TCP未连接")
            self.error_count += 1
            return None

        # 检查连接稳定性
        if not self.is_connection_stable():
            self.logger.debug("连接尚未稳定，跳过本次读取")
            return None

        # 构建PDU
        pdu = bytearray([0x03, register_addr >> 8, register_addr & 0xFF, 0x00, register_count])
        # 构建完整的TCP请求
        request = self._build_tcp_header(len(pdu), slave_addr) + pdu

        self.tx_data.append({
            'time': time.strftime('%H:%M:%S'),
            'data': binascii.hexlify(request).decode('ascii'),
            'type': '读寄存器'
        })
        self._manage_data_history()  # 管理数据历史，防止内存泄漏
        self.logger.info(f"发送: {binascii.hexlify(request).decode('ascii')}")

        try:
            self.socket.send(request)
            self.update_activity_time()  # 更新活动时间
            response = self.socket.recv(1024)
            if response:
                self.update_activity_time()  # 更新活动时间
            self.rx_data.append({
                'time': time.strftime('%H:%M:%S'),
                'data': binascii.hexlify(response).decode('ascii') if response else 'No response',
                'type': '读寄存器响应'
            })
            self._manage_data_history()  # 管理数据历史，防止内存泄漏
            self.logger.info(f"接收: {binascii.hexlify(response).decode('ascii') if response else 'No response'}")

            if len(response) < 9:  # TCP头部(7字节) + 功能码(1字节) + 数据长度(1字节)
                self.logger.warning("响应数据不完整")
                print("响应数据不完整")
                self.error_count += 1
                return None

            # 解析TCP头部
            if response[7] != 0x03:  # 功能码
                self.logger.warning("响应格式错误")
                print("响应格式错误")
                self.error_count += 1
                return None

            data_length = response[8]
            self.success_count += 1
            data = []
            for i in range(0, data_length, 2):
                if 9 + i + 1 < len(response):
                    value = (response[9 + i] << 8) | response[9 + i + 1]
                    data.append(value)
            return data
        except Exception as e:
            self.logger.error(f"读取寄存器错误: {e}")
            print(f"读取寄存器错误: {e}")
            self.error_count += 1
            return None

    def read_large_value(self, slave_addr, register_addr):
        """读取大数值（32位），从两个连续的寄存器中读取"""
        try:
            data = self.read_register(slave_addr, register_addr, 2)
            if not data or len(data) < 2:
                self.logger.warning("读取大数值失败：数据不完整")
                return None

            high_word = data[0]
            low_word = data[1]
            value = (high_word << 16) | low_word

            self.logger.info(f"读取大数值: 高16位={high_word}, 低16位={low_word}, 组合值={value}")
            return value
        except Exception as e:
            self.logger.error(f"读取大数值错误: {e}")
            print(f"读取大数值错误: {e}")
            self.error_count += 1
            return None

    def write_register(self, slave_addr, register_addr, value):
        """写入单个寄存器"""
        if not self.is_connected():
            self.logger.warning("尝试写入寄存器但TCP未连接")
            self.error_count += 1
            return False

        # 处理大数值
        if isinstance(value, int) and (value < 0 or value > 65535):
            return self.write_large_value(slave_addr, register_addr, value)

        try:
            value = int(value) & 0xFFFF
            if value < 0 or value > 65535:
                self.logger.warning(f"寄存器值超出范围: {value}，已截断为0-65535")
                value = max(0, min(value, 65535))
        except Exception as e:
            self.logger.error(f"寄存器值转换错误: {e}")
            print(f"寄存器值转换错误: {e}")
            self.error_count += 1
            return False

        # 构建PDU
        pdu = bytearray([0x06, register_addr >> 8, register_addr & 0xFF, value >> 8, value & 0xFF])
        # 构建完整的TCP请求
        request = self._build_tcp_header(len(pdu), slave_addr) + pdu

        self.tx_data.append({
            'time': time.strftime('%H:%M:%S'),
            'data': binascii.hexlify(request).decode('ascii'),
            'type': '写寄存器'
        })
        self._manage_data_history()  # 管理数据历史，防止内存泄漏
        self.logger.info(f"发送: {binascii.hexlify(request).decode('ascii')}")

        try:
            self.socket.send(request)
            response = self.socket.recv(1024)
            self.rx_data.append({
                'time': time.strftime('%H:%M:%S'),
                'data': binascii.hexlify(response).decode('ascii') if response else 'No response',
                'type': '写寄存器响应'
            })
            self._manage_data_history()  # 管理数据历史，防止内存泄漏
            self.logger.info(f"接收: {binascii.hexlify(response).decode('ascii') if response else 'No response'}")

            if len(response) < 12:  # TCP头部(7字节) + PDU(5字节)
                self.logger.warning("响应数据不完整")
                print("响应数据不完整")
                self.error_count += 1
                return False

            if response[7] != 0x06:  # 功能码
                self.logger.warning("响应格式错误")
                print("响应格式错误")
                self.error_count += 1
                return False

            self.success_count += 1
            return True
        except Exception as e:
            self.logger.error(f"写入寄存器错误: {e}")
            print(f"写入寄存器错误: {e}")
            self.error_count += 1
            return False

    def write_large_value(self, slave_addr, register_addr, value):
        """处理大数值，将其拆分为多个寄存器写入"""
        try:
            value = int(value)
            high_word = (value >> 16) & 0xFFFF
            low_word = value & 0xFFFF

            self.logger.info(f"大数值 {value} 拆分为: 高16位={high_word}, 低16位={low_word}")

            high_success = self.write_register(slave_addr, register_addr, high_word)
            low_success = self.write_register(slave_addr, register_addr + 1, low_word)

            return high_success and low_success
        except Exception as e:
            self.logger.error(f"写入大数值错误: {e}")
            print(f"写入大数值错误: {e}")
            self.error_count += 1
            return False

    def write_multiple_registers(self, slave_addr, register_addr, values):
        """写入多个寄存器"""
        if not self.is_connected():
            self.logger.warning("尝试写入多个寄存器但TCP未连接")
            self.error_count += 1
            return False

        register_count = len(values)
        byte_count = register_count * 2

        # 构建PDU
        pdu = bytearray([0x10, register_addr >> 8, register_addr & 0xFF,
                        register_count >> 8, register_count & 0xFF, byte_count])
        for value in values:
            pdu.append(value >> 8)
            pdu.append(value & 0xFF)

        # 构建完整的TCP请求
        request = self._build_tcp_header(len(pdu), slave_addr) + pdu

        self.tx_data.append({
            'time': time.strftime('%H:%M:%S'),
            'data': binascii.hexlify(request).decode('ascii'),
            'type': '写多个寄存器'
        })
        self.logger.info(f"发送: {binascii.hexlify(request).decode('ascii')}")

        try:
            self.socket.send(request)
            response = self.socket.recv(1024)
            self.rx_data.append({
                'time': time.strftime('%H:%M:%S'),
                'data': binascii.hexlify(response).decode('ascii') if response else 'No response',
                'type': '写多个寄存器响应'
            })
            self.logger.info(f"接收: {binascii.hexlify(response).decode('ascii') if response else 'No response'}")

            if len(response) < 12:  # TCP头部(7字节) + PDU(5字节)
                self.logger.warning("响应数据不完整")
                print("响应数据不完整")
                self.error_count += 1
                return False

            if response[7] != 0x10:  # 功能码
                self.logger.warning("响应格式错误")
                print("响应格式错误")
                self.error_count += 1
                return False

            self.success_count += 1
            return True
        except Exception as e:
            self.logger.error(f"写入多个寄存器错误: {e}")
            print(f"写入多个寄存器错误: {e}")
            self.error_count += 1
            return False

    def read_coil(self, slave_addr, coil_addr, coil_count=1):
        """读取线圈状态（位操作）"""
        if not self.is_connected():
            self.logger.warning("尝试读取线圈但TCP未连接")
            self.error_count += 1
            return None

        # 构建PDU
        pdu = bytearray([0x01, coil_addr >> 8, coil_addr & 0xFF, coil_count >> 8, coil_count & 0xFF])
        # 构建完整的TCP请求
        request = self._build_tcp_header(len(pdu), slave_addr) + pdu

        self.tx_data.append({
            'time': time.strftime('%H:%M:%S'),
            'data': binascii.hexlify(request).decode('ascii'),
            'type': '读线圈'
        })
        self.logger.info(f"发送: {binascii.hexlify(request).decode('ascii')}")

        try:
            self.socket.send(request)
            response = self.socket.recv(1024)
            self.rx_data.append({
                'time': time.strftime('%H:%M:%S'),
                'data': binascii.hexlify(response).decode('ascii') if response else 'No response',
                'type': '读线圈响应'
            })
            self.logger.info(f"接收: {binascii.hexlify(response).decode('ascii') if response else 'No response'}")

            if len(response) < 9:
                self.logger.warning("响应数据不完整")
                print("响应数据不完整")
                self.error_count += 1
                return None

            if response[7] != 0x01:  # 功能码
                self.logger.warning("响应格式错误")
                print("响应格式错误")
                self.error_count += 1
                return None

            data_length = response[8]
            self.success_count += 1

            # 解析位数据
            coil_values = []
            for i in range(coil_count):
                byte_index = i // 8
                bit_index = i % 8
                if 9 + byte_index < len(response):
                    bit_value = (response[9 + byte_index] >> bit_index) & 1
                    coil_values.append(bit_value)
            return coil_values
        except Exception as e:
            self.logger.error(f"读取线圈错误: {e}")
            print(f"读取线圈错误: {e}")
            self.error_count += 1
            return None

    def write_coil(self, slave_addr, coil_addr, value):
        """写入单个线圈（位操作）"""
        if not self.is_connected():
            self.logger.warning("尝试写入线圈但TCP未连接")
            self.error_count += 1
            return False

        # 线圈值：0x0000 = OFF, 0xFF00 = ON
        coil_value = 0xFF00 if value else 0x0000

        # 构建PDU
        pdu = bytearray([0x05, coil_addr >> 8, coil_addr & 0xFF,
                        coil_value >> 8, coil_value & 0xFF])
        # 构建完整的TCP请求
        request = self._build_tcp_header(len(pdu), slave_addr) + pdu

        self.tx_data.append({
            'time': time.strftime('%H:%M:%S'),
            'data': binascii.hexlify(request).decode('ascii'),
            'type': '写线圈'
        })
        self.logger.info(f"发送: {binascii.hexlify(request).decode('ascii')}")

        try:
            self.socket.send(request)
            response = self.socket.recv(1024)
            self.rx_data.append({
                'time': time.strftime('%H:%M:%S'),
                'data': binascii.hexlify(response).decode('ascii') if response else 'No response',
                'type': '写线圈响应'
            })
            self.logger.info(f"接收: {binascii.hexlify(response).decode('ascii') if response else 'No response'}")

            if len(response) < 12:  # TCP头部(7字节) + PDU(5字节)
                self.logger.warning("响应数据不完整")
                print("响应数据不完整")
                self.error_count += 1
                return False

            if response[7] != 0x05:  # 功能码
                self.logger.warning("响应格式错误")
                print("响应格式错误")
                self.error_count += 1
                return False

            self.success_count += 1
            return True
        except Exception as e:
            self.logger.error(f"写入线圈错误: {e}")
            print(f"写入线圈错误: {e}")
            self.error_count += 1
            return False

    def read_double_word_register(self, slave_addr, register_addr):
        """读取双字寄存器（32位）"""
        try:
            data = self.read_register(slave_addr, register_addr, 2)
            if not data or len(data) < 2:
                self.logger.warning("读取双字寄存器失败：数据不完整")
                return None

            high_word = data[0]
            low_word = data[1]
            value = (high_word << 16) | low_word

            self.logger.info(f"读取32位寄存器 {register_addr}: 高16位={high_word}, 低16位={low_word}, 32位值={value}")
            return value
        except Exception as e:
            self.logger.error(f"读取32位寄存器错误: {e}")
            print(f"读取32位寄存器错误: {e}")
            self.error_count += 1
            return None

    def write_double_word_register(self, slave_addr, register_addr, value):
        """写入双字寄存器（32位）"""
        try:
            value = int(value)
            high_word = (value >> 16) & 0xFFFF
            low_word = value & 0xFFFF

            self.logger.info(f"写入32位寄存器 {register_addr}: 32位值={value}, 高16位={high_word}, 低16位={low_word}")

            return self.write_multiple_registers(slave_addr, register_addr, [high_word, low_word])
        except Exception as e:
            self.logger.error(f"写入32位寄存器错误: {e}")
            print(f"写入32位寄存器错误: {e}")
            self.error_count += 1
            return False


class CuttingMachineController(QMainWindow):
    """剪切机控制软件主界面"""
    def __init__(self):
        super().__init__()

        # 通讯协议类型和对象
        self.protocol_type = "RTU"  # 默认使用RTU协议
        self.modbus = ModbusRTU()
        self.modbus_tcp = ModbusTCP()

        # 配置文件路径
        self.config_file = "config.json"

        # 默认寄存器地址映射
        self.register_map = {
            'length': 42092,     # 剪切长度寄存器地址 (HD1004)
            'increment': 42094,  # 递增量寄存器地址 (HD1006)
            'speed': 42088,      # 伺服速度寄存器地址 (HD1000)
            'error_adjust': 1004, # 误差调整寄存器地址
            'roots': 42098,      # 根数寄存器地址 (HD1010)
            'machines': 42104,   # 台数寄存器地址 (HD1016)
            # 启动停止和复位已改为位地址控制，不再使用寄存器
            'current_length': 3000, # 当前长度寄存器地址
            'completed_count': 42100, # 已完成数量寄存器地址 (HD1012)
            'machine_status': 3002   # 机器状态寄存器地址
        }

        # 默认位地址映射
        self.bit_map = {
            'manual_auto_switch': 10,    # 手自动切换
            'host_reset_button': 11,     # 上位机复位按钮
            'resetting': 12,             # 复位中
            'standby': 13,               # 待机中
            'auto_running': 14,          # 自动运行中
            'emergency_stop': 15,        # 急停中
            'start': 16,                 # 启动位地址 (M16)
            'stop': 17,                  # 停止位地址 (M17)
            'infrared_sensor': 7         # 红外感应器输入 (X7)
        }

        # 数据类型配置
        self.data_types = {
            'double_word_registers': [42088, 42092, 42094, 42098, 42104, 42100],  # 双字寄存器
            'single_word_registers': [],                                           # 单字寄存器
            'read_only_registers': [42100],                                        # 只读寄存器 (已完成数量)
            'read_write_bits': [10, 11, 16, 17],                                  # 读写位 (包含启动停止)
            'read_only_bits': [12, 13, 14, 15, 7]                                # 只读位 (包含红外感应器X7)
        }

        # 初始化排产单和任务计划管理器
        self.production_order = ProductionOrder()
        self.task_planner = TaskPlanner()

        # 当前选中的任务索引
        self.current_task_index = -1

        # 红外感应功能开关
        self.infrared_enabled = True  # 默认启用红外感应

        # 加载配置
        self.load_config()

        # 设置应用程序样式
        self.set_application_style()

        self.init_ui()

        # 定时器用于状态更新
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)

        # 定时器用于端口监控更新
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self.update_port_monitor)
        self.monitor_timer.start(2000)  # 每2秒更新一次，降低更新频率

        # 定时器用于状态栏时间更新
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_status_time)
        self.time_timer.start(1000)  # 每秒更新一次时间

        # 自动重连相关变量
        self.auto_reconnect_enabled = False  # 自动重连开关
        self.reconnect_attempts = 0  # 当前重连尝试次数
        self.max_reconnect_attempts = 3  # 最大重连次数（减少到3次）
        self.reconnect_interval = 10  # 重连间隔（增加到10秒）
        self.last_connection_params = {}  # 保存最后的连接参数
        self.was_connected_before = False  # 标记之前是否连接过
        self.connection_failure_count = 0  # 连续连接失败计数
        self.last_disconnect_time = 0  # 最后断开时间，用于防止频繁重连

        # 定时器用于自动重连
        self.reconnect_timer = QTimer()
        self.reconnect_timer.timeout.connect(self.attempt_reconnect)
        self.reconnect_timer.setSingleShot(True)  # 单次触发

        # 定时器用于定期资源清理
        self.cleanup_timer = QTimer()
        self.cleanup_timer.timeout.connect(self.periodic_cleanup)
        self.cleanup_timer.start(300000)  # 每5分钟清理一次

        # 定时器管理器 - 统一管理所有定时器
        self.active_timers = {
            'status': self.status_timer,
            'monitor': self.monitor_timer,
            'time': self.time_timer,
            'reconnect': self.reconnect_timer,
            'cleanup': self.cleanup_timer
        }
        self.dialog_timers = []  # 存储对话框相关的定时器

        # 初始化脉冲计算器
        self.pulse_calculator = PulseCalculator(
            wheel_diameter=48.0,      # 滚轮直径48mm
            pulses_per_revolution=2000 # 步进电机2000脉冲/圈
        )

        # 初始化端口信息显示
        self.update_port_info_display()

    def register_dialog_timer(self, timer):
        """注册对话框定时器，用于统一管理"""
        try:
            if timer not in self.dialog_timers:
                self.dialog_timers.append(timer)
                print(f"注册对话框定时器，当前共有 {len(self.dialog_timers)} 个对话框定时器")
        except Exception as e:
            print(f"注册对话框定时器时发生错误: {e}")

    def cleanup_dialog_timers(self):
        """清理所有对话框定时器"""
        try:
            cleaned_count = 0
            for timer in self.dialog_timers[:]:  # 使用切片复制避免修改列表时的问题
                if timer and timer.isActive():
                    timer.stop()
                    cleaned_count += 1
                self.dialog_timers.remove(timer)

            if cleaned_count > 0:
                print(f"清理了 {cleaned_count} 个对话框定时器")
                self.add_monitor_message(f"清理了 {cleaned_count} 个对话框定时器")
        except Exception as e:
            print(f"清理对话框定时器时发生错误: {e}")

    def stop_all_timers(self):
        """停止所有定时器"""
        try:
            stopped_count = 0
            # 停止主要定时器
            for name, timer in self.active_timers.items():
                if timer and timer.isActive():
                    timer.stop()
                    stopped_count += 1
                    print(f"停止定时器: {name}")

            # 停止对话框定时器
            for timer in self.dialog_timers:
                if timer and timer.isActive():
                    timer.stop()
                    stopped_count += 1

            print(f"总共停止了 {stopped_count} 个定时器")
        except Exception as e:
            print(f"停止定时器时发生错误: {e}")

    def cleanup_resources(self):
        """清理所有资源，防止内存泄漏"""
        try:
            print("开始清理系统资源...")

            # 清理通信数据历史
            if hasattr(self, 'modbus'):
                try:
                    initial_tx = len(self.modbus.tx_data)
                    initial_rx = len(self.modbus.rx_data)
                    self.modbus.tx_data.clear()
                    self.modbus.rx_data.clear()
                    print(f"清理RTU通信数据: 发送{initial_tx}条, 接收{initial_rx}条")
                except Exception as e:
                    print(f"清理RTU数据时发生错误: {e}")

            if hasattr(self, 'modbus_tcp'):
                try:
                    initial_tx = len(self.modbus_tcp.tx_data)
                    initial_rx = len(self.modbus_tcp.rx_data)
                    self.modbus_tcp.tx_data.clear()
                    self.modbus_tcp.rx_data.clear()
                    print(f"清理TCP通信数据: 发送{initial_tx}条, 接收{initial_rx}条")
                except Exception as e:
                    print(f"清理TCP数据时发生错误: {e}")

            # 停止所有定时器
            self.stop_all_timers()

            # 清理对话框定时器
            self.cleanup_dialog_timers()

            # 强制垃圾回收
            import gc
            collected = gc.collect()
            print(f"垃圾回收释放了 {collected} 个对象")

            print("系统资源清理完成")

        except Exception as e:
            print(f"清理系统资源时发生错误: {e}")

    def periodic_cleanup(self):
        """定期清理，防止长时间运行导致的内存泄漏"""
        try:
            # 清理通信数据历史
            if hasattr(self, 'modbus'):
                self.modbus._manage_data_history()

            if hasattr(self, 'modbus_tcp'):
                self.modbus_tcp._manage_data_history()

            # 检查并清理无效的对话框定时器
            invalid_timers = []
            for timer in self.dialog_timers:
                if not timer or not timer.isActive():
                    invalid_timers.append(timer)

            for timer in invalid_timers:
                if timer in self.dialog_timers:
                    self.dialog_timers.remove(timer)

            if invalid_timers:
                print(f"定期清理：移除了 {len(invalid_timers)} 个无效的对话框定时器")

            # 强制垃圾回收（轻量级）
            import gc
            if gc.get_count()[0] > 1000:  # 只在对象数量较多时才执行
                collected = gc.collect()
                if collected > 0:
                    print(f"定期清理：垃圾回收释放了 {collected} 个对象")
                    self.add_monitor_message(f"定期清理：释放了 {collected} 个对象")

        except Exception as e:
            print(f"定期清理时发生错误: {e}")

    def write_32bit_value(self, register_start_addr, value_32bit):
        """
        将32位数值写入两个连续的16位寄存器

        参数:
            register_start_addr: 起始寄存器地址（会占用此地址和下一个地址）
            value_32bit: 32位数值

        返回:
            写入是否成功
        """
        try:
            current_modbus = self.get_current_modbus()
            if not current_modbus.is_connected():
                self.add_monitor_message("❌ 未连接PLC，无法写入32位数据")
                return False

            # 拆分32位数值为两个16位数值
            high_16bit, low_16bit = self.pulse_calculator.split_32bit_to_16bit(value_32bit)

            slave_addr = self.slave_addr_spin.value()

            # 写入低位寄存器（起始地址）
            success_low = current_modbus.write_register(slave_addr, register_start_addr, low_16bit)
            if not success_low:
                self.add_monitor_message(f"❌ 写入低位寄存器失败: 地址{register_start_addr}")
                return False

            # 写入高位寄存器（起始地址+1）
            success_high = current_modbus.write_register(slave_addr, register_start_addr + 1, high_16bit)
            if not success_high:
                self.add_monitor_message(f"❌ 写入高位寄存器失败: 地址{register_start_addr + 1}")
                return False

            self.add_monitor_message(f"✅ 32位数据写入成功: {value_32bit}")
            self.add_monitor_message(f"   寄存器{register_start_addr}(低位): {low_16bit}")
            self.add_monitor_message(f"   寄存器{register_start_addr + 1}(高位): {high_16bit}")
            return True

        except Exception as e:
            self.add_monitor_message(f"❌ 写入32位数据时发生错误: {e}")
            print(f"写入32位数据错误: {e}")
            return False

    def send_pulse_data_to_plc(self, first_length_mm, increment_mm):
        """
        发送脉冲数据到PLC

        参数:
            first_length_mm: 第一根长度(毫米)
            increment_mm: 递增长度(毫米)

        返回:
            发送是否成功
        """
        try:
            # 计算脉冲数据
            cutting_data = self.pulse_calculator.calculate_cutting_data(first_length_mm, increment_mm)

            first_pulses = cutting_data['first_length_pulses']
            increment_pulses = cutting_data['increment_pulses']

            # 检查脉冲数是否超出16位范围
            if first_pulses > 65535:
                self.add_monitor_message(f"⚠️ 警告：第一根脉冲数({first_pulses})超出16位范围，将被截断")
                first_pulses = first_pulses & 0xFFFF

            if abs(increment_pulses) > 32767:  # 有符号16位范围
                self.add_monitor_message(f"⚠️ 警告：递增脉冲数({increment_pulses})超出16位有符号范围，将被截断")
                increment_pulses = max(-32768, min(32767, increment_pulses))

            self.add_monitor_message("=" * 50)
            self.add_monitor_message("📊 脉冲数据计算结果:")
            self.add_monitor_message(f"  第一根长度: {first_length_mm}mm -> {first_pulses}脉冲")
            self.add_monitor_message(f"  递增长度: {increment_mm}mm -> {increment_pulses}脉冲")
            self.add_monitor_message(f"  实际第一根长度: {cutting_data['first_length_actual']:.3f}mm")
            self.add_monitor_message(f"  实际递增长度: {cutting_data['increment_actual']:.3f}mm")
            self.add_monitor_message(f"  目标寄存器: HD1004({self.register_map['first_length_pulses']}) 和 HD1006({self.register_map['increment_pulses']})")

            current_modbus = self.get_current_modbus()
            if not current_modbus.is_connected():
                self.add_monitor_message("❌ 未连接PLC，无法发送脉冲数据")
                return False

            slave_addr = self.slave_addr_spin.value()

            # 发送第一根长度脉冲数据到HD1004
            first_success = current_modbus.write_register(
                slave_addr,
                self.register_map['first_length_pulses'],
                first_pulses
            )

            if not first_success:
                self.add_monitor_message("❌ 发送第一根长度脉冲数据失败")
                return False

            # 发送递增长度脉冲数据到HD1006
            increment_success = current_modbus.write_register(
                slave_addr,
                self.register_map['increment_pulses'],
                increment_pulses
            )

            if not increment_success:
                self.add_monitor_message("❌ 发送递增长度脉冲数据失败")
                return False

            self.add_monitor_message("🎉 脉冲数据发送成功！")
            self.add_monitor_message(f"  HD1004 = {first_pulses} (第一根脉冲)")
            self.add_monitor_message(f"  HD1006 = {increment_pulses} (递增脉冲)")
            self.add_monitor_message("=" * 50)

            return True

        except Exception as e:
            self.add_monitor_message(f"❌ 发送脉冲数据时发生错误: {e}")
            print(f"发送脉冲数据错误: {e}")
            return False

    def send_pulse_data(self):
        """发送脉冲数据按钮的响应函数"""
        try:
            # 获取当前参数值
            first_length = self.length_spin.value()  # 第一根长度
            increment = self.increment_spin.value()  # 递增量

            if first_length <= 0:
                QMessageBox.warning(self, "参数错误", "第一根长度必须大于0")
                return

            # 显示确认对话框
            reply = QMessageBox.question(
                self,
                "确认发送脉冲数据",
                f"即将发送以下脉冲数据到PLC:\n\n"
                f"第一根长度: {first_length} mm\n"
                f"递增长度: {increment} mm\n\n"
                f"确定要发送吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # 发送脉冲数据
            success = self.send_pulse_data_to_plc(first_length, increment)

            if success:
                QMessageBox.information(
                    self,
                    "发送成功",
                    "脉冲数据已成功发送到PLC！\n\n"
                    "PLC将根据这些数据计算每根的裁剪长度。"
                )
            else:
                QMessageBox.critical(
                    self,
                    "发送失败",
                    "脉冲数据发送失败！\n\n"
                    "请检查PLC连接状态和寄存器配置。"
                )

        except Exception as e:
            QMessageBox.critical(
                self,
                "发送错误",
                f"发送脉冲数据时发生错误:\n{str(e)}"
            )
            print(f"发送脉冲数据错误: {e}")

    def smart_write_register(self, slave_addr, register_addr, value):
        """智能写入寄存器，根据配置自动选择单字或双字操作"""
        try:
            # 检查是否为双字寄存器
            if register_addr in self.data_types.get('double_word_registers', []):
                return self.modbus.write_double_word_register(slave_addr, register_addr, value)
            else:
                return self.modbus.write_register(slave_addr, register_addr, value)
        except Exception as e:
            print(f"智能写入寄存器错误: {e}")
            return False

    def smart_read_register(self, slave_addr, register_addr):
        """智能读取寄存器，根据配置自动选择单字或双字操作"""
        try:
            # 检查是否为双字寄存器
            if register_addr in self.data_types.get('double_word_registers', []):
                return self.modbus.read_double_word_register(slave_addr, register_addr)
            else:
                data = self.modbus.read_register(slave_addr, register_addr, 1)
                return data[0] if data and len(data) > 0 else None
        except Exception as e:
            print(f"智能读取寄存器错误: {e}")
            return None

    def smart_write_bit(self, slave_addr, bit_addr, value):
        """智能写入位，检查读写权限"""
        try:
            # 检查是否为只读位
            if bit_addr in self.data_types.get('read_only_bits', []):
                print(f"位地址 {bit_addr} 为只读，无法写入")
                return False

            # 检查是否为读写位
            if bit_addr in self.data_types.get('read_write_bits', []):
                return self.modbus.write_coil(slave_addr, bit_addr, value)
            else:
                print(f"位地址 {bit_addr} 不在配置的读写位列表中")
                return False
        except Exception as e:
            print(f"智能写入位错误: {e}")
            return False

    def smart_read_bit(self, slave_addr, bit_addr):
        """智能读取位"""
        try:
            data = self.modbus.read_coil(slave_addr, bit_addr, 1)
            return data[0] if data and len(data) > 0 else None
        except Exception as e:
            print(f"智能读取位错误: {e}")
            return None

    def check_infrared_sensor(self):
        """检查红外感应器状态 (X7) - 增强异常处理版本

        Returns:
            True: 检测到物料
            False: 未检测到物料
            None: 读取失败或功能禁用
        """
        try:
            # 检查红外感应功能是否启用
            if not self.infrared_enabled:
                # 功能禁用时更新状态显示
                try:
                    if hasattr(self, 'infrared_status_label') and self.infrared_status_label:
                        self.infrared_status_label.setText("⚫ 已禁用")
                        self.infrared_status_label.setStyleSheet("color: #6c757d; font-weight: bold; font-size: 13px;")
                except Exception as ui_error:
                    print(f"更新红外感应器禁用状态显示时发生错误: {ui_error}")
                return None

            current_modbus = self.get_current_modbus()
            if not current_modbus.is_connected():
                # 未连接时显示未知状态
                try:
                    if hasattr(self, 'infrared_status_label') and self.infrared_status_label:
                        self.infrared_status_label.setText("❓ 未连接")
                        self.infrared_status_label.setStyleSheet("color: #f39c12; font-weight: bold; font-size: 13px;")
                except Exception as ui_error:
                    print(f"更新红外感应器未连接状态显示时发生错误: {ui_error}")
                return None

            try:
                slave_addr = self.slave_addr_spin.value()
                infrared_addr = self.bit_map['infrared_sensor']

                # 读取X7输入状态
                data = current_modbus.read_coil(slave_addr, infrared_addr, 1)
                if data and len(data) > 0:
                    status = data[0]
                    # 更新红外感应器状态显示
                    try:
                        if hasattr(self, 'infrared_status_label') and self.infrared_status_label:
                            if status:
                                self.infrared_status_label.setText("🟢 有料")
                                self.infrared_status_label.setStyleSheet("color: #27ae60; font-weight: bold; font-size: 13px;")
                            else:
                                self.infrared_status_label.setText("🔴 无料")
                                self.infrared_status_label.setStyleSheet("color: #e74c3c; font-weight: bold; font-size: 13px;")
                    except Exception as ui_error:
                        print(f"更新红外感应器状态显示时发生错误: {ui_error}")
                    return status
                else:
                    # 读取失败时显示错误状态
                    try:
                        if hasattr(self, 'infrared_status_label') and self.infrared_status_label:
                            self.infrared_status_label.setText("❌ 读取失败")
                            self.infrared_status_label.setStyleSheet("color: #e74c3c; font-weight: bold; font-size: 13px;")
                    except Exception as ui_error:
                        print(f"更新红外感应器读取失败状态显示时发生错误: {ui_error}")
                    return None
            except Exception as read_error:
                print(f"读取红外感应器数据时发生错误: {read_error}")
                # 读取失败时显示错误状态
                try:
                    if hasattr(self, 'infrared_status_label') and self.infrared_status_label:
                        self.infrared_status_label.setText("❌ 读取错误")
                        self.infrared_status_label.setStyleSheet("color: #e74c3c; font-weight: bold; font-size: 13px;")
                except Exception as ui_error:
                    print(f"更新红外感应器读取错误状态显示时发生错误: {ui_error}")
                return None
        except Exception as e:
            print(f"检查红外感应器状态主函数发生错误: {e}")
            # 异常时显示错误状态
            try:
                if hasattr(self, 'infrared_status_label') and self.infrared_status_label:
                    self.infrared_status_label.setText("❌ 检测异常")
                    self.infrared_status_label.setStyleSheet("color: #e74c3c; font-weight: bold; font-size: 13px;")
            except Exception as ui_error:
                print(f"更新红外感应器异常状态显示时发生错误: {ui_error}")
            return None

    def toggle_infrared_function(self):
        """切换红外感应功能开关"""
        try:
            self.infrared_enabled = not self.infrared_enabled

            if self.infrared_enabled:
                # 启用红外感应功能
                self.infrared_enable_btn.setText("🟢 已启用")
                self.infrared_enable_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #27ae60;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        padding: 4px 12px;
                        font-size: 12px;
                        font-weight: bold;
                        min-width: 80px;
                        max-height: 24px;
                    }
                    QPushButton:hover {
                        background-color: #229954;
                    }
                    QPushButton:pressed {
                        background-color: #1e8449;
                    }
                """)
                self.add_monitor_message("✅ 红外感应功能已启用")
                self.status_label.setText("红外感应功能已启用")

                # 启用后立即检查并更新红外感应器状态
                QTimer.singleShot(100, self.force_update_infrared_status)
            else:
                # 禁用红外感应功能
                self.infrared_enable_btn.setText("🔴 已禁用")
                self.infrared_enable_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #e74c3c;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        padding: 4px 12px;
                        font-size: 12px;
                        font-weight: bold;
                        min-width: 80px;
                        max-height: 24px;
                    }
                    QPushButton:hover {
                        background-color: #c0392b;
                    }
                    QPushButton:pressed {
                        background-color: #a93226;
                    }
                """)
                self.add_monitor_message("⚠️ 红外感应功能已禁用")
                self.status_label.setText("红外感应功能已禁用")

                # 禁用后立即更新状态显示为"已禁用"
                if hasattr(self, 'infrared_status_label'):
                    self.infrared_status_label.setText("⚫ 已禁用")
                    self.infrared_status_label.setStyleSheet("color: #6c757d; font-weight: bold; font-size: 13px;")

            # 保存配置
            self.save_config()

        except Exception as e:
            print(f"切换红外感应功能错误: {e}")
            QMessageBox.critical(self, "错误", f"切换红外感应功能时发生错误: {str(e)}")

    def force_update_infrared_status(self):
        """强制更新红外感应器状态显示"""
        try:
            if self.infrared_enabled:
                # 如果功能已启用，重新检查实际状态
                current_modbus = self.get_current_modbus()
                if current_modbus.is_connected():
                    self.check_infrared_sensor()
                else:
                    # 未连接时显示默认状态
                    if hasattr(self, 'infrared_status_label'):
                        self.infrared_status_label.setText("🔴 无料")
                        self.infrared_status_label.setStyleSheet("color: #e74c3c; font-weight: bold; font-size: 13px;")
            else:
                # 功能禁用时显示禁用状态
                if hasattr(self, 'infrared_status_label'):
                    self.infrared_status_label.setText("⚫ 已禁用")
                    self.infrared_status_label.setStyleSheet("color: #6c757d; font-weight: bold; font-size: 13px;")
        except Exception as e:
            print(f"强制更新红外感应器状态错误: {e}")

    def update_infrared_button_state(self):
        """更新红外感应功能按钮状态"""
        try:
            if hasattr(self, 'infrared_enable_btn'):
                if self.infrared_enabled:
                    self.infrared_enable_btn.setText("🟢 已启用")
                    self.infrared_enable_btn.setStyleSheet("""
                        QPushButton {
                            background-color: #27ae60;
                            color: white;
                            border: none;
                            border-radius: 4px;
                            padding: 4px 12px;
                            font-size: 12px;
                            font-weight: bold;
                            min-width: 80px;
                            max-height: 24px;
                        }
                        QPushButton:hover {
                            background-color: #229954;
                        }
                        QPushButton:pressed {
                            background-color: #1e8449;
                        }
                    """)
                else:
                    self.infrared_enable_btn.setText("🔴 已禁用")
                    self.infrared_enable_btn.setStyleSheet("""
                        QPushButton {
                            background-color: #e74c3c;
                            color: white;
                            border: none;
                            border-radius: 4px;
                            padding: 4px 12px;
                            font-size: 12px;
                            font-weight: bold;
                            min-width: 80px;
                            max-height: 24px;
                        }
                        QPushButton:hover {
                            background-color: #c0392b;
                        }
                        QPushButton:pressed {
                            background-color: #a93226;
                        }
                    """)

                # 延迟更新红外感应器状态显示，确保界面已完全初始化
                QTimer.singleShot(300, self.force_update_infrared_status)
        except Exception as e:
            print(f"更新红外感应按钮状态错误: {e}")

    def update_status_time(self):
        """更新状态栏时间显示 - 增强异常处理版本"""
        try:
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            if hasattr(self, 'status_time_label') and self.status_time_label:
                self.status_time_label.setText(f"🕒 {current_time}")
        except Exception as e:
            print(f"更新时间显示错误: {e}")
            # 时间更新失败不应该影响程序运行，静默处理

    def closeEvent(self, event):
        """应用程序关闭事件，保存分割器状态"""
        try:
            print("正在关闭应用程序...")

            # 使用统一的资源清理方法
            self.cleanup_resources()

            # 保存分割器状态
            if hasattr(self, 'main_splitter'):
                self.save_config()

            # 断开连接
            try:
                if hasattr(self, 'modbus') and self.modbus.is_connected():
                    self.modbus.disconnect()
                if hasattr(self, 'modbus_tcp') and self.modbus_tcp.is_connected():
                    self.modbus_tcp.disconnect()
            except Exception as disconnect_error:
                print(f"断开连接时发生错误: {disconnect_error}")

            print("应用程序关闭完成")
            event.accept()
        except Exception as e:
            print(f"关闭应用程序时发生错误: {e}")
            event.accept()

    def set_application_style(self):
        """设置应用程序样式为扁平化现代风格"""
        # 设置应用程序样式为Fusion（扁平化风格）
        QApplication.setStyle(QStyleFactory.create('Fusion'))

        # 创建自定义调色板 - 使用白色背景
        palette = QPalette()
        palette.setColor(QPalette.Window, Qt.white)
        palette.setColor(QPalette.WindowText, Qt.black)
        palette.setColor(QPalette.Base, Qt.white)
        palette.setColor(QPalette.AlternateBase, QColor(240, 240, 240))
        palette.setColor(QPalette.ToolTipBase, Qt.white)
        palette.setColor(QPalette.ToolTipText, Qt.black)
        palette.setColor(QPalette.Text, Qt.black)
        palette.setColor(QPalette.Button, QColor(240, 240, 240))
        palette.setColor(QPalette.ButtonText, Qt.black)
        palette.setColor(QPalette.BrightText, Qt.red)
        palette.setColor(QPalette.Link, QColor(42, 130, 218))
        palette.setColor(QPalette.Highlight, QColor(42, 130, 218))
        palette.setColor(QPalette.HighlightedText, Qt.white)

        # 应用调色板
        QApplication.setPalette(palette)

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("剪切机自动化控制系统 v3.2")
        self.setGeometry(100, 100, 1200, 800)  # 适当增大默认窗口大小
        self.setMinimumSize(1000, 700)  # 增大最小窗口大小以适应更多内容

        # 设置窗口图标
        try:
            if os.path.exists("icon.ico"):
                self.setWindowIcon(QIcon("icon.ico"))
        except Exception as e:
            print(f"设置窗口图标失败: {e}")

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建选项卡部件
        tab_widget = QTabWidget()
        tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #e0e0e0;
                background-color: white;
                border-radius: 4px;
            }
            QTabBar::tab {
                background-color: #f8f9fa;
                border: 1px solid #e0e0e0;
                border-bottom: none;
                padding: 12px 20px;
                margin-right: 2px;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
                font-size: 14px;
                font-weight: bold;
                color: #495057;
                min-width: 100px;
            }
            QTabBar::tab:selected {
                background-color: white;
                color: #2c3e50;
                border-bottom: 2px solid #3498db;
            }
            QTabBar::tab:hover:!selected {
                background-color: #e9ecef;
                color: #2c3e50;
            }
        """)

        # 主控制选项卡 - 使用QSplitter实现可拖拽调整大小的布局
        control_tab = QWidget()
        control_tab_layout = QVBoxLayout(control_tab)
        control_tab_layout.setContentsMargins(0, 0, 0, 0)
        control_tab_layout.setSpacing(0)

        # 创建垂直分割器，允许用户拖拽调整通讯数据区域和其他内容的大小比例
        splitter = QSplitter(Qt.Vertical)
        splitter.setHandleWidth(8)  # 设置拖拽条宽度
        splitter.setChildrenCollapsible(False)  # 防止子部件完全收缩
        splitter.setStyleSheet("""
            QSplitter::handle {
                background-color: #ecf0f1;
                border: 1px solid #bdc3c7;
                border-radius: 3px;
                height: 8px;
                margin: 1px;
                image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==);
            }
            QSplitter::handle:hover {
                background-color: #3498db;
                border-color: #2980b9;
            }
            QSplitter::handle:pressed {
                background-color: #2980b9;
                border-color: #1f618d;
            }
            QSplitter::handle:vertical {
                height: 8px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #ecf0f1, stop:0.5 #bdc3c7, stop:1 #ecf0f1);
            }
            QSplitter::handle:vertical:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:0.5 #2980b9, stop:1 #3498db);
            }
        """)

        # 创建通讯数据区域（上半部分）
        comm_widget = QWidget()
        comm_widget.setMinimumHeight(100)  # 设置最小高度，防止完全收缩
        comm_widget.setStyleSheet("""
            QWidget {
                background-color: white;
                border-bottom: 1px solid #e0e0e0;
            }
        """)
        comm_layout = QVBoxLayout(comm_widget)
        comm_layout.setContentsMargins(10, 10, 10, 10)
        comm_layout.setSpacing(5)

        # 将通讯数据区域添加到上半部分
        self.create_port_monitor_ui(comm_layout)
        splitter.addWidget(comm_widget)

        # 创建滚动区域用于其他内容（下半部分）
        control_scroll = QScrollArea()
        control_scroll.setWidgetResizable(True)
        control_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        control_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        control_scroll.setMinimumHeight(200)  # 设置最小高度，确保内容可见

        # 创建滚动内容部件
        control_content = QWidget()
        main_layout = QVBoxLayout(control_content)
        main_layout.setSpacing(15)  # 增加间距，避免拥挤
        main_layout.setContentsMargins(20, 20, 20, 20)  # 增加边距，留白更多

        # 设置滚动区域
        control_scroll.setWidget(control_content)
        splitter.addWidget(control_scroll)

        # 设置初始分割比例：通讯数据区域占25%，其他内容占75%
        splitter.setSizes([180, 520])

        # 设置分割器的拉伸因子，使两个区域都可以调整大小
        splitter.setStretchFactor(0, 1)  # 通讯数据区域
        splitter.setStretchFactor(1, 3)  # 其他内容区域，增加权重

        # 保存分割器引用，以便后续保存/恢复设置
        self.main_splitter = splitter

        # 从配置中恢复分割器位置
        self.restore_splitter_state()

        # 将分割器添加到主布局
        control_tab_layout.addWidget(splitter)

        # 端口配置选项卡
        port_config_tab = QWidget()
        port_config_layout = QVBoxLayout(port_config_tab)

        # 排产单管理选项卡
        production_order_tab = QWidget()
        production_order_layout = QVBoxLayout(production_order_tab)

        # 任务计划选项卡
        task_plan_tab = QWidget()
        task_plan_layout = QVBoxLayout(task_plan_tab)

        # 添加选项卡（使用图标和文字）
        tab_widget.addTab(control_tab, "🔧 参数测试")
        tab_widget.addTab(task_plan_tab, "📋 任务计划")
        tab_widget.addTab(production_order_tab, "📊 排产单管理")
        tab_widget.addTab(port_config_tab, "⚙️ 端口配置")

        # 设置中央部件布局
        central_layout = QVBoxLayout(central_widget)
        central_layout.addWidget(tab_widget)
        central_layout.setContentsMargins(5, 5, 5, 5)

        # 创建状态栏
        self.statusBar = self.statusBar()
        self.statusBar.setStyleSheet("""
            QStatusBar {
                background-color: #f8f9fa;
                border-top: 1px solid #e0e0e0;
                color: #495057;
                font-size: 13px;
            }
            QStatusBar::item {
                border: none;
            }
        """)

        # 状态标签
        self.status_label = QLabel("🟢 系统就绪")
        self.status_label.setStyleSheet("color: #28a745; font-weight: bold; padding: 4px 8px;")
        self.statusBar.addWidget(self.status_label)

        # 添加分隔符
        self.statusBar.addWidget(QLabel("|"))

        # 连接状态标签
        self.status_connection_label = QLabel("⚫ 未连接")
        self.status_connection_label.setStyleSheet("color: #6c757d; padding: 4px 8px;")
        self.statusBar.addWidget(self.status_connection_label)

        # 添加弹性空间
        self.statusBar.addPermanentWidget(QLabel(""))

        # 时间标签
        self.status_time_label = QLabel()
        self.status_time_label.setStyleSheet("color: #6c757d; padding: 4px 8px;")
        self.statusBar.addPermanentWidget(self.status_time_label)

        # 更新时间显示
        self.update_status_time()

        # 创建排产单管理界面
        self.create_production_order_ui(production_order_layout)

        # 创建任务计划界面
        self.create_task_plan_ui(task_plan_layout)

        # 创建端口配置界面
        self.create_port_config_ui(port_config_layout)

        # 串口连接设置区域
        connection_group = QGroupBox("🔗 串口连接")
        connection_group.setStyleSheet("""
            QGroupBox {
                border: 2px solid #e8f4fd;
                border-radius: 8px;
                margin-top: 10px;
                margin-bottom: 10px;
                background-color: #f8fcff;
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 5px 10px;
                background-color: white;
                border-radius: 4px;
            }
        """)
        connection_layout = QHBoxLayout()
        connection_layout.setSpacing(20)  # 增加间距
        connection_layout.setContentsMargins(20, 20, 20, 15)  # 增加内边距

        # 串口信息显示
        self.port_info_label = QLabel("端口: 未配置")
        self.port_info_label.setStyleSheet("color: #3498db; font-weight: bold;")
        connection_layout.addWidget(self.port_info_label)

        # 波特率信息显示
        self.baudrate_info_label = QLabel("波特率: 未配置")
        self.baudrate_info_label.setStyleSheet("color: #3498db; font-weight: bold;")
        connection_layout.addWidget(self.baudrate_info_label)

        # 隐藏的串口选择组件（为了保持代码兼容性）
        self.port_combo = QComboBox()
        self.port_combo.hide()

        # 隐藏的波特率选择组件（为了保持代码兼容性）
        self.baudrate_combo = QComboBox()
        for baudrate in [4800, 9600, 19200, 38400, 57600, 115200]:
            self.baudrate_combo.addItem(str(baudrate))
        self.baudrate_combo.setCurrentText("9600")  # 默认波特率
        self.baudrate_combo.hide()

        # 从站地址
        self.slave_addr_spin = QSpinBox()
        self.slave_addr_spin.setStyleSheet(
            "QSpinBox {border: 1px solid #3498db; border-radius: 3px; padding: 5px;}"
            "QSpinBox::up-button, QSpinBox::down-button {width: 16px;}"
        )
        self.slave_addr_spin.setRange(1, 247)
        self.slave_addr_spin.setValue(1)
        connection_layout.addWidget(QLabel("从站地址:"))
        connection_layout.addWidget(self.slave_addr_spin)

        # 连接按钮
        self.connect_btn = QPushButton("连接")
        self.connect_btn.setStyleSheet(
            "QPushButton {background-color: #3498db; color: white; border-radius: 3px; padding: 5px 15px;}"
            "QPushButton:hover {background-color: #2980b9;}"
            "QPushButton:pressed {background-color: #1c6ea4;}"
        )
        self.connect_btn.clicked.connect(self.toggle_connection)
        connection_layout.addWidget(self.connect_btn)

        # 自动更新状态复选框
        self.auto_update_check = QCheckBox("自动更新状态")
        self.auto_update_check.setChecked(True)
        self.auto_update_check.setToolTip("选中时，自动读取设备状态；取消选中可减少通信量")
        self.auto_update_check.stateChanged.connect(self.toggle_auto_update)
        connection_layout.addWidget(self.auto_update_check)

        # 自动重连复选框
        self.auto_reconnect_check = QCheckBox("自动重连")
        self.auto_reconnect_check.setChecked(False)
        self.auto_reconnect_check.setToolTip("选中时，连接断开后自动尝试重新连接")
        self.auto_reconnect_check.stateChanged.connect(self.toggle_auto_reconnect)
        connection_layout.addWidget(self.auto_reconnect_check)

        # 刷新串口按钮
        refresh_btn = QPushButton("刷新")
        refresh_btn.setStyleSheet(
            "QPushButton {background-color: #2ecc71; color: white; border-radius: 3px; padding: 5px 15px;}"
            "QPushButton:hover {background-color: #27ae60;}"
            "QPushButton:pressed {background-color: #1e8449;}"
        )
        refresh_btn.clicked.connect(self.refresh_ports)
        connection_layout.addWidget(refresh_btn)

        connection_group.setLayout(connection_layout)
        main_layout.addWidget(connection_group)

        # 基本参数设置区域
        basic_params_group = QGroupBox("⚙️ 基本参数")
        basic_params_group.setStyleSheet("""
            QGroupBox {
                border: 2px solid #e8f6f3;
                border-radius: 8px;
                margin-top: 10px;
                margin-bottom: 10px;
                background-color: #f0fdf4;
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 5px 10px;
                background-color: white;
                border-radius: 4px;
            }
        """)
        basic_params_layout = QGridLayout()
        basic_params_layout.setSpacing(15)  # 增加间距
        basic_params_layout.setContentsMargins(20, 20, 20, 15)  # 增加内边距

        # 统一的输入框样式
        input_style = """
            QDoubleSpinBox, QSpinBox {
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 13px;
                background-color: white;
                min-height: 20px;
            }
            QDoubleSpinBox:focus, QSpinBox:focus {
                border-color: #27ae60;
                background-color: #f8fff8;
            }
            QDoubleSpinBox::up-button, QSpinBox::up-button,
            QDoubleSpinBox::down-button, QSpinBox::down-button {
                width: 20px;
                border: none;
                background-color: #f0f0f0;
            }
            QDoubleSpinBox::up-button:hover, QSpinBox::up-button:hover,
            QDoubleSpinBox::down-button:hover, QSpinBox::down-button:hover {
                background-color: #27ae60;
            }
        """

        # 剪切长度
        length_label = QLabel("剪切长度 (mm)")
        length_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 13px;")
        self.length_spin = QDoubleSpinBox()
        self.length_spin.setStyleSheet(input_style)
        self.length_spin.setRange(0, 99999.99)
        self.length_spin.setDecimals(2)
        self.length_spin.setSingleStep(0.01)
        self.length_spin.setValue(100.00)
        basic_params_layout.addWidget(length_label, 0, 0)
        basic_params_layout.addWidget(self.length_spin, 0, 1)

        # 递增量
        increment_label = QLabel("递增量 (mm)")
        increment_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 13px;")
        self.increment_spin = QDoubleSpinBox()
        self.increment_spin.setStyleSheet(input_style)
        self.increment_spin.setRange(-100, 100)
        self.increment_spin.setDecimals(2)
        self.increment_spin.setSingleStep(0.01)
        self.increment_spin.setValue(0.00)
        basic_params_layout.addWidget(increment_label, 0, 2)
        basic_params_layout.addWidget(self.increment_spin, 0, 3)

        # 伺服速度
        speed_label = QLabel("伺服速度 (%)")
        speed_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 13px;")
        self.speed_spin = QSpinBox()
        self.speed_spin.setStyleSheet(input_style)
        self.speed_spin.setRange(1, 100)
        self.speed_spin.setValue(50)
        basic_params_layout.addWidget(speed_label, 1, 0)
        basic_params_layout.addWidget(self.speed_spin, 1, 1)

        # 误差调整
        error_adjust_label = QLabel("误差调整 (mm)")
        error_adjust_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 13px;")
        self.error_adjust_spin = QDoubleSpinBox()
        self.error_adjust_spin.setStyleSheet(input_style)
        self.error_adjust_spin.setRange(-10, 10)
        self.error_adjust_spin.setDecimals(2)
        self.error_adjust_spin.setSingleStep(0.01)
        self.error_adjust_spin.setValue(0.00)
        basic_params_layout.addWidget(error_adjust_label, 1, 2)
        basic_params_layout.addWidget(self.error_adjust_spin, 1, 3)

        # 根数和台数（简化为一行）
        roots_label = QLabel("根数")
        roots_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 13px;")
        self.roots_spin = QSpinBox()
        self.roots_spin.setStyleSheet(input_style)
        self.roots_spin.setRange(1, 100)
        self.roots_spin.setValue(1)
        basic_params_layout.addWidget(roots_label, 2, 0)
        basic_params_layout.addWidget(self.roots_spin, 2, 1)

        machines_label = QLabel("台数")
        machines_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 13px;")
        self.machines_spin = QSpinBox()
        self.machines_spin.setStyleSheet(input_style)
        self.machines_spin.setRange(1, 100)
        self.machines_spin.setValue(1)
        basic_params_layout.addWidget(machines_label, 2, 2)
        basic_params_layout.addWidget(self.machines_spin, 2, 3)

        # 基本参数按钮区域
        basic_buttons_layout = QHBoxLayout()
        basic_buttons_layout.setSpacing(15)
        basic_buttons_layout.addStretch()  # 左侧弹性空间

        # 统一的按钮样式
        button_style = """
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: bold;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
                color: #7f8c8d;
            }
        """

        # 读取参数按钮
        self.read_params_btn = QPushButton("📖 读取参数")
        self.read_params_btn.setStyleSheet(button_style.replace("#27ae60", "#3498db").replace("#229954", "#2980b9").replace("#1e8449", "#1f618d"))
        self.read_params_btn.clicked.connect(self.read_parameters)
        self.read_params_btn.setEnabled(False)
        self.read_params_btn.setToolTip("读取剪切机当前参数设置")
        basic_buttons_layout.addWidget(self.read_params_btn)

        # 应用参数按钮
        self.apply_params_btn = QPushButton("💾 应用参数")
        self.apply_params_btn.setStyleSheet(button_style)
        self.apply_params_btn.clicked.connect(self.apply_parameters)
        self.apply_params_btn.setEnabled(False)
        self.apply_params_btn.setToolTip("将参数写入剪切机")
        basic_buttons_layout.addWidget(self.apply_params_btn)

        # 发送脉冲数据按钮
        self.send_pulse_btn = QPushButton("发送脉冲数据")
        self.send_pulse_btn.setStyleSheet("""
            QPushButton {
                background-color: #e67e22;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
                font-size: 12px;
                font-weight: bold;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #d35400;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
                color: #7f8c8d;
            }
        """)
        self.send_pulse_btn.clicked.connect(self.send_pulse_data)
        self.send_pulse_btn.setEnabled(False)
        self.send_pulse_btn.setToolTip("将长度转换为脉冲数据发送给PLC\n第一根长度 → HD1004\n递增长度 → HD1006")
        basic_buttons_layout.addWidget(self.send_pulse_btn)

        basic_buttons_layout.addStretch()  # 右侧弹性空间
        basic_params_layout.addLayout(basic_buttons_layout, 3, 0, 1, 4)

        # 脉冲数据说明
        pulse_info_label = QLabel("💡 脉冲数据说明：点击'发送脉冲数据'将长度转换为脉冲数并写入HD1004(第一根)和HD1006(递增)")
        pulse_info_label.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                font-size: 11px;
                font-style: italic;
                padding: 5px;
                background-color: #ecf0f1;
                border-radius: 3px;
                border-left: 3px solid #e67e22;
            }
        """)
        pulse_info_label.setWordWrap(True)
        basic_params_layout.addWidget(pulse_info_label, 4, 0, 1, 4)

        basic_params_group.setLayout(basic_params_layout)
        main_layout.addWidget(basic_params_group)

        # 为了保持兼容性，创建隐藏的测试参数控件
        self.start_stop_value_spin = QSpinBox()
        self.start_stop_value_spin.setRange(0, 1)
        self.start_stop_value_spin.setValue(0)
        self.start_stop_value_spin.hide()

        self.reset_value_spin = QSpinBox()
        self.reset_value_spin.setRange(0, 1)
        self.reset_value_spin.setValue(0)
        self.reset_value_spin.hide()

        self.current_length_test_spin = QDoubleSpinBox()
        self.current_length_test_spin.setRange(0, 99999.99)
        self.current_length_test_spin.setDecimals(2)
        self.current_length_test_spin.setValue(0.00)
        self.current_length_test_spin.hide()

        self.completed_count_test_spin = QSpinBox()
        self.completed_count_test_spin.setRange(0, 99999)
        self.completed_count_test_spin.setValue(0)
        self.completed_count_test_spin.hide()

        self.machine_status_test_spin = QSpinBox()
        self.machine_status_test_spin.setRange(0, 10)
        self.machine_status_test_spin.setValue(0)
        self.machine_status_test_spin.hide()

        # 为了保持兼容性，创建隐藏的按钮
        self.apply_all_params_btn = QPushButton()
        self.apply_all_params_btn.clicked.connect(self.apply_all_parameters)
        self.apply_all_params_btn.setEnabled(False)
        self.apply_all_params_btn.hide()

        self.read_all_params_btn = QPushButton()
        self.read_all_params_btn.clicked.connect(self.read_all_parameters)
        self.read_all_params_btn.setEnabled(False)
        self.read_all_params_btn.hide()

        # 高级测试区域（可折叠）
        advanced_test_group = QGroupBox("🔧 高级测试")
        advanced_test_group.setStyleSheet("""
            QGroupBox {
                border: 2px solid #f0f0f0;
                border-radius: 8px;
                margin-top: 10px;
                margin-bottom: 10px;
                background-color: #fafafa;
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 5px 10px;
                background-color: white;
                border-radius: 4px;
            }
        """)
        register_test_layout = QGridLayout()
        register_test_layout.setSpacing(15)
        register_test_layout.setContentsMargins(20, 20, 20, 15)

        # 简化的寄存器测试输入
        test_input_layout = QHBoxLayout()

        # 寄存器地址
        addr_label = QLabel("地址:")
        addr_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 13px;")
        self.register_addr_spin = QSpinBox()
        self.register_addr_spin.setStyleSheet(input_style)
        self.register_addr_spin.setRange(0, 65535)
        self.register_addr_spin.setValue(1000)
        self.register_addr_spin.setToolTip("寄存器地址 (0-65535)")

        # 数据值
        value_label = QLabel("数值:")
        value_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 13px;")
        self.register_value_spin = QSpinBox()
        self.register_value_spin.setStyleSheet(input_style)
        self.register_value_spin.setRange(-32768, 65535)
        self.register_value_spin.setValue(0)
        self.register_value_spin.setToolTip("要写入的数据值")

        # 十六进制显示
        hex_label = QLabel("十六进制:")
        hex_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 13px;")
        self.hex_value_edit = QLineEdit()
        self.hex_value_edit.setStyleSheet("""
            QLineEdit {
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 13px;
                font-family: 'Courier New';
                background-color: white;
                min-height: 20px;
            }
            QLineEdit:focus {
                border-color: #3498db;
                background-color: #f8fcff;
            }
        """)
        self.hex_value_edit.setPlaceholderText("0x0000")
        self.hex_value_edit.setMaximumWidth(100)

        # 连接同步更新
        self.register_value_spin.valueChanged.connect(self.update_hex_from_dec)
        self.hex_value_edit.textChanged.connect(self.update_dec_from_hex)

        # 布局
        test_input_layout.addWidget(addr_label)
        test_input_layout.addWidget(self.register_addr_spin)
        test_input_layout.addWidget(value_label)
        test_input_layout.addWidget(self.register_value_spin)
        test_input_layout.addWidget(hex_label)
        test_input_layout.addWidget(self.hex_value_edit)
        test_input_layout.addStretch()

        register_test_layout.addLayout(test_input_layout, 0, 0, 1, 4)

        # 简化的测试按钮
        test_buttons_layout = QHBoxLayout()
        test_buttons_layout.addStretch()

        # 读取按钮
        self.read_single_btn = QPushButton("📖 读取")
        self.read_single_btn.setStyleSheet(button_style.replace("#27ae60", "#3498db").replace("#229954", "#2980b9").replace("#1e8449", "#1f618d"))
        self.read_single_btn.clicked.connect(self.read_single_register)
        self.read_single_btn.setEnabled(False)
        test_buttons_layout.addWidget(self.read_single_btn)

        # 写入按钮
        self.write_single_btn = QPushButton("✏️ 写入")
        self.write_single_btn.setStyleSheet(button_style.replace("#27ae60", "#e67e22").replace("#229954", "#d35400").replace("#1e8449", "#ba4a00"))
        self.write_single_btn.clicked.connect(self.write_single_register)
        self.write_single_btn.setEnabled(False)
        test_buttons_layout.addWidget(self.write_single_btn)

        test_buttons_layout.addStretch()
        register_test_layout.addLayout(test_buttons_layout, 1, 0, 1, 4)

        # 简化的结果显示
        self.register_result_text = QTextEdit()
        self.register_result_text.setStyleSheet("""
            QTextEdit {
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                padding: 10px;
                font-family: 'Courier New';
                font-size: 12px;
                background-color: #f9f9f9;
            }
        """)
        self.register_result_text.setMaximumHeight(60)
        self.register_result_text.setPlaceholderText("测试结果将显示在这里...")
        register_test_layout.addWidget(self.register_result_text, 2, 0, 1, 4)

        # 为了兼容性，创建隐藏的批量读取相关控件
        self.register_count_spin = QSpinBox()
        self.register_count_spin.setRange(1, 10)
        self.register_count_spin.setValue(1)
        self.register_count_spin.hide()

        self.batch_read_btn = QPushButton()
        self.batch_read_btn.clicked.connect(self.batch_read_registers)
        self.batch_read_btn.setEnabled(False)
        self.batch_read_btn.hide()

        advanced_test_group.setLayout(register_test_layout)
        main_layout.addWidget(advanced_test_group)

        # 操作控制区域
        control_group = QGroupBox("🎮 设备控制")
        control_group.setStyleSheet("""
            QGroupBox {
                border: 2px solid #fff2e6;
                border-radius: 8px;
                margin-top: 10px;
                margin-bottom: 10px;
                background-color: #fffaf7;
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 5px 10px;
                background-color: white;
                border-radius: 4px;
            }
        """)
        control_layout = QHBoxLayout()
        control_layout.setSpacing(15)
        control_layout.setContentsMargins(20, 20, 20, 15)
        control_layout.addStretch()

        # 启动按钮
        self.start_btn = QPushButton("▶️ 启动")
        self.start_btn.setStyleSheet(button_style)
        self.start_btn.clicked.connect(self.start_cutting)
        self.start_btn.setEnabled(False)
        control_layout.addWidget(self.start_btn)

        # 停止按钮
        self.stop_btn = QPushButton("⏹️ 停止")
        self.stop_btn.setStyleSheet(button_style.replace("#27ae60", "#e74c3c").replace("#229954", "#c0392b").replace("#1e8449", "#a93226"))
        self.stop_btn.clicked.connect(self.stop_cutting)
        self.stop_btn.setEnabled(False)
        control_layout.addWidget(self.stop_btn)

        # 复位按钮
        self.reset_btn = QPushButton("🔄 复位")
        self.reset_btn.setStyleSheet(button_style.replace("#27ae60", "#f39c12").replace("#229954", "#e67e22").replace("#1e8449", "#d35400"))
        self.reset_btn.clicked.connect(self.reset_machine)
        self.reset_btn.setEnabled(False)
        control_layout.addWidget(self.reset_btn)

        # 更新状态按钮
        self.update_status_btn = QPushButton("🔄 更新状态")
        self.update_status_btn.setStyleSheet(button_style.replace("#27ae60", "#9b59b6").replace("#229954", "#8e44ad").replace("#1e8449", "#6c3483"))
        self.update_status_btn.clicked.connect(self.manual_update_status)
        self.update_status_btn.setEnabled(False)
        control_layout.addWidget(self.update_status_btn)

        control_layout.addStretch()
        control_group.setLayout(control_layout)
        main_layout.addWidget(control_group)

        # 状态显示区域
        status_group = QGroupBox("📊 设备状态")
        status_group.setStyleSheet("""
            QGroupBox {
                border: 2px solid #e8f4fd;
                border-radius: 8px;
                margin-top: 10px;
                margin-bottom: 10px;
                background-color: #f8fcff;
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 5px 10px;
                background-color: white;
                border-radius: 4px;
            }
        """)
        status_layout = QGridLayout()
        status_layout.setSpacing(15)
        status_layout.setContentsMargins(20, 20, 20, 15)

        # 连接状态
        conn_label = QLabel("连接状态:")
        conn_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 13px;")
        self.connection_status_label = QLabel("未连接")
        self.connection_status_label.setStyleSheet("color: #e74c3c; font-weight: bold; font-size: 13px;")
        status_layout.addWidget(conn_label, 0, 0)
        status_layout.addWidget(self.connection_status_label, 0, 1)

        # 当前长度
        length_label = QLabel("当前长度:")
        length_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 13px;")
        self.current_length_label = QLabel("0.00 mm")
        self.current_length_label.setStyleSheet("color: #3498db; font-weight: bold; font-size: 13px;")
        status_layout.addWidget(length_label, 0, 2)
        status_layout.addWidget(self.current_length_label, 0, 3)

        # 已完成数量
        count_label = QLabel("已完成数量:")
        count_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 13px;")
        self.completed_count_label = QLabel("0")
        self.completed_count_label.setStyleSheet("color: #27ae60; font-weight: bold; font-size: 13px;")
        status_layout.addWidget(count_label, 1, 0)
        status_layout.addWidget(self.completed_count_label, 1, 1)

        # 剪切机状态
        machine_label = QLabel("设备状态:")
        machine_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 13px;")
        self.machine_status_label = QLabel("待机")
        self.machine_status_label.setStyleSheet("color: #f39c12; font-weight: bold; font-size: 13px;")
        status_layout.addWidget(machine_label, 1, 2)
        status_layout.addWidget(self.machine_status_label, 1, 3)

        # 红外感应器状态和控制
        infrared_label = QLabel("红外感应器:")
        infrared_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 13px;")
        self.infrared_status_label = QLabel("🔴 无料")
        self.infrared_status_label.setStyleSheet("color: #e74c3c; font-weight: bold; font-size: 13px;")
        status_layout.addWidget(infrared_label, 2, 0)
        status_layout.addWidget(self.infrared_status_label, 2, 1)

        # 红外感应功能开关
        infrared_control_label = QLabel("红外感应功能:")
        infrared_control_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 13px;")
        self.infrared_enable_btn = QPushButton("🟢 已启用")
        self.infrared_enable_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 4px 12px;
                font-size: 12px;
                font-weight: bold;
                min-width: 80px;
                max-height: 24px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
        """)
        self.infrared_enable_btn.setToolTip("点击切换红外感应功能开关")
        self.infrared_enable_btn.clicked.connect(self.toggle_infrared_function)
        status_layout.addWidget(infrared_control_label, 2, 2)
        status_layout.addWidget(self.infrared_enable_btn, 2, 3)

        # 自动停止通知开关
        auto_stop_label = QLabel("自动停止通知:")
        auto_stop_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 13px;")
        self.auto_stop_notification_check = QCheckBox("启用")
        self.auto_stop_notification_check.setChecked(True)
        self.auto_stop_notification_check.setStyleSheet("font-size: 13px; color: #2c3e50;")
        self.auto_stop_notification_check.setToolTip("启用时，红外感应器检测到无料自动停止时会弹出提示对话框")
        # 保存设置到实例变量
        self.auto_stop_notification = True
        self.auto_stop_notification_check.stateChanged.connect(
            lambda state: setattr(self, 'auto_stop_notification', state == Qt.Checked)
        )
        status_layout.addWidget(auto_stop_label, 3, 0)
        status_layout.addWidget(self.auto_stop_notification_check, 3, 1)

        # 简化的进度条
        progress_label = QLabel("任务进度:")
        progress_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 13px;")
        self.main_progress_bar = QProgressBar()
        self.main_progress_bar.setRange(0, 100)
        self.main_progress_bar.setValue(0)
        self.main_progress_bar.setFormat("%v/%m (%p%)")
        self.main_progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                text-align: center;
                font-weight: bold;
                background-color: white;
                min-height: 20px;
            }
            QProgressBar::chunk {
                background-color: #27ae60;
                border-radius: 4px;
            }
        """)
        status_layout.addWidget(progress_label, 4, 0)
        status_layout.addWidget(self.main_progress_bar, 4, 1, 1, 3)

        status_group.setLayout(status_layout)
        main_layout.addWidget(status_group)

        # 注意：端口监控组件现在已经移到固定的顶部区域，不再添加到滚动区域

    def get_current_modbus(self):
        """获取当前活动的Modbus通讯对象"""
        if self.protocol_type == "TCP":
            return self.modbus_tcp
        else:
            return self.modbus

    def refresh_ports(self):
        """刷新可用串口列表"""
        self.port_combo.clear()
        ports = serial.tools.list_ports.comports()
        for port in ports:
            self.port_combo.addItem(port.device)

    def toggle_connection(self):
        """切换连接状态"""
        try:
            current_modbus = self.get_current_modbus()
            if not current_modbus.is_connected():
                # 连接
                if self.protocol_type == "RTU":
                    # RTU协议连接
                    port = self.port_combo.currentText()
                    baudrate = int(self.baudrate_combo.currentText())

                    if not port:
                        QMessageBox.warning(self, "警告", "未配置串口，请切换到\"端口配置\"选项卡进行配置")
                        return

                    # 更新状态栏
                    self.status_label.setText(f"正在连接到 {port}...")
                    QApplication.processEvents()  # 确保UI更新

                    self.modbus.port = port
                    self.modbus.baudrate = baudrate
                    connection_success = self.modbus.connect()
                else:
                    # TCP协议连接
                    host = getattr(self, 'tcp_host', '*************')
                    port = getattr(self, 'tcp_port', 502)

                    # 更新状态栏
                    self.status_label.setText(f"正在连接到 {host}:{port}...")
                    QApplication.processEvents()  # 确保UI更新

                    self.modbus_tcp.host = host
                    self.modbus_tcp.port = port
                    connection_success = self.modbus_tcp.connect()

                if connection_success:
                    self.connection_status_label.setText("已连接")
                    self.connection_status_label.setStyleSheet("color: #27ae60; font-weight: bold;")
                    self.status_connection_label.setText("🟢 已连接")
                    self.status_connection_label.setStyleSheet("color: #28a745; font-weight: bold; padding: 4px 8px;")
                    self.connect_btn.setText("断开")
                    self.apply_params_btn.setEnabled(True)
                    self.send_pulse_btn.setEnabled(True)  # 启用脉冲数据发送按钮
                    self.apply_all_params_btn.setEnabled(True)
                    self.read_params_btn.setEnabled(True)
                    self.read_all_params_btn.setEnabled(True)
                    self.read_single_btn.setEnabled(True)
                    self.write_single_btn.setEnabled(True)
                    self.batch_read_btn.setEnabled(True)
                    self.start_btn.setEnabled(True)
                    self.stop_btn.setEnabled(True)
                    self.reset_btn.setEnabled(True)
                    self.update_status_btn.setEnabled(True)

                    # 启用任务计划界面的控制按钮
                    self.task_start_btn.setEnabled(True)
                    self.task_stop_btn.setEnabled(True)
                    self.task_reset_btn.setEnabled(True)

                    # 保存连接参数用于自动重连
                    if self.protocol_type == "RTU":
                        self.last_connection_params = {
                            'protocol': 'RTU',
                            'port': port,
                            'baudrate': baudrate
                        }
                    else:
                        self.last_connection_params = {
                            'protocol': 'TCP',
                            'host': host,
                            'port': port
                        }

                    # 重置重连计数器
                    self.reconnect_attempts = 0
                    self.was_connected_before = True

                    # 停止重连定时器（如果正在运行）
                    if self.reconnect_timer.isActive():
                        self.reconnect_timer.stop()

                    # 如果启用了自动更新，则启动状态更新定时器
                    if self.auto_update_check.isChecked():
                        self.status_timer.start(3000)  # 每3秒更新一次，降低更新频率
                        self.add_monitor_message("已启用自动状态更新")
                    else:
                        self.add_monitor_message("自动状态更新已禁用，可在连接区域勾选'自动更新状态'启用")

                    # 添加连接成功消息到监控
                    if self.protocol_type == "RTU":
                        self.add_monitor_message(f"成功连接到串口 {port}, 波特率 {baudrate}")
                        self.status_label.setText(f"已连接到 {port}")
                    else:
                        self.add_monitor_message(f"成功连接到TCP服务器 {host}:{port}")
                        self.status_label.setText(f"已连接到 {host}:{port}")
                else:
                    if self.protocol_type == "RTU":
                        QMessageBox.critical(self, "错误", f"无法连接到串口 {port}")
                    else:
                        QMessageBox.critical(self, "错误", f"无法连接到TCP服务器 {host}:{port}")
                    self.status_label.setText("连接失败")
            else:
                # 断开连接
                self.status_label.setText("正在断开连接...")
                QApplication.processEvents()  # 确保UI更新

                current_modbus.disconnect()
                self.status_timer.stop()

                # 停止自动重连（手动断开时）
                if self.reconnect_timer.isActive():
                    self.reconnect_timer.stop()
                self.was_connected_before = False  # 手动断开时重置标记
                self.reconnect_attempts = 0

                self.connection_status_label.setText("未连接")
                self.connection_status_label.setStyleSheet("color: red; font-weight: bold;")
                self.status_connection_label.setText("⚫ 未连接")
                self.status_connection_label.setStyleSheet("color: #6c757d; padding: 4px 8px;")
                self.connect_btn.setText("连接")
                self.apply_params_btn.setEnabled(False)
                self.send_pulse_btn.setEnabled(False)  # 禁用脉冲数据发送按钮
                self.apply_all_params_btn.setEnabled(False)
                self.read_params_btn.setEnabled(False)
                self.read_all_params_btn.setEnabled(False)
                self.read_single_btn.setEnabled(False)
                self.write_single_btn.setEnabled(False)
                self.batch_read_btn.setEnabled(False)
                self.start_btn.setEnabled(False)
                self.stop_btn.setEnabled(False)
                self.reset_btn.setEnabled(False)
                self.update_status_btn.setEnabled(False)

                # 重置进度条
                self.main_progress_bar.setValue(0)
                self.main_progress_bar.setFormat("未连接")

                # 禁用任务计划界面的控制按钮
                self.task_start_btn.setEnabled(False)
                self.task_stop_btn.setEnabled(False)
                self.task_reset_btn.setEnabled(False)
                self.status_label.setText("已断开连接")
                self.add_monitor_message("已手动断开连接，停止自动重连")
        except Exception as e:
            self.status_label.setText(f"连接操作错误: {str(e)}")
            QMessageBox.critical(self, "错误", f"连接操作发生错误: {str(e)}")
            # 确保断开连接
            try:
                self.get_current_modbus().disconnect()
            except:
                pass
            self.status_timer.stop()
            self.connection_status_label.setText("未连接")
            self.connection_status_label.setStyleSheet("color: red; font-weight: bold;")
            self.connect_btn.setText("连接")
            self.apply_params_btn.setEnabled(False)
            self.send_pulse_btn.setEnabled(False)  # 禁用脉冲数据发送按钮
            self.apply_all_params_btn.setEnabled(False)
            self.read_params_btn.setEnabled(False)
            self.read_all_params_btn.setEnabled(False)
            self.read_single_btn.setEnabled(False)
            self.write_single_btn.setEnabled(False)
            self.batch_read_btn.setEnabled(False)
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(False)
            self.reset_btn.setEnabled(False)
            self.update_status_btn.setEnabled(False)

            # 重置进度条
            self.main_progress_bar.setValue(0)
            self.main_progress_bar.setFormat("错误")

    def apply_parameters(self):
        """应用参数设置到剪切机"""
        current_modbus = self.get_current_modbus()
        if not current_modbus.is_connected():
            return

        slave_addr = self.slave_addr_spin.value()

        # 获取参数值
        length = int(self.length_spin.value() * 100)  # 转换为整数，单位0.01mm
        increment = int(self.increment_spin.value() * 100)  # 转换为整数，单位0.01mm
        speed = self.speed_spin.value()
        error_adjust = int(self.error_adjust_spin.value() * 100)  # 转换为整数，单位0.01mm
        roots = self.roots_spin.value()  # 根数参数
        machines = self.machines_spin.value()  # 台数参数

        # 写入参数到对应寄存器
        success = True
        success &= current_modbus.write_register(slave_addr, self.register_map['length'], length)
        success &= current_modbus.write_register(slave_addr, self.register_map['increment'], increment)
        success &= current_modbus.write_register(slave_addr, self.register_map['speed'], speed)
        success &= current_modbus.write_register(slave_addr, self.register_map['error_adjust'], error_adjust)
        success &= current_modbus.write_register(slave_addr, self.register_map['roots'], roots)
        success &= current_modbus.write_register(slave_addr, self.register_map['machines'], machines)

        if success:
            QMessageBox.information(self, "成功", "基本参数已成功应用到剪切机")
        else:
            QMessageBox.warning(self, "警告", "部分或全部基本参数应用失败")

    def apply_all_parameters(self):
        """应用所有参数设置到剪切机（包括控制和状态寄存器）"""
        current_modbus = self.get_current_modbus()
        if not current_modbus.is_connected():
            return

        slave_addr = self.slave_addr_spin.value()

        # 添加应用消息到监控
        self.add_monitor_message("开始应用所有参数到剪切机...")
        self.status_label.setText("正在应用所有参数...")
        QApplication.processEvents()  # 确保UI更新

        # 获取所有参数值
        length = int(self.length_spin.value() * 100)  # 转换为整数，单位0.01mm
        increment = int(self.increment_spin.value() * 100)  # 转换为整数，单位0.01mm
        speed = self.speed_spin.value()
        error_adjust = int(self.error_adjust_spin.value() * 100)  # 转换为整数，单位0.01mm
        roots = self.roots_spin.value()
        machines = self.machines_spin.value()

        # 控制已改为位地址，不再使用寄存器

        # 状态寄存器测试值
        current_length_test = int(self.current_length_test_spin.value() * 100)
        completed_count_test = self.completed_count_test_spin.value()
        machine_status_test = self.machine_status_test_spin.value()

        # 写入所有参数到对应寄存器
        success = True
        write_results = {}

        try:
            # 写入基本参数寄存器
            if current_modbus.write_register(slave_addr, self.register_map['length'], length):
                write_results['剪切长度'] = f"{self.length_spin.value():.2f} mm"
                self.add_monitor_message(f"写入剪切长度: {self.length_spin.value():.2f} mm")
            else:
                success = False
                self.add_monitor_message("写入剪切长度失败")

            if current_modbus.write_register(slave_addr, self.register_map['increment'], increment):
                write_results['增移量'] = f"{self.increment_spin.value():.2f} mm"
                self.add_monitor_message(f"写入增移量: {self.increment_spin.value():.2f} mm")
            else:
                success = False
                self.add_monitor_message("写入增移量失败")

            if current_modbus.write_register(slave_addr, self.register_map['speed'], speed):
                write_results['伺服速度'] = f"{speed}%"
                self.add_monitor_message(f"写入伺服速度: {speed}%")
            else:
                success = False
                self.add_monitor_message("写入伺服速度失败")

            if current_modbus.write_register(slave_addr, self.register_map['error_adjust'], error_adjust):
                write_results['误差调整'] = f"{self.error_adjust_spin.value():.2f} mm"
                self.add_monitor_message(f"写入误差调整: {self.error_adjust_spin.value():.2f} mm")
            else:
                success = False
                self.add_monitor_message("写入误差调整失败")

            if current_modbus.write_register(slave_addr, self.register_map['roots'], roots):
                write_results['根数'] = f"{roots}"
                self.add_monitor_message(f"写入根数: {roots}")
            else:
                success = False
                self.add_monitor_message("写入根数失败")

            if current_modbus.write_register(slave_addr, self.register_map['machines'], machines):
                write_results['台数'] = f"{machines}"
                self.add_monitor_message(f"写入台数: {machines}")
            else:
                success = False
                self.add_monitor_message("写入台数失败")

            # 控制已改为位地址，不再使用寄存器
            # 启动使用M16，停止使用M17，复位使用M11

            # 写入状态寄存器测试值
            if current_modbus.write_register(slave_addr, self.register_map['current_length'], current_length_test):
                write_results['当前长度测试值'] = f"{self.current_length_test_spin.value():.2f} mm"
                self.add_monitor_message(f"写入当前长度测试值: {self.current_length_test_spin.value():.2f} mm")
            else:
                success = False
                self.add_monitor_message("写入当前长度测试值失败")

            if current_modbus.write_register(slave_addr, self.register_map['completed_count'], completed_count_test):
                write_results['已完成数量测试值'] = f"{completed_count_test}"
                self.add_monitor_message(f"写入已完成数量测试值: {completed_count_test}")
            else:
                success = False
                self.add_monitor_message("写入已完成数量测试值失败")

            if current_modbus.write_register(slave_addr, self.register_map['machine_status'], machine_status_test):
                status_text = {0: '待机', 1: '运行', 2: '停止', 3: '错误', 4: '复位'}.get(machine_status_test, f'状态{machine_status_test}')
                write_results['机器状态测试值'] = f"{machine_status_test} ({status_text})"
                self.add_monitor_message(f"写入机器状态测试值: {machine_status_test} ({status_text})")
            else:
                success = False
                self.add_monitor_message("写入机器状态测试值失败")

        except Exception as e:
            success = False
            self.add_monitor_message(f"写入参数时发生错误: {str(e)}")
            print(f"写入参数错误: {e}")

        # 显示写入结果
        if success:
            # 构建结果消息
            result_message = "成功写入所有参数到剪切机:\n\n"
            for param_name, param_value in write_results.items():
                result_message += f"{param_name}: {param_value}\n"

            QMessageBox.information(self, "写入成功", result_message)
            self.add_monitor_message("所有参数写入完成")
            self.status_label.setText("所有参数写入完成")
        else:
            QMessageBox.warning(self, "写入失败", "部分或全部参数写入失败，请检查连接和寄存器配置")
            self.add_monitor_message("参数写入失败")
            self.status_label.setText("参数写入失败")

    def read_parameters(self):
        """读取剪切机当前的所有参数设置"""
        current_modbus = self.get_current_modbus()
        if not current_modbus.is_connected():
            return

        slave_addr = self.slave_addr_spin.value()

        # 添加读取消息到监控
        self.add_monitor_message("开始读取剪切机参数...")
        self.status_label.setText("正在读取参数...")
        QApplication.processEvents()  # 确保UI更新

        # 读取所有参数寄存器
        read_success = True
        read_results = {}

        try:
            # 读取剪切长度
            length_data = current_modbus.read_register(slave_addr, self.register_map['length'])
            if length_data and len(length_data) > 0:
                length_value = length_data[0] / 100.0  # 转换为mm
                self.length_spin.setValue(length_value)
                read_results['剪切长度'] = f"{length_value:.2f} mm"
                self.add_monitor_message(f"读取剪切长度: {length_value:.2f} mm")
            else:
                read_success = False
                self.add_monitor_message("读取剪切长度失败")

            # 读取增移量
            increment_data = current_modbus.read_register(slave_addr, self.register_map['increment'])
            if increment_data and len(increment_data) > 0:
                increment_value = increment_data[0] / 100.0  # 转换为mm
                self.increment_spin.setValue(increment_value)
                read_results['增移量'] = f"{increment_value:.2f} mm"
                self.add_monitor_message(f"读取增移量: {increment_value:.2f} mm")
            else:
                read_success = False
                self.add_monitor_message("读取增移量失败")

            # 读取伺服速度
            speed_data = current_modbus.read_register(slave_addr, self.register_map['speed'])
            if speed_data and len(speed_data) > 0:
                speed_value = speed_data[0]
                self.speed_spin.setValue(speed_value)
                read_results['伺服速度'] = f"{speed_value}%"
                self.add_monitor_message(f"读取伺服速度: {speed_value}%")
            else:
                read_success = False
                self.add_monitor_message("读取伺服速度失败")

            # 读取误差调整
            error_adjust_data = current_modbus.read_register(slave_addr, self.register_map['error_adjust'])
            if error_adjust_data and len(error_adjust_data) > 0:
                error_adjust_value = error_adjust_data[0] / 100.0  # 转换为mm
                self.error_adjust_spin.setValue(error_adjust_value)
                read_results['误差调整'] = f"{error_adjust_value:.2f} mm"
                self.add_monitor_message(f"读取误差调整: {error_adjust_value:.2f} mm")
            else:
                read_success = False
                self.add_monitor_message("读取误差调整失败")

            # 读取根数
            roots_data = current_modbus.read_register(slave_addr, self.register_map['roots'])
            if roots_data and len(roots_data) > 0:
                roots_value = roots_data[0]
                self.roots_spin.setValue(roots_value)
                read_results['根数'] = f"{roots_value}"
                self.add_monitor_message(f"读取根数: {roots_value}")
            else:
                read_success = False
                self.add_monitor_message("读取根数失败")

            # 读取台数
            machines_data = current_modbus.read_register(slave_addr, self.register_map['machines'])
            if machines_data and len(machines_data) > 0:
                machines_value = machines_data[0]
                self.machines_spin.setValue(machines_value)
                read_results['台数'] = f"{machines_value}"
                self.add_monitor_message(f"读取台数: {machines_value}")
            else:
                read_success = False
                self.add_monitor_message("读取台数失败")

        except Exception as e:
            read_success = False
            self.add_monitor_message(f"读取参数时发生错误: {str(e)}")
            print(f"读取参数错误: {e}")

        # 显示读取结果
        if read_success:
            # 构建结果消息
            result_message = "成功读取剪切机参数:\n\n"
            for param_name, param_value in read_results.items():
                result_message += f"{param_name}: {param_value}\n"

            QMessageBox.information(self, "读取成功", result_message)
            self.add_monitor_message("参数读取完成")
            self.status_label.setText("参数读取完成")
        else:
            QMessageBox.warning(self, "读取失败", "部分或全部参数读取失败，请检查连接和寄存器配置")
            self.add_monitor_message("参数读取失败")
            self.status_label.setText("参数读取失败")

    def read_all_parameters(self):
        """读取剪切机所有寄存器的当前值（包括控制和状态寄存器）"""
        current_modbus = self.get_current_modbus()
        if not current_modbus.is_connected():
            return

        slave_addr = self.slave_addr_spin.value()

        # 添加读取消息到监控
        self.add_monitor_message("开始读取所有寄存器数据...")
        self.status_label.setText("正在读取所有寄存器...")
        QApplication.processEvents()  # 确保UI更新

        # 读取所有寄存器
        read_success = True
        read_results = {}

        try:
            # 读取基本参数寄存器
            length_data = current_modbus.read_register(slave_addr, self.register_map['length'])
            if length_data and len(length_data) > 0:
                length_value = length_data[0] / 100.0  # 转换为mm
                self.length_spin.setValue(length_value)
                read_results['剪切长度'] = f"{length_value:.2f} mm"
                self.add_monitor_message(f"读取剪切长度: {length_value:.2f} mm")
            else:
                read_success = False
                self.add_monitor_message("读取剪切长度失败")

            increment_data = current_modbus.read_register(slave_addr, self.register_map['increment'])
            if increment_data and len(increment_data) > 0:
                increment_value = increment_data[0] / 100.0  # 转换为mm
                self.increment_spin.setValue(increment_value)
                read_results['增移量'] = f"{increment_value:.2f} mm"
                self.add_monitor_message(f"读取增移量: {increment_value:.2f} mm")
            else:
                read_success = False
                self.add_monitor_message("读取增移量失败")

            speed_data = current_modbus.read_register(slave_addr, self.register_map['speed'])
            if speed_data and len(speed_data) > 0:
                speed_value = speed_data[0]
                self.speed_spin.setValue(speed_value)
                read_results['伺服速度'] = f"{speed_value}%"
                self.add_monitor_message(f"读取伺服速度: {speed_value}%")
            else:
                read_success = False
                self.add_monitor_message("读取伺服速度失败")

            error_adjust_data = current_modbus.read_register(slave_addr, self.register_map['error_adjust'])
            if error_adjust_data and len(error_adjust_data) > 0:
                error_adjust_value = error_adjust_data[0] / 100.0  # 转换为mm
                self.error_adjust_spin.setValue(error_adjust_value)
                read_results['误差调整'] = f"{error_adjust_value:.2f} mm"
                self.add_monitor_message(f"读取误差调整: {error_adjust_value:.2f} mm")
            else:
                read_success = False
                self.add_monitor_message("读取误差调整失败")

            roots_data = current_modbus.read_register(slave_addr, self.register_map['roots'])
            if roots_data and len(roots_data) > 0:
                roots_value = roots_data[0]
                self.roots_spin.setValue(roots_value)
                read_results['根数'] = f"{roots_value}"
                self.add_monitor_message(f"读取根数: {roots_value}")
            else:
                read_success = False
                self.add_monitor_message("读取根数失败")

            machines_data = current_modbus.read_register(slave_addr, self.register_map['machines'])
            if machines_data and len(machines_data) > 0:
                machines_value = machines_data[0]
                self.machines_spin.setValue(machines_value)
                read_results['台数'] = f"{machines_value}"
                self.add_monitor_message(f"读取台数: {machines_value}")
            else:
                read_success = False
                self.add_monitor_message("读取台数失败")

            # 读取控制位地址 (启动/停止改为使用位地址M16/M17)
            # 注意：启动停止现在使用位地址M16/M17，不再使用寄存器
            try:
                start_bit_data = current_modbus.read_coils(slave_addr, self.bit_map['start'], 1)
                stop_bit_data = current_modbus.read_coils(slave_addr, self.bit_map['stop'], 1)
                if start_bit_data and stop_bit_data:
                    start_status = start_bit_data[0] if start_bit_data else False
                    stop_status = stop_bit_data[0] if stop_bit_data else False
                    read_results['启动位M16'] = f"{'ON' if start_status else 'OFF'}"
                    read_results['停止位M17'] = f"{'ON' if stop_status else 'OFF'}"
                    self.add_monitor_message(f"读取启动位M16: {'ON' if start_status else 'OFF'}")
                    self.add_monitor_message(f"读取停止位M17: {'ON' if stop_status else 'OFF'}")
                else:
                    self.add_monitor_message("读取启动/停止位地址失败")
            except Exception as e:
                self.add_monitor_message(f"读取启动/停止位地址异常: {str(e)}")

            # 复位控制已改为位地址M11，不再使用寄存器

            # 读取状态寄存器
            current_length_data = current_modbus.read_register(slave_addr, self.register_map['current_length'])
            if current_length_data and len(current_length_data) > 0:
                current_length_value = current_length_data[0] / 100.0  # 转换为mm
                self.current_length_test_spin.setValue(current_length_value)
                read_results['当前长度'] = f"{current_length_value:.2f} mm"
                self.add_monitor_message(f"读取当前长度: {current_length_value:.2f} mm")
            else:
                read_success = False
                self.add_monitor_message("读取当前长度失败")

            completed_count_data = current_modbus.read_register(slave_addr, self.register_map['completed_count'])
            if completed_count_data and len(completed_count_data) > 0:
                completed_count_value = completed_count_data[0]
                self.completed_count_test_spin.setValue(completed_count_value)
                read_results['已完成数量'] = f"{completed_count_value}"
                self.add_monitor_message(f"读取已完成数量: {completed_count_value}")
            else:
                read_success = False
                self.add_monitor_message("读取已完成数量失败")

            machine_status_data = current_modbus.read_register(slave_addr, self.register_map['machine_status'])
            if machine_status_data and len(machine_status_data) > 0:
                machine_status_value = machine_status_data[0]
                self.machine_status_test_spin.setValue(machine_status_value)
                status_text = {0: '待机', 1: '运行', 2: '停止', 3: '错误', 4: '复位'}.get(machine_status_value, f'状态{machine_status_value}')
                read_results['机器状态'] = f"{machine_status_value} ({status_text})"
                self.add_monitor_message(f"读取机器状态: {machine_status_value} ({status_text})")
            else:
                read_success = False
                self.add_monitor_message("读取机器状态失败")

        except Exception as e:
            read_success = False
            self.add_monitor_message(f"读取寄存器时发生错误: {str(e)}")
            print(f"读取寄存器错误: {e}")

        # 显示读取结果
        if read_success:
            # 构建结果消息
            result_message = "成功读取所有寄存器数据:\n\n"
            for param_name, param_value in read_results.items():
                result_message += f"{param_name}: {param_value}\n"

            QMessageBox.information(self, "读取成功", result_message)
            self.add_monitor_message("所有寄存器读取完成")
            self.status_label.setText("所有寄存器读取完成")
        else:
            QMessageBox.warning(self, "读取失败", "部分或全部寄存器读取失败，请检查连接和寄存器配置")
            self.add_monitor_message("寄存器读取失败")
            self.status_label.setText("寄存器读取失败")

    def update_hex_from_dec(self):
        """从十进制值更新十六进制显示"""
        try:
            dec_value = self.register_value_spin.value()
            if dec_value < 0:
                # 处理负数，转换为16位补码
                hex_value = format(dec_value & 0xFFFF, '04X')
            else:
                hex_value = format(dec_value, '04X')
            self.hex_value_edit.blockSignals(True)
            self.hex_value_edit.setText(f"0x{hex_value}")
            self.hex_value_edit.blockSignals(False)
        except:
            pass

    def update_dec_from_hex(self):
        """从十六进制值更新十进制显示"""
        try:
            hex_text = self.hex_value_edit.text().strip()
            if hex_text.startswith('0x') or hex_text.startswith('0X'):
                hex_text = hex_text[2:]
            if hex_text:
                dec_value = int(hex_text, 16)
                # 处理16位有符号数
                if dec_value > 32767:
                    dec_value = dec_value - 65536
                self.register_value_spin.blockSignals(True)
                self.register_value_spin.setValue(dec_value)
                self.register_value_spin.blockSignals(False)
        except:
            pass

    def read_single_register(self):
        """读取单个寄存器"""
        if not self.modbus.is_connected():
            return

        slave_addr = self.slave_addr_spin.value()
        register_addr = self.register_addr_spin.value()

        self.add_monitor_message(f"读取寄存器 {register_addr}...")
        self.status_label.setText(f"正在读取寄存器 {register_addr}...")
        QApplication.processEvents()

        try:
            data = self.modbus.read_register(slave_addr, register_addr, 1)
            if data and len(data) > 0:
                value = data[0]
                # 更新界面显示
                self.register_value_spin.setValue(value if value <= 32767 else value - 65536)

                # 显示详细结果
                result_text = f"[{time.strftime('%H:%M:%S')}] 读取成功\n"
                result_text += f"寄存器地址: {register_addr}\n"
                result_text += f"十进制值: {value if value <= 32767 else value - 65536}\n"
                result_text += f"无符号值: {value}\n"
                result_text += f"十六进制: 0x{value:04X}\n"
                result_text += f"二进制: {value:016b}\n"
                result_text += "-" * 40 + "\n"

                self.register_result_text.append(result_text)
                self.add_monitor_message(f"读取寄存器 {register_addr} 成功: {value}")
                self.status_label.setText("读取完成")
            else:
                error_text = f"[{time.strftime('%H:%M:%S')}] 读取失败\n"
                error_text += f"寄存器地址: {register_addr}\n"
                error_text += "错误: 无响应或数据格式错误\n"
                error_text += "-" * 40 + "\n"

                self.register_result_text.append(error_text)
                self.add_monitor_message(f"读取寄存器 {register_addr} 失败")
                self.status_label.setText("读取失败")
        except Exception as e:
            error_text = f"[{time.strftime('%H:%M:%S')}] 读取异常\n"
            error_text += f"寄存器地址: {register_addr}\n"
            error_text += f"错误信息: {str(e)}\n"
            error_text += "-" * 40 + "\n"

            self.register_result_text.append(error_text)
            self.add_monitor_message(f"读取寄存器 {register_addr} 异常: {str(e)}")
            self.status_label.setText("读取异常")

    def write_single_register(self):
        """写入单个寄存器"""
        if not self.modbus.is_connected():
            return

        slave_addr = self.slave_addr_spin.value()
        register_addr = self.register_addr_spin.value()
        register_value = self.register_value_spin.value()

        # 转换为无符号16位值
        if register_value < 0:
            write_value = register_value + 65536
        else:
            write_value = register_value

        self.add_monitor_message(f"写入寄存器 {register_addr}: {register_value}...")
        self.status_label.setText(f"正在写入寄存器 {register_addr}...")
        QApplication.processEvents()

        try:
            success = self.modbus.write_register(slave_addr, register_addr, write_value)
            if success:
                result_text = f"[{time.strftime('%H:%M:%S')}] 写入成功\n"
                result_text += f"寄存器地址: {register_addr}\n"
                result_text += f"写入值(十进制): {register_value}\n"
                result_text += f"写入值(无符号): {write_value}\n"
                result_text += f"写入值(十六进制): 0x{write_value:04X}\n"
                result_text += f"写入值(二进制): {write_value:016b}\n"
                result_text += "-" * 40 + "\n"

                self.register_result_text.append(result_text)
                self.add_monitor_message(f"写入寄存器 {register_addr} 成功: {register_value}")
                self.status_label.setText("写入完成")
            else:
                error_text = f"[{time.strftime('%H:%M:%S')}] 写入失败\n"
                error_text += f"寄存器地址: {register_addr}\n"
                error_text += f"尝试写入值: {register_value}\n"
                error_text += "错误: 写入操作失败\n"
                error_text += "-" * 40 + "\n"

                self.register_result_text.append(error_text)
                self.add_monitor_message(f"写入寄存器 {register_addr} 失败")
                self.status_label.setText("写入失败")
        except Exception as e:
            error_text = f"[{time.strftime('%H:%M:%S')}] 写入异常\n"
            error_text += f"寄存器地址: {register_addr}\n"
            error_text += f"尝试写入值: {register_value}\n"
            error_text += f"错误信息: {str(e)}\n"
            error_text += "-" * 40 + "\n"

            self.register_result_text.append(error_text)
            self.add_monitor_message(f"写入寄存器 {register_addr} 异常: {str(e)}")
            self.status_label.setText("写入异常")

    def batch_read_registers(self):
        """批量读取连续寄存器"""
        if not self.modbus.is_connected():
            return

        slave_addr = self.slave_addr_spin.value()
        start_addr = self.register_addr_spin.value()
        count = self.register_count_spin.value()

        self.add_monitor_message(f"批量读取寄存器 {start_addr}-{start_addr + count - 1}...")
        self.status_label.setText(f"正在批量读取 {count} 个寄存器...")
        QApplication.processEvents()

        try:
            data = self.modbus.read_register(slave_addr, start_addr, count)
            if data and len(data) > 0:
                result_text = f"[{time.strftime('%H:%M:%S')}] 批量读取成功\n"
                result_text += f"起始地址: {start_addr}, 数量: {count}\n"
                result_text += "-" * 40 + "\n"

                for i, value in enumerate(data):
                    addr = start_addr + i
                    signed_value = value if value <= 32767 else value - 65536
                    result_text += f"寄存器 {addr:5d}: {signed_value:6d} (0x{value:04X}) [{value:016b}]\n"

                result_text += "-" * 40 + "\n"

                self.register_result_text.append(result_text)
                self.add_monitor_message(f"批量读取 {count} 个寄存器成功")
                self.status_label.setText("批量读取完成")
            else:
                error_text = f"[{time.strftime('%H:%M:%S')}] 批量读取失败\n"
                error_text += f"起始地址: {start_addr}, 数量: {count}\n"
                error_text += "错误: 无响应或数据格式错误\n"
                error_text += "-" * 40 + "\n"

                self.register_result_text.append(error_text)
                self.add_monitor_message(f"批量读取寄存器失败")
                self.status_label.setText("批量读取失败")
        except Exception as e:
            error_text = f"[{time.strftime('%H:%M:%S')}] 批量读取异常\n"
            error_text += f"起始地址: {start_addr}, 数量: {count}\n"
            error_text += f"错误信息: {str(e)}\n"
            error_text += "-" * 40 + "\n"

            self.register_result_text.append(error_text)
            self.add_monitor_message(f"批量读取寄存器异常: {str(e)}")
            self.status_label.setText("批量读取异常")

    def start_cutting(self):
        """启动剪切机"""
        current_modbus = self.get_current_modbus()
        if not current_modbus.is_connected():
            return

        slave_addr = self.slave_addr_spin.value()

        # 检查红外感应功能是否启用
        if self.infrared_enabled:
            # 检查红外感应器状态 (X7)
            infrared_status = self.check_infrared_sensor()
            if infrared_status is None:
                QMessageBox.warning(self, "警告", "无法读取红外感应器状态，请检查连接")
                return
            elif not infrared_status:
                QMessageBox.warning(self, "警告", "红外感应器未检测到物料，无法启动机器\n请确保进料口有物料后再启动")
                self.add_monitor_message("启动失败：红外感应器未检测到物料 (X7=OFF)")
                return

            # 红外感应器检测到物料，可以启动
            self.add_monitor_message("红外感应器检测到物料 (X7=ON)，允许启动")
        else:
            # 红外感应功能已禁用，跳过检查
            self.add_monitor_message("红外感应功能已禁用，跳过物料检查")

        # 写入启动命令到位地址M16
        if current_modbus.write_coil(slave_addr, self.bit_map['start'], True):  # True表示启动
            self.machine_status_label.setText("运行中")
            self.add_monitor_message("启动命令已发送到M16")
        else:
            QMessageBox.warning(self, "警告", "启动命令发送失败")

    def stop_cutting(self):
        """停止剪切机"""
        current_modbus = self.get_current_modbus()
        if not current_modbus.is_connected():
            return

        slave_addr = self.slave_addr_spin.value()
        # 写入停止命令到位地址M17
        if current_modbus.write_coil(slave_addr, self.bit_map['stop'], True):  # True表示停止
            self.machine_status_label.setText("已停止")
            self.add_monitor_message("停止命令已发送到M17")
        else:
            QMessageBox.warning(self, "警告", "停止命令发送失败")

    def reset_machine(self):
        """复位剪切机"""
        current_modbus = self.get_current_modbus()
        if not current_modbus.is_connected():
            return

        slave_addr = self.slave_addr_spin.value()
        # 写入复位命令到位地址M11
        if current_modbus.write_coil(slave_addr, self.bit_map['host_reset_button'], True):  # True表示复位
            self.machine_status_label.setText("复位中")
            self.completed_count_label.setText("0")
            self.add_monitor_message("复位命令已发送到M11")
        else:
            QMessageBox.warning(self, "警告", "复位命令发送失败")

    def update_status(self):
        """更新状态信息 - 增强异常处理版本"""
        try:
            # 首先检查连接状态，如果未连接则立即停止定时器并返回
            current_modbus = self.get_current_modbus()
            if not current_modbus.is_connected():
                # 记录断开时间
                current_time = time.time()
                if self.last_disconnect_time == 0:
                    self.last_disconnect_time = current_time
                    self.add_monitor_message("检测到连接断开")

                # 如果未连接，更新UI状态但不尝试读取寄存器
                try:
                    self.connection_status_label.setText("未连接")
                    self.connection_status_label.setStyleSheet("color: red; font-weight: bold;")
                    self.status_connection_label.setText("⚫ 未连接")
                    self.status_connection_label.setStyleSheet("color: #6c757d; padding: 4px 8px;")
                    self.machine_status_label.setText("未连接")
                    self.current_length_label.setText("--")
                    self.completed_count_label.setText("--")
                    # 重置进度条
                    self.main_progress_bar.setValue(0)
                    self.main_progress_bar.setFormat("未连接")
                except Exception as ui_error:
                    print(f"更新UI状态时发生错误: {ui_error}")

                # 停止状态更新定时器，防止持续尝试读取
                try:
                    self.status_timer.stop()
                    if current_time - self.last_disconnect_time < 5:  # 5秒内只记录一次
                        self.add_monitor_message("检测到连接已断开，停止状态更新")
                except Exception as timer_error:
                    print(f"停止定时器时发生错误: {timer_error}")

                # 检查是否需要自动重连（增加防抖机制）
                try:
                    # 断开后等待至少10秒再尝试重连，避免频繁重连
                    time_since_disconnect = current_time - self.last_disconnect_time

                    if (self.auto_reconnect_enabled and
                        self.was_connected_before and
                        self.last_connection_params and
                        self.reconnect_attempts < self.max_reconnect_attempts and
                        not self.reconnect_timer.isActive() and
                        time_since_disconnect >= 10):  # 至少等待10秒

                        self.reconnect_attempts += 1
                        self.add_monitor_message(f"准备自动重连 (第{self.reconnect_attempts}/{self.max_reconnect_attempts}次)...")
                        self.status_label.setText(f"自动重连中 ({self.reconnect_attempts}/{self.max_reconnect_attempts})")

                        # 启动重连定时器
                        self.reconnect_timer.start(self.reconnect_interval * 1000)  # 转换为毫秒
                    elif time_since_disconnect < 10:
                        # 显示等待重连的倒计时
                        wait_time = int(10 - time_since_disconnect)
                        self.status_label.setText(f"等待重连... ({wait_time}秒)")

                except Exception as reconnect_error:
                    print(f"处理自动重连时发生错误: {reconnect_error}")

                return
            else:
                # 连接正常，重置断开时间
                self.last_disconnect_time = 0

            slave_addr = self.slave_addr_spin.value()

            # 读取当前长度 - 支持大长度值，增强异常处理
            try:
                # 首先尝试使用read_large_value读取大数值
                large_length_value = current_modbus.read_large_value(slave_addr, self.register_map['current_length'])
                if large_length_value is not None:
                    # 转换为实际长度（单位：mm）
                    current_length = large_length_value / 100.0
                    self.current_length_label.setText(f"{current_length:.2f} mm")
                    if hasattr(self, 'logger'):
                        self.logger.info(f"读取大长度值: {large_length_value} ({current_length:.2f}mm)")
                else:
                    # 如果读取大数值失败，回退到常规读取方式
                    length_data = current_modbus.read_register(slave_addr, self.register_map['current_length'])
                    if length_data and len(length_data) > 0:
                        current_length = length_data[0] / 100.0  # 转换为mm
                        self.current_length_label.setText(f"{current_length:.2f} mm")
                    else:
                        self.current_length_label.setText("读取失败")
            except Exception as e:
                if hasattr(self, 'logger'):
                    self.logger.error(f"读取当前长度错误: {e}")
                print(f"读取当前长度错误: {e}")
                try:
                    self.current_length_label.setText("读取错误")
                except:
                    pass

            # 读取已完成数量，增强异常处理
            try:
                count_data = current_modbus.read_register(slave_addr, self.register_map['completed_count'])
                if count_data and len(count_data) > 0:
                    completed_count = count_data[0]
                    self.completed_count_label.setText(str(completed_count))

                    # 更新进度条
                    try:
                        if self.current_task_index >= 0:
                            task = self.task_planner.get_task_by_index(self.current_task_index)
                            if task and task['total_quantity'] > 0:
                                # 更新主界面进度条
                                self.main_progress_bar.setMaximum(task['total_quantity'])
                                self.main_progress_bar.setValue(task['completed_quantity'] + completed_count)
                                self.main_progress_bar.setFormat(f"{task['completed_quantity'] + completed_count}/{task['total_quantity']} ({int((task['completed_quantity'] + completed_count) / task['total_quantity'] * 100)}%)")
                        else:
                            # 如果没有选择任务，显示当前完成数量
                            self.main_progress_bar.setMaximum(100)
                            self.main_progress_bar.setValue(completed_count)
                            self.main_progress_bar.setFormat(f"{completed_count}")
                    except Exception as progress_error:
                        print(f"更新进度条时发生错误: {progress_error}")
                else:
                    self.completed_count_label.setText("读取失败")
            except Exception as e:
                print(f"读取已完成数量错误: {e}")
                try:
                    self.completed_count_label.setText("读取错误")
                except:
                    pass

            # 读取机器状态，增强异常处理
            machine_is_running = False
            try:
                status_data = current_modbus.read_register(slave_addr, self.register_map['machine_status'])
                if status_data and len(status_data) > 0:
                    status_code = status_data[0]
                    if status_code == 0:
                        self.machine_status_label.setText("待机")
                        self.machine_status_label.setStyleSheet("color: #d35400; font-weight: bold;")
                    elif status_code == 1:
                        self.machine_status_label.setText("运行中")
                        self.machine_status_label.setStyleSheet("color: #27ae60; font-weight: bold;")
                        machine_is_running = True
                    elif status_code == 2:
                        self.machine_status_label.setText("已停止")
                        self.machine_status_label.setStyleSheet("color: #e74c3c; font-weight: bold;")
                    elif status_code == 3:
                        self.machine_status_label.setText("错误")
                        self.machine_status_label.setStyleSheet("color: red; font-weight: bold;")
                    elif status_code == 4:
                        self.machine_status_label.setText("复位中")
                        self.machine_status_label.setStyleSheet("color: #f39c12; font-weight: bold;")
                    else:
                        self.machine_status_label.setText(f"未知状态({status_code})")
                        self.machine_status_label.setStyleSheet("color: #7f8c8d; font-weight: bold;")
                else:
                    self.machine_status_label.setText("读取失败")
            except Exception as e:
                print(f"读取机器状态错误: {e}")
                try:
                    self.machine_status_label.setText("读取错误")
                except:
                    pass

            # 检查红外感应器状态并更新显示，增强异常处理
            try:
                infrared_status = self.check_infrared_sensor()

                # 如果红外感应功能启用且机器正在运行但红外感应器检测不到物料，自动停止机器
                if self.infrared_enabled and machine_is_running and infrared_status is not None and not infrared_status:
                    self.add_monitor_message("⚠️ 红外感应器检测到无料，自动停止机器")
                    # 发送停止命令
                    try:
                        if current_modbus.write_coil(slave_addr, self.bit_map['stop'], True):
                            self.machine_status_label.setText("已停止")
                            self.machine_status_label.setStyleSheet("color: #e74c3c; font-weight: bold;")
                            self.add_monitor_message("自动停止命令已发送到M17")
                            # 可选：显示提示对话框
                            if hasattr(self, 'auto_stop_notification') and self.auto_stop_notification:
                                QMessageBox.warning(self, "自动停止", "红外感应器检测到无料，机器已自动停止\n请补充物料后重新启动")
                        else:
                            self.add_monitor_message("⚠️ 自动停止命令发送失败")
                    except Exception as stop_error:
                        print(f"发送停止命令时发生错误: {stop_error}")
                        self.add_monitor_message(f"⚠️ 发送停止命令时发生错误: {stop_error}")
            except Exception as infrared_error:
                print(f"检查红外感应器状态时发生错误: {infrared_error}")

            # 更新连接状态样式
            try:
                self.connection_status_label.setStyleSheet("color: #27ae60; font-weight: bold;")
            except Exception as style_error:
                print(f"更新连接状态样式时发生错误: {style_error}")

        except Exception as main_error:
            print(f"update_status主函数发生严重错误: {main_error}")
            # 发生严重错误时停止定时器，防止持续崩溃
            try:
                self.status_timer.stop()
                self.add_monitor_message(f"状态更新发生严重错误，已停止自动更新: {main_error}")
                self.status_label.setText("状态更新错误")
            except:
                pass


    def create_port_monitor_ui(self, layout):
        """创建端口监控界面（紧凑版本，适合固定高度）"""
        # 创建紧凑的统计信息区域
        stats_group = QGroupBox("📊 通信统计")
        stats_group.setStyleSheet("""
            QGroupBox {
                border: 2px solid #e8f4fd;
                border-radius: 6px;
                margin-top: 5px;
                margin-bottom: 5px;
                background-color: #f8fcff;
                font-size: 12px;
                font-weight: bold;
                color: #2c3e50;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 2px 8px;
                background-color: white;
                border-radius: 3px;
            }
        """)
        stats_layout = QGridLayout()
        stats_layout.setSpacing(8)
        stats_layout.setContentsMargins(10, 10, 10, 8)

        # 统计标签样式
        label_style = "font-size: 13px; color: #2c3e50; font-weight: bold;"
        value_style = "font-size: 13px; font-weight: bold;"

        # 发送/接收计数
        send_label = QLabel("发送数据包:")
        send_label.setStyleSheet(label_style)
        stats_layout.addWidget(send_label, 0, 0)
        self.tx_count_label = QLabel("0")
        self.tx_count_label.setStyleSheet(value_style + "color: #2ecc71;")
        stats_layout.addWidget(self.tx_count_label, 0, 1)

        recv_label = QLabel("接收数据包:")
        recv_label.setStyleSheet(label_style)
        stats_layout.addWidget(recv_label, 0, 2)
        self.rx_count_label = QLabel("0")
        self.rx_count_label.setStyleSheet(value_style + "color: #3498db;")
        stats_layout.addWidget(self.rx_count_label, 0, 3)

        # 成功/错误计数
        success_label = QLabel("成功次数:")
        success_label.setStyleSheet(label_style)
        stats_layout.addWidget(success_label, 1, 0)
        self.success_count_label = QLabel("0")
        self.success_count_label.setStyleSheet(value_style + "color: #2ecc71;")
        stats_layout.addWidget(self.success_count_label, 1, 1)

        error_label = QLabel("错误次数:")
        error_label.setStyleSheet(label_style)
        stats_layout.addWidget(error_label, 1, 2)
        self.error_count_label = QLabel("0")
        self.error_count_label.setStyleSheet(value_style + "color: #e74c3c;")
        stats_layout.addWidget(self.error_count_label, 1, 3)

        # 成功率
        rate_label = QLabel("成功率:")
        rate_label.setStyleSheet(label_style)
        stats_layout.addWidget(rate_label, 2, 0)
        self.success_rate_label = QLabel("0%")
        self.success_rate_label.setStyleSheet(value_style + "color: #f39c12;")
        stats_layout.addWidget(self.success_rate_label, 2, 1)

        # 清除按钮（紧凑版）
        clear_btn = QPushButton("清除")
        clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border-radius: 3px;
                padding: 3px 8px;
                font-size: 10px;
                font-weight: bold;
                min-width: 40px;
                max-height: 20px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        clear_btn.clicked.connect(self.clear_statistics)
        stats_layout.addWidget(clear_btn, 2, 3)

        stats_group.setLayout(stats_layout)
        layout.addWidget(stats_group)

        # 创建紧凑的通信数据显示区域
        comm_group = QGroupBox("📡 通信数据")
        comm_group.setStyleSheet("""
            QGroupBox {
                border: 2px solid #fff2e6;
                border-radius: 6px;
                margin-top: 5px;
                margin-bottom: 5px;
                background-color: #fffaf7;
                font-size: 12px;
                font-weight: bold;
                color: #2c3e50;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 2px 8px;
                background-color: white;
                border-radius: 3px;
            }
        """)
        comm_layout = QVBoxLayout()
        comm_layout.setSpacing(5)
        comm_layout.setContentsMargins(8, 8, 8, 8)

        # 控制按钮区域
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(8)

        # 复选框样式（调整字体大小）
        checkbox_style = "font-size: 12px; color: #2c3e50;"

        # 自动滚动复选框
        self.auto_scroll_check = QCheckBox("自动滚动")
        self.auto_scroll_check.setChecked(True)
        self.auto_scroll_check.setStyleSheet(checkbox_style)
        self.auto_scroll_check.setToolTip("选中时，新消息会自动滚动到底部")
        buttons_layout.addWidget(self.auto_scroll_check)

        # 过滤选项
        filter_label = QLabel("过滤:")
        filter_label.setStyleSheet("font-size: 12px; color: #2c3e50; font-weight: bold;")
        buttons_layout.addWidget(filter_label)

        self.filter_combo = QComboBox()
        self.filter_combo.addItem("全部")
        self.filter_combo.addItem("发送")
        self.filter_combo.addItem("接收")
        self.filter_combo.setStyleSheet("""
            QComboBox {
                border: 1px solid #e0e0e0;
                border-radius: 3px;
                padding: 4px 8px;
                font-size: 12px;
                background-color: white;
                color: #2c3e50;
                min-width: 60px;
                min-height: 24px;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
                background-color: #f0f0f0;
            }
            QComboBox::drop-down:hover {
                background-color: #3498db;
            }
            QComboBox::down-arrow {
                width: 12px;
                height: 12px;
            }
            QComboBox QAbstractItemView {
                border: 1px solid #e0e0e0;
                background-color: white;
                color: #2c3e50;
                font-size: 12px;
                selection-background-color: #3498db;
                selection-color: white;
                outline: none;
            }
            QComboBox QAbstractItemView::item {
                padding: 6px 8px;
                border: none;
                color: #2c3e50;
                background-color: white;
            }
            QComboBox QAbstractItemView::item:hover {
                background-color: #e8f4fd;
                color: #2c3e50;
            }
            QComboBox QAbstractItemView::item:selected {
                background-color: #3498db;
                color: white;
            }
        """)
        self.filter_combo.setToolTip("选择要显示的消息类型")
        self.filter_combo.currentIndexChanged.connect(self.update_port_monitor)
        buttons_layout.addWidget(self.filter_combo)

        buttons_layout.addStretch()  # 添加弹性空间

        # 导出按钮
        export_btn = QPushButton("导出")
        export_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border-radius: 4px;
                padding: 4px 12px;
                font-size: 12px;
                font-weight: bold;
                min-width: 50px;
                min-height: 24px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        export_btn.setToolTip("将通信日志导出到文件")
        export_btn.clicked.connect(self.export_comm_log)
        buttons_layout.addWidget(export_btn)

        # 清除按钮
        clear_log_btn = QPushButton("清除")
        clear_log_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border-radius: 4px;
                padding: 4px 12px;
                font-size: 12px;
                font-weight: bold;
                min-width: 50px;
                min-height: 24px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        clear_log_btn.setToolTip("清除通信日志")
        clear_log_btn.clicked.connect(self.clear_comm_log)
        buttons_layout.addWidget(clear_log_btn)

        comm_layout.addLayout(buttons_layout)

        # 通信数据文本框（调整字体大小与参数测试界面一致）
        self.comm_text = QTextEdit()
        self.comm_text.setReadOnly(True)
        self.comm_text.setStyleSheet("""
            QTextEdit {
                background-color: #2c3e50;
                color: #ecf0f1;
                border: 1px solid #34495e;
                border-radius: 4px;
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 13px;
                line-height: 1.3;
                padding: 8px;
            }
        """)
        self.comm_text.setLineWrapMode(QTextEdit.NoWrap)  # 禁用自动换行，使数据更易读
        comm_layout.addWidget(self.comm_text)

        comm_group.setLayout(comm_layout)
        layout.addWidget(comm_group)

    def update_port_monitor(self):
        """更新端口监控信息 - 增强异常处理版本"""
        try:
            # 即使未连接也更新统计信息，这样用户可以看到连接状态
            # 更新发送/接收计数
            try:
                self.tx_count_label.setText(str(len(self.modbus.tx_data)))
                self.rx_count_label.setText(str(len(self.modbus.rx_data)))
            except Exception as count_error:
                print(f"更新发送/接收计数时发生错误: {count_error}")

            # 更新成功/错误计数
            try:
                self.success_count_label.setText(str(self.modbus.success_count))
                self.error_count_label.setText(str(self.modbus.error_count))
            except Exception as status_error:
                print(f"更新成功/错误计数时发生错误: {status_error}")

            # 计算成功率
            try:
                total = self.modbus.success_count + self.modbus.error_count
                if total > 0:
                    success_rate = (self.modbus.success_count / total) * 100
                    self.success_rate_label.setText(f"{success_rate:.1f}%")
                else:
                    self.success_rate_label.setText("0%")
            except Exception as rate_error:
                print(f"计算成功率时发生错误: {rate_error}")
                try:
                    self.success_rate_label.setText("计算错误")
                except:
                    pass
        except Exception as main_error:
            print(f"update_port_monitor主函数发生错误: {main_error}")

        # 更新最新的通信数据（增加显示条数）
        if self.modbus.tx_data or self.modbus.rx_data:
            # 保存当前滚动条位置
            scrollbar = self.comm_text.verticalScrollBar()
            current_position = scrollbar.value()

            # 检查是否应该自动滚动（用户选择了自动滚动且当前在底部）
            auto_scroll = self.auto_scroll_check.isChecked() and current_position == scrollbar.maximum()

            # 根据过滤选项选择要显示的数据
            filter_option = self.filter_combo.currentText()
            all_data = []

            # 显示发送数据
            if filter_option == "全部" or filter_option == "发送":
                for item in self.modbus.tx_data[-50:]:  # 减少显示量以适应紧凑界面
                    all_data.append(f"[{item['time']}] TX ({item['type']}): {item['data']}")

            # 显示接收数据
            if filter_option == "全部" or filter_option == "接收":
                for item in self.modbus.rx_data[-50:]:  # 减少显示量以适应紧凑界面
                    all_data.append(f"[{item['time']}] RX ({item['type']}): {item['data']}")

            # 按时间排序
            all_data.sort()  # 假设时间戳格式允许直接排序

            # 更新文本显示
            self.comm_text.setText("\n".join(all_data))

            # 根据用户选择决定是否自动滚动
            if auto_scroll:
                scrollbar.setValue(scrollbar.maximum())
            else:
                # 尝试保持相对位置
                scrollbar.setValue(current_position)

    def export_comm_log(self):
        """导出通信日志到文件"""
        if not self.modbus.tx_data and not self.modbus.rx_data:
            QMessageBox.information(self, "提示", "没有通信数据可导出")
            return

        # 选择保存文件
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出通信日志", "",
            "文本文件 (*.txt);;所有文件 (*)"
        )

        if not file_path:
            return

        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                # 写入发送数据
                f.write("===== 发送数据 =====\n")
                for item in self.modbus.tx_data:
                    f.write(f"[{item['time']}] ({item['type']}): {item['data']}\n")

                # 写入接收数据
                f.write("\n===== 接收数据 =====\n")
                for item in self.modbus.rx_data:
                    f.write(f"[{item['time']}] ({item['type']}): {item['data']}\n")

                # 写入统计信息
                f.write("\n===== 统计信息 =====\n")
                f.write(f"发送数据包: {len(self.modbus.tx_data)}\n")
                f.write(f"接收数据包: {len(self.modbus.rx_data)}\n")
                f.write(f"成功次数: {self.modbus.success_count}\n")
                f.write(f"错误次数: {self.modbus.error_count}\n")

                total = self.modbus.success_count + self.modbus.error_count
                if total > 0:
                    success_rate = (self.modbus.success_count / total) * 100
                    f.write(f"成功率: {success_rate:.1f}%\n")

            QMessageBox.information(self, "成功", f"通信日志已导出到: {file_path}")
            self.status_label.setText(f"通信日志已导出")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出日志失败: {str(e)}")

    def clear_comm_log(self):
        """清除通信日志"""
        msgBox = QMessageBox(QMessageBox.Question, "确认", "确定要清除通信日志吗？\n这将清空所有通信记录，但不会影响统计数据。",
                          QMessageBox.Yes | QMessageBox.No, self)
        msgBox.button(QMessageBox.Yes).setText("是")
        msgBox.button(QMessageBox.No).setText("否")
        msgBox.setDefaultButton(QMessageBox.No)
        reply = msgBox.exec_()

        if reply == QMessageBox.Yes:
            # 清空通信数据但保留统计信息
            self.modbus.tx_data = []
            self.modbus.rx_data = []
            self.comm_text.clear()
            self.add_monitor_message("通信日志已清除")
            self.status_label.setText("通信日志已清除")

    def add_monitor_message(self, message):
        """添加消息到监控窗口"""
        # 保存当前滚动条位置
        scrollbar = self.comm_text.verticalScrollBar()
        current_position = scrollbar.value()

        # 检查是否应该自动滚动（用户选择了自动滚动且当前在底部）
        auto_scroll = self.auto_scroll_check.isChecked() and current_position == scrollbar.maximum()

        # 添加消息
        timestamp = time.strftime('%H:%M:%S')
        self.comm_text.append(f"[{timestamp}] {message}")

        # 根据用户选择决定是否自动滚动
        if auto_scroll:
            scrollbar.setValue(scrollbar.maximum())

    def clear_statistics(self):
        """清除通信统计信息"""
        self.modbus.tx_data = []
        self.modbus.rx_data = []
        self.modbus.error_count = 0
        self.modbus.success_count = 0
        self.tx_count_label.setText("0")
        self.rx_count_label.setText("0")
        self.success_count_label.setText("0")
        self.error_count_label.setText("0")
        self.success_rate_label.setText("0%")
        self.comm_text.clear()
        self.add_monitor_message("已清除统计信息")
        self.status_label.setText("已清除统计信息")

    def toggle_auto_update(self, state):
        """切换自动更新状态"""
        if state == Qt.Checked:
            # 启用自动更新
            current_modbus = self.get_current_modbus()
            if current_modbus.is_connected():
                self.status_timer.start(3000)  # 每3秒更新一次
                self.add_monitor_message("已启用自动状态更新")
                self.status_label.setText("已启用自动状态更新")
        else:
            # 禁用自动更新
            self.status_timer.stop()
            self.add_monitor_message("已禁用自动状态更新")
            self.status_label.setText("已禁用自动状态更新")

    def toggle_auto_reconnect(self, state):
        """切换自动重连状态"""
        if state == Qt.Checked:
            self.auto_reconnect_enabled = True
            self.add_monitor_message("已启用自动重连功能")
            self.status_label.setText("已启用自动重连")
        else:
            self.auto_reconnect_enabled = False
            # 停止正在进行的重连
            if self.reconnect_timer.isActive():
                self.reconnect_timer.stop()
                self.add_monitor_message("已停止自动重连")
            self.add_monitor_message("已禁用自动重连功能")
            self.status_label.setText("已禁用自动重连")

    def attempt_reconnect(self):
        """尝试自动重连"""
        if not self.auto_reconnect_enabled or not self.last_connection_params:
            return

        self.add_monitor_message(f"正在尝试自动重连... (第{self.reconnect_attempts}/{self.max_reconnect_attempts}次)")
        self.status_label.setText(f"重连中... ({self.reconnect_attempts}/{self.max_reconnect_attempts})")

        try:
            # 根据保存的连接参数尝试重连
            if self.last_connection_params['protocol'] == 'RTU':
                # RTU重连
                port = self.last_connection_params['port']
                baudrate = self.last_connection_params['baudrate']

                self.modbus.port = port
                self.modbus.baudrate = baudrate
                connection_success = self.modbus.connect()

                if connection_success:
                    self.add_monitor_message(f"自动重连成功！已连接到串口 {port}")
                    self.status_label.setText(f"重连成功 - {port}")
                    self._on_reconnect_success()
                else:
                    self._on_reconnect_failed()

            else:
                # TCP重连
                host = self.last_connection_params['host']
                port = self.last_connection_params['port']

                self.modbus_tcp.host = host
                self.modbus_tcp.port = port
                connection_success = self.modbus_tcp.connect()

                if connection_success:
                    self.add_monitor_message(f"自动重连成功！已连接到TCP服务器 {host}:{port}")
                    self.status_label.setText(f"重连成功 - {host}:{port}")
                    self._on_reconnect_success()
                else:
                    self._on_reconnect_failed()

        except Exception as e:
            self.add_monitor_message(f"自动重连异常: {str(e)}")
            self._on_reconnect_failed()

    def _on_reconnect_success(self):
        """重连成功后的处理"""
        # 重置重连计数器
        self.reconnect_attempts = 0

        # 更新UI状态
        self.connection_status_label.setText("已连接")
        self.connection_status_label.setStyleSheet("color: #27ae60; font-weight: bold;")
        self.status_connection_label.setText("🟢 已连接")
        self.status_connection_label.setStyleSheet("color: #28a745; font-weight: bold; padding: 4px 8px;")
        self.connect_btn.setText("断开")

        # 启用相关按钮
        self.apply_params_btn.setEnabled(True)
        self.send_pulse_btn.setEnabled(True)  # 启用脉冲数据发送按钮
        self.apply_all_params_btn.setEnabled(True)
        self.read_params_btn.setEnabled(True)
        self.read_all_params_btn.setEnabled(True)
        self.read_single_btn.setEnabled(True)
        self.write_single_btn.setEnabled(True)
        self.batch_read_btn.setEnabled(True)
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(True)
        self.reset_btn.setEnabled(True)
        self.update_status_btn.setEnabled(True)

        # 启用任务计划界面的控制按钮
        self.task_start_btn.setEnabled(True)
        self.task_stop_btn.setEnabled(True)
        self.task_reset_btn.setEnabled(True)

        # 如果启用了自动更新，则重新启动状态更新定时器
        if self.auto_update_check.isChecked():
            self.status_timer.start(3000)
            self.add_monitor_message("自动重连成功，已恢复状态更新")

    def _on_reconnect_failed(self):
        """重连失败后的处理"""
        if self.reconnect_attempts >= self.max_reconnect_attempts:
            self.add_monitor_message(f"自动重连失败，已达到最大重试次数 ({self.max_reconnect_attempts})")
            self.status_label.setText("自动重连失败")
            # 重置重连状态
            self.reconnect_attempts = 0
        else:
            # 继续尝试重连
            next_attempt_time = self.reconnect_interval
            self.add_monitor_message(f"重连失败，{next_attempt_time}秒后重试...")
            self.status_label.setText(f"重连失败，{next_attempt_time}秒后重试")

            # 重新启动重连定时器
            if self.auto_reconnect_enabled:
                self.reconnect_timer.start(self.reconnect_interval * 1000)

    def manual_update_status(self):
        """手动更新状态"""
        current_modbus = self.get_current_modbus()
        if not current_modbus.is_connected():
            return

        self.add_monitor_message("手动更新状态...")
        self.status_label.setText("正在更新状态...")
        QApplication.processEvents()  # 确保UI更新

        # 调用状态更新函数
        self.update_status()

        self.add_monitor_message("状态更新完成")
        self.status_label.setText("状态更新完成")

    def create_port_config_ui(self, layout):
        """创建端口配置界面"""
        # 添加滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # 创建滚动内容部件
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        scroll_layout.setSpacing(15)
        scroll_layout.setContentsMargins(20, 20, 20, 20)

        # 设置滚动区域
        scroll_area.setWidget(scroll_content)
        layout.addWidget(scroll_area)

        # 统一的输入框样式
        input_style = """
            QComboBox, QSpinBox, QLineEdit {
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 13px;
                background-color: white;
                min-height: 20px;
                color: #2c3e50;
            }
            QComboBox:focus, QSpinBox:focus, QLineEdit:focus {
                border-color: #3498db;
                background-color: #f8fcff;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
                background-color: #f0f0f0;
            }
            QComboBox::drop-down:hover {
                background-color: #3498db;
            }
            QComboBox::down-arrow {
                width: 12px;
                height: 12px;
                image: none;
                border: 2px solid #2c3e50;
                border-top: none;
                border-left: none;
                margin: 2px;
            }
            QComboBox QAbstractItemView {
                border: 1px solid #e0e0e0;
                background-color: white;
                color: #2c3e50;
                font-size: 13px;
                selection-background-color: #3498db;
                selection-color: white;
                outline: none;
            }
            QComboBox QAbstractItemView::item {
                padding: 8px 12px;
                border: none;
                color: #2c3e50;
                background-color: white;
            }
            QComboBox QAbstractItemView::item:hover {
                background-color: #e8f4fd;
                color: #2c3e50;
            }
            QComboBox QAbstractItemView::item:selected {
                background-color: #3498db;
                color: white;
            }
            QSpinBox::up-button, QSpinBox::down-button {
                width: 20px;
                border: none;
                background-color: #f0f0f0;
            }
            QSpinBox::up-button:hover, QSpinBox::down-button:hover {
                background-color: #3498db;
            }
        """

        # 创建端口配置组
        config_group = QGroupBox("🔌 端口配置")
        config_group.setStyleSheet("""
            QGroupBox {
                border: 2px solid #e8f4fd;
                border-radius: 8px;
                margin-top: 10px;
                margin-bottom: 10px;
                background-color: #f8fcff;
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 5px 10px;
                background-color: white;
                border-radius: 4px;
            }
        """)
        config_layout = QGridLayout()
        config_layout.setSpacing(15)
        config_layout.setContentsMargins(20, 20, 20, 15)

        # 协议选择
        protocol_label = QLabel("通讯协议")
        protocol_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 13px;")
        config_layout.addWidget(protocol_label, 0, 0)
        self.protocol_combo = QComboBox()
        self.protocol_combo.setStyleSheet(input_style)
        self.protocol_combo.addItem("Modbus RTU", "RTU")
        self.protocol_combo.addItem("Modbus TCP", "TCP")
        # 设置当前协议
        if self.protocol_type == "TCP":
            self.protocol_combo.setCurrentIndex(1)
        else:
            self.protocol_combo.setCurrentIndex(0)
        self.protocol_combo.currentTextChanged.connect(self.on_protocol_changed)
        config_layout.addWidget(self.protocol_combo, 0, 1)

        # RTU协议配置组
        self.rtu_config_group = QGroupBox("🔌 RTU协议配置")
        self.rtu_config_group.setStyleSheet("""
            QGroupBox {
                border: 2px solid #e8f4fd;
                border-radius: 8px;
                margin-top: 10px;
                margin-bottom: 10px;
                background-color: #f8fcff;
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 5px 10px;
                background-color: white;
                border-radius: 4px;
            }
        """)
        rtu_layout = QGridLayout()
        rtu_layout.setSpacing(15)
        rtu_layout.setContentsMargins(20, 20, 20, 15)

        # 端口设置
        port_label = QLabel("端口名称")
        port_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 13px;")
        rtu_layout.addWidget(port_label, 0, 0)
        self.port_name_combo = QComboBox()
        self.port_name_combo.setStyleSheet(input_style)
        # 添加可用端口
        ports = serial.tools.list_ports.comports()
        for port in ports:
            self.port_name_combo.addItem(port.device)
        # 设置当前端口
        if self.modbus.port:
            index = self.port_name_combo.findText(self.modbus.port)
            if index >= 0:
                self.port_name_combo.setCurrentIndex(index)
        rtu_layout.addWidget(self.port_name_combo, 0, 1)

        # 波特率设置
        baudrate_label = QLabel("波特率")
        baudrate_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 13px;")
        rtu_layout.addWidget(baudrate_label, 1, 0)
        self.baudrate_config_combo = QComboBox()
        self.baudrate_config_combo.setStyleSheet(input_style)
        for baudrate in [4800, 9600, 19200, 38400, 57600, 115200]:
            self.baudrate_config_combo.addItem(str(baudrate))
        self.baudrate_config_combo.setCurrentText(str(self.modbus.baudrate))
        rtu_layout.addWidget(self.baudrate_config_combo, 1, 1)

        # 数据位设置
        bytesize_label = QLabel("数据位")
        bytesize_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 13px;")
        rtu_layout.addWidget(bytesize_label, 2, 0)
        self.bytesize_combo = QComboBox()
        self.bytesize_combo.setStyleSheet(input_style)
        self.bytesize_combo.addItem("5", serial.FIVEBITS)
        self.bytesize_combo.addItem("6", serial.SIXBITS)
        self.bytesize_combo.addItem("7", serial.SEVENBITS)
        self.bytesize_combo.addItem("8", serial.EIGHTBITS)
        self.bytesize_combo.setCurrentIndex(3)  # 默认8位
        rtu_layout.addWidget(self.bytesize_combo, 2, 1)

        # 校验位设置
        parity_label = QLabel("校验位")
        parity_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 13px;")
        rtu_layout.addWidget(parity_label, 3, 0)
        self.parity_combo = QComboBox()
        self.parity_combo.setStyleSheet(input_style)
        self.parity_combo.addItem("无", serial.PARITY_NONE)
        self.parity_combo.addItem("奇校验", serial.PARITY_ODD)
        self.parity_combo.addItem("偶校验", serial.PARITY_EVEN)
        self.parity_combo.addItem("标记", serial.PARITY_MARK)
        self.parity_combo.addItem("空格", serial.PARITY_SPACE)
        rtu_layout.addWidget(self.parity_combo, 3, 1)

        # 停止位设置
        stopbits_label = QLabel("停止位")
        stopbits_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 13px;")
        rtu_layout.addWidget(stopbits_label, 4, 0)
        self.stopbits_combo = QComboBox()
        self.stopbits_combo.setStyleSheet(input_style)
        self.stopbits_combo.addItem("1", serial.STOPBITS_ONE)
        self.stopbits_combo.addItem("1.5", serial.STOPBITS_ONE_POINT_FIVE)
        self.stopbits_combo.addItem("2", serial.STOPBITS_TWO)
        rtu_layout.addWidget(self.stopbits_combo, 4, 1)

        # 可用端口列表
        ports_label = QLabel("可用端口")
        ports_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 13px;")
        rtu_layout.addWidget(ports_label, 5, 0)
        self.available_ports_list = QTextEdit()
        self.available_ports_list.setStyleSheet("""
            QTextEdit {
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                padding: 10px;
                font-family: 'Courier New';
                font-size: 12px;
                background-color: #f9f9f9;
            }
        """)
        self.available_ports_list.setReadOnly(True)
        self.available_ports_list.setMaximumHeight(80)
        rtu_layout.addWidget(self.available_ports_list, 5, 1, 2, 1)

        # 刷新端口列表按钮
        refresh_ports_btn = QPushButton("🔄 刷新端口")
        refresh_ports_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: bold;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #1f618d;
            }
        """)
        refresh_ports_btn.clicked.connect(self.refresh_available_ports)
        rtu_layout.addWidget(refresh_ports_btn, 6, 0)

        self.rtu_config_group.setLayout(rtu_layout)
        config_layout.addWidget(self.rtu_config_group, 1, 0, 1, 2)

        # TCP协议配置组
        self.tcp_config_group = QGroupBox("🌐 TCP协议配置")
        self.tcp_config_group.setStyleSheet("""
            QGroupBox {
                border: 2px solid #e8f6f3;
                border-radius: 8px;
                margin-top: 10px;
                margin-bottom: 10px;
                background-color: #f0fdf4;
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 5px 10px;
                background-color: white;
                border-radius: 4px;
            }
        """)
        tcp_layout = QGridLayout()
        tcp_layout.setSpacing(15)
        tcp_layout.setContentsMargins(20, 20, 20, 15)

        # IP地址设置
        host_label = QLabel("IP地址")
        host_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 13px;")
        tcp_layout.addWidget(host_label, 0, 0)
        self.tcp_host_edit = QLineEdit()
        self.tcp_host_edit.setStyleSheet("""
            QLineEdit {
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 13px;
                background-color: white;
                min-height: 20px;
            }
            QLineEdit:focus {
                border-color: #27ae60;
                background-color: #f8fff8;
            }
        """)
        self.tcp_host_edit.setText(getattr(self, 'tcp_host', '*************'))
        self.tcp_host_edit.setPlaceholderText("例如: *************")
        tcp_layout.addWidget(self.tcp_host_edit, 0, 1)

        # 端口设置
        tcp_port_label = QLabel("端口号")
        tcp_port_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 13px;")
        tcp_layout.addWidget(tcp_port_label, 1, 0)
        self.tcp_port_spin = QSpinBox()
        self.tcp_port_spin.setStyleSheet(input_style)
        self.tcp_port_spin.setRange(1, 65535)
        self.tcp_port_spin.setValue(getattr(self, 'tcp_port', 502))
        tcp_layout.addWidget(self.tcp_port_spin, 1, 1)

        self.tcp_config_group.setLayout(tcp_layout)
        config_layout.addWidget(self.tcp_config_group, 2, 0, 1, 2)

        # 通用超时设置
        timeout_label = QLabel("超时 (秒)")
        timeout_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 13px;")
        config_layout.addWidget(timeout_label, 3, 0)
        self.timeout_spin = QDoubleSpinBox()
        self.timeout_spin.setStyleSheet(input_style)
        self.timeout_spin.setRange(0.1, 10.0)
        self.timeout_spin.setSingleStep(0.1)
        self.timeout_spin.setValue(self.modbus.timeout)
        config_layout.addWidget(self.timeout_spin, 3, 1)

        # 统一的按钮样式
        button_style = """
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: bold;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
        """

        # 保存按钮
        save_btn = QPushButton("💾 保存配置")
        save_btn.setStyleSheet(button_style)
        save_btn.clicked.connect(self.save_port_config)
        config_layout.addWidget(save_btn, 4, 0, 1, 2)

        config_group.setLayout(config_layout)
        scroll_layout.addWidget(config_group)

        # 根据当前协议显示/隐藏相应的配置组
        self.on_protocol_changed()

        # 创建寄存器映射配置组
        register_group = QGroupBox("📋 寄存器映射配置")
        register_group.setStyleSheet("""
            QGroupBox {
                border: 2px solid #e8f6f3;
                border-radius: 8px;
                margin-top: 10px;
                margin-bottom: 10px;
                background-color: #f0fdf4;
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 5px 10px;
                background-color: white;
                border-radius: 4px;
            }
        """)
        register_layout = QGridLayout()
        register_layout.setSpacing(15)
        register_layout.setContentsMargins(20, 20, 20, 15)



        # 添加PLC地址映射快速配置按钮
        plc_config_label = QLabel("PLC地址映射")
        plc_config_label.setStyleSheet("font-weight: bold; color: #e74c3c; font-size: 14px;")
        register_layout.addWidget(plc_config_label, 0, 0)

        apply_plc_btn = QPushButton("🔧 应用PLC地址映射")
        apply_plc_btn.setStyleSheet(button_style.replace("#27ae60", "#e74c3c").replace("#229954", "#c0392b").replace("#1e8449", "#a93226"))
        apply_plc_btn.clicked.connect(self.apply_plc_address_mapping)
        apply_plc_btn.setToolTip("一键应用PLC提供的地址映射（HD1000-HD1012对应42088-42100，M10-M15对应10-15）")
        register_layout.addWidget(apply_plc_btn, 0, 1, 1, 3)

        # 寄存器映射配置组已在前面定义，现在添加内容

        # 剪切参数寄存器 - 第二行
        length_reg_label = QLabel("剪切长度寄存器")
        length_reg_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 13px;")
        register_layout.addWidget(length_reg_label, 1, 0)
        self.length_reg_spin = QSpinBox()
        self.length_reg_spin.setStyleSheet(input_style)
        self.length_reg_spin.setRange(0, 99999)  # 增加范围以支持更大的地址
        self.length_reg_spin.setValue(self.register_map['length'])
        register_layout.addWidget(self.length_reg_spin, 1, 1)

        increment_reg_label = QLabel("增移量寄存器")
        increment_reg_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 13px;")
        register_layout.addWidget(increment_reg_label, 1, 2)
        self.increment_reg_spin = QSpinBox()
        self.increment_reg_spin.setStyleSheet(input_style)
        self.increment_reg_spin.setRange(0, 99999)
        self.increment_reg_spin.setValue(self.register_map['increment'])
        register_layout.addWidget(self.increment_reg_spin, 1, 3)

        # 第三行
        speed_reg_label = QLabel("伺服速度寄存器")
        speed_reg_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 13px;")
        register_layout.addWidget(speed_reg_label, 2, 0)
        self.speed_reg_spin = QSpinBox()
        self.speed_reg_spin.setStyleSheet(input_style)
        self.speed_reg_spin.setRange(0, 99999)
        self.speed_reg_spin.setValue(self.register_map['speed'])
        register_layout.addWidget(self.speed_reg_spin, 2, 1)

        error_adjust_reg_label = QLabel("误差调整寄存器")
        error_adjust_reg_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 13px;")
        register_layout.addWidget(error_adjust_reg_label, 2, 2)
        self.error_adjust_reg_spin = QSpinBox()
        self.error_adjust_reg_spin.setStyleSheet(input_style)
        self.error_adjust_reg_spin.setRange(0, 99999)
        self.error_adjust_reg_spin.setValue(self.register_map['error_adjust'])
        register_layout.addWidget(self.error_adjust_reg_spin, 2, 3)

        # 第四行
        roots_reg_label = QLabel("根数寄存器")
        roots_reg_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 13px;")
        register_layout.addWidget(roots_reg_label, 3, 0)
        self.roots_reg_spin = QSpinBox()
        self.roots_reg_spin.setStyleSheet(input_style)
        self.roots_reg_spin.setRange(0, 99999)
        self.roots_reg_spin.setValue(self.register_map['roots'])
        register_layout.addWidget(self.roots_reg_spin, 3, 1)

        machines_reg_label = QLabel("台数寄存器")
        machines_reg_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 13px;")
        register_layout.addWidget(machines_reg_label, 3, 2)
        self.machines_reg_spin = QSpinBox()
        self.machines_reg_spin.setStyleSheet(input_style)
        self.machines_reg_spin.setRange(0, 99999)
        self.machines_reg_spin.setValue(self.register_map['machines'])
        register_layout.addWidget(self.machines_reg_spin, 3, 3)

        # 控制寄存器已改为位地址控制，不再需要寄存器配置

        # 状态寄存器 - 第五行
        current_length_reg_label = QLabel("当前长度寄存器")
        current_length_reg_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 13px;")
        register_layout.addWidget(current_length_reg_label, 4, 0)
        self.current_length_reg_spin = QSpinBox()
        self.current_length_reg_spin.setStyleSheet(input_style)
        self.current_length_reg_spin.setRange(0, 99999)
        self.current_length_reg_spin.setValue(self.register_map['current_length'])
        register_layout.addWidget(self.current_length_reg_spin, 4, 1)

        completed_count_reg_label = QLabel("已完成数量寄存器")
        completed_count_reg_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 13px;")
        register_layout.addWidget(completed_count_reg_label, 4, 2)
        self.completed_count_reg_spin = QSpinBox()
        self.completed_count_reg_spin.setStyleSheet(input_style)
        self.completed_count_reg_spin.setRange(0, 99999)
        self.completed_count_reg_spin.setValue(self.register_map['completed_count'])
        register_layout.addWidget(self.completed_count_reg_spin, 4, 3)

        # 第六行
        machine_status_reg_label = QLabel("机器状态寄存器")
        machine_status_reg_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 13px;")
        register_layout.addWidget(machine_status_reg_label, 5, 0)
        self.machine_status_reg_spin = QSpinBox()
        self.machine_status_reg_spin.setStyleSheet(input_style)
        self.machine_status_reg_spin.setRange(0, 99999)
        self.machine_status_reg_spin.setValue(self.register_map['machine_status'])
        register_layout.addWidget(self.machine_status_reg_spin, 5, 1)

        # 保存寄存器配置按钮
        save_reg_btn = QPushButton("💾 保存寄存器配置")
        save_reg_btn.setStyleSheet(button_style.replace("#27ae60", "#e74c3c").replace("#229954", "#c0392b").replace("#1e8449", "#a93226"))
        save_reg_btn.clicked.connect(self.save_port_config)  # 使用相同的保存函数
        register_layout.addWidget(save_reg_btn, 6, 0, 1, 4)

        register_group.setLayout(register_layout)
        scroll_layout.addWidget(register_group)

        # 创建位地址映射配置组
        bit_group = QGroupBox("🔘 位地址映射配置")
        bit_group.setStyleSheet("""
            QGroupBox {
                border: 2px solid #fff2e6;
                border-radius: 8px;
                margin-top: 10px;
                margin-bottom: 10px;
                background-color: #fffaf7;
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 5px 10px;
                background-color: white;
                border-radius: 4px;
            }
        """)
        bit_layout = QGridLayout()
        bit_layout.setSpacing(15)
        bit_layout.setContentsMargins(20, 20, 20, 15)

        # 位地址配置 - 第一行
        manual_auto_bit_label = QLabel("手自动切换位 (M10)")
        manual_auto_bit_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 13px;")
        bit_layout.addWidget(manual_auto_bit_label, 0, 0)
        self.manual_auto_bit_spin = QSpinBox()
        self.manual_auto_bit_spin.setStyleSheet(input_style)
        self.manual_auto_bit_spin.setRange(0, 20479)
        self.manual_auto_bit_spin.setValue(self.bit_map.get('manual_auto_switch', 10))
        bit_layout.addWidget(self.manual_auto_bit_spin, 0, 1)

        reset_bit_label = QLabel("上位机复位位 (M11)")
        reset_bit_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 13px;")
        bit_layout.addWidget(reset_bit_label, 0, 2)
        self.reset_bit_spin = QSpinBox()
        self.reset_bit_spin.setStyleSheet(input_style)
        self.reset_bit_spin.setRange(0, 20479)
        self.reset_bit_spin.setValue(self.bit_map.get('host_reset_button', 11))
        bit_layout.addWidget(self.reset_bit_spin, 0, 3)

        # 第二行 - 只读位
        resetting_bit_label = QLabel("复位中位 (M12) - 只读")
        resetting_bit_label.setStyleSheet("font-weight: bold; color: #6c757d; font-size: 13px;")
        bit_layout.addWidget(resetting_bit_label, 1, 0)
        self.resetting_bit_spin = QSpinBox()
        self.resetting_bit_spin.setStyleSheet(input_style)
        self.resetting_bit_spin.setRange(0, 20479)
        self.resetting_bit_spin.setValue(self.bit_map.get('resetting', 12))
        bit_layout.addWidget(self.resetting_bit_spin, 1, 1)

        standby_bit_label = QLabel("待机中位 (M13) - 只读")
        standby_bit_label.setStyleSheet("font-weight: bold; color: #6c757d; font-size: 13px;")
        bit_layout.addWidget(standby_bit_label, 1, 2)
        self.standby_bit_spin = QSpinBox()
        self.standby_bit_spin.setStyleSheet(input_style)
        self.standby_bit_spin.setRange(0, 20479)
        self.standby_bit_spin.setValue(self.bit_map.get('standby', 13))
        bit_layout.addWidget(self.standby_bit_spin, 1, 3)

        # 第三行 - 只读位
        running_bit_label = QLabel("自动运行位 (M14) - 只读")
        running_bit_label.setStyleSheet("font-weight: bold; color: #6c757d; font-size: 13px;")
        bit_layout.addWidget(running_bit_label, 2, 0)
        self.running_bit_spin = QSpinBox()
        self.running_bit_spin.setStyleSheet(input_style)
        self.running_bit_spin.setRange(0, 20479)
        self.running_bit_spin.setValue(self.bit_map.get('auto_running', 14))
        bit_layout.addWidget(self.running_bit_spin, 2, 1)

        emergency_bit_label = QLabel("急停中位 (M15) - 只读")
        emergency_bit_label.setStyleSheet("font-weight: bold; color: #6c757d; font-size: 13px;")
        bit_layout.addWidget(emergency_bit_label, 2, 2)
        self.emergency_bit_spin = QSpinBox()
        self.emergency_bit_spin.setStyleSheet(input_style)
        self.emergency_bit_spin.setRange(0, 20479)
        self.emergency_bit_spin.setValue(self.bit_map.get('emergency_stop', 15))
        bit_layout.addWidget(self.emergency_bit_spin, 2, 3)

        # 第四行 - 启动和停止位地址
        start_bit_label = QLabel("启动位 (M16)")
        start_bit_label.setStyleSheet("font-weight: bold; color: #27ae60; font-size: 13px;")
        bit_layout.addWidget(start_bit_label, 3, 0)
        self.start_bit_spin = QSpinBox()
        self.start_bit_spin.setStyleSheet(input_style)
        self.start_bit_spin.setRange(0, 20479)
        self.start_bit_spin.setValue(self.bit_map.get('start', 16))
        bit_layout.addWidget(self.start_bit_spin, 3, 1)

        stop_bit_label = QLabel("停止位 (M17)")
        stop_bit_label.setStyleSheet("font-weight: bold; color: #e74c3c; font-size: 13px;")
        bit_layout.addWidget(stop_bit_label, 3, 2)
        self.stop_bit_spin = QSpinBox()
        self.stop_bit_spin.setStyleSheet(input_style)
        self.stop_bit_spin.setRange(0, 20479)
        self.stop_bit_spin.setValue(self.bit_map.get('stop', 17))
        bit_layout.addWidget(self.stop_bit_spin, 3, 3)

        # 第五行 - 红外感应器输入
        infrared_bit_label = QLabel("红外感应器 (X7) - 只读")
        infrared_bit_label.setStyleSheet("font-weight: bold; color: #9b59b6; font-size: 13px;")
        bit_layout.addWidget(infrared_bit_label, 4, 0)
        self.infrared_bit_spin = QSpinBox()
        self.infrared_bit_spin.setStyleSheet(input_style)
        self.infrared_bit_spin.setRange(0, 20479)
        self.infrared_bit_spin.setValue(self.bit_map.get('infrared_sensor', 7))
        self.infrared_bit_spin.setToolTip("红外感应器输入端口，用于检测进料口是否有物料")
        bit_layout.addWidget(self.infrared_bit_spin, 4, 1)

        # 添加说明标签
        infrared_desc_label = QLabel("检测到物料时为ON，无物料时为OFF")
        infrared_desc_label.setStyleSheet("font-size: 11px; color: #7f8c8d; font-style: italic;")
        bit_layout.addWidget(infrared_desc_label, 4, 2, 1, 2)

        # 保存位地址配置按钮
        save_bit_btn = QPushButton("💾 保存位地址配置")
        save_bit_btn.setStyleSheet(button_style.replace("#27ae60", "#f39c12").replace("#229954", "#d35400").replace("#1e8449", "#ba4a00"))
        save_bit_btn.clicked.connect(self.save_port_config)  # 使用相同的保存函数
        bit_layout.addWidget(save_bit_btn, 5, 0, 1, 4)

        bit_group.setLayout(bit_layout)
        scroll_layout.addWidget(bit_group)

        # 初始化刷新端口列表
        self.refresh_available_ports()

    def on_protocol_changed(self):
        """协议切换时的处理"""
        protocol_data = self.protocol_combo.currentData()
        if protocol_data:
            self.protocol_type = protocol_data
        else:
            # 如果没有数据，根据文本判断
            if "TCP" in self.protocol_combo.currentText():
                self.protocol_type = "TCP"
            else:
                self.protocol_type = "RTU"

        # 根据协议类型显示/隐藏相应的配置组
        if self.protocol_type == "TCP":
            self.rtu_config_group.hide()
            self.tcp_config_group.show()
        else:
            self.tcp_config_group.hide()
            self.rtu_config_group.show()

        # 更新主界面的端口信息显示
        self.update_port_info_display()

    def refresh_available_ports(self):
        """刷新可用端口列表"""
        # 清空文本框
        self.available_ports_list.clear()

        # 保存当前选择的端口
        current_port = self.port_name_combo.currentText()

        # 清空下拉框
        self.port_name_combo.clear()

        # 获取可用端口
        ports = serial.tools.list_ports.comports()
        if ports:
            for port in ports:
                # 添加到文本框
                self.available_ports_list.append(f"{port.device} - {port.description}")
                # 添加到下拉框
                self.port_name_combo.addItem(port.device)
        else:
            self.available_ports_list.append("未检测到可用端口")

        # 恢复之前选择的端口（如果仍然可用）
        if current_port:
            index = self.port_name_combo.findText(current_port)
            if index >= 0:
                self.port_name_combo.setCurrentIndex(index)

        self.status_label.setText("已刷新可用端口列表")

    def save_port_config(self):
        """保存端口配置"""
        # 检查是否已连接
        current_modbus = self.get_current_modbus()
        if current_modbus.is_connected():
            msgBox = QMessageBox(QMessageBox.Question, "确认", "保存配置需要断开当前连接，是否继续？",
                              QMessageBox.Yes | QMessageBox.No, self)
            msgBox.button(QMessageBox.Yes).setText("是")
            msgBox.button(QMessageBox.No).setText("否")
            msgBox.setDefaultButton(QMessageBox.No)
            reply = msgBox.exec_()
            if reply == QMessageBox.No:
                return
            # 断开连接
            self.toggle_connection()

        # 保存协议类型
        self.protocol_type = self.protocol_combo.currentData() or ("TCP" if "TCP" in self.protocol_combo.currentText() else "RTU")

        # 保存RTU配置
        if hasattr(self, 'port_name_combo'):
            self.modbus.port = self.port_name_combo.currentText()
        if hasattr(self, 'baudrate_config_combo'):
            self.modbus.baudrate = int(self.baudrate_config_combo.currentText())
        if hasattr(self, 'bytesize_combo'):
            self.modbus.bytesize = self.bytesize_combo.currentData()
        if hasattr(self, 'parity_combo'):
            self.modbus.parity = self.parity_combo.currentData()
        if hasattr(self, 'stopbits_combo'):
            self.modbus.stopbits = self.stopbits_combo.currentData()

        # 保存TCP配置
        if hasattr(self, 'tcp_host_edit'):
            self.tcp_host = self.tcp_host_edit.text()
            self.modbus_tcp.host = self.tcp_host
        if hasattr(self, 'tcp_port_spin'):
            self.tcp_port = self.tcp_port_spin.value()
            self.modbus_tcp.port = self.tcp_port

        # 保存通用超时配置
        if hasattr(self, 'timeout_spin'):
            timeout_value = self.timeout_spin.value()
            self.modbus.timeout = timeout_value
            self.modbus_tcp.timeout = timeout_value

        # 保存寄存器映射配置
        self.register_map['length'] = self.length_reg_spin.value()
        self.register_map['increment'] = self.increment_reg_spin.value()
        self.register_map['speed'] = self.speed_reg_spin.value()
        self.register_map['error_adjust'] = self.error_adjust_reg_spin.value()
        self.register_map['roots'] = self.roots_reg_spin.value()  # 新增根数寄存器
        self.register_map['machines'] = self.machines_reg_spin.value()  # 新增台数寄存器
        # 启动停止和复位已改为位地址控制，不再保存寄存器配置
        self.register_map['current_length'] = self.current_length_reg_spin.value()
        self.register_map['completed_count'] = self.completed_count_reg_spin.value()
        self.register_map['machine_status'] = self.machine_status_reg_spin.value()

        # 保存位地址映射配置
        self.bit_map['manual_auto_switch'] = self.manual_auto_bit_spin.value()
        self.bit_map['host_reset_button'] = self.reset_bit_spin.value()
        self.bit_map['resetting'] = self.resetting_bit_spin.value()
        self.bit_map['standby'] = self.standby_bit_spin.value()
        self.bit_map['auto_running'] = self.running_bit_spin.value()
        self.bit_map['emergency_stop'] = self.emergency_bit_spin.value()
        self.bit_map['start'] = self.start_bit_spin.value()
        self.bit_map['stop'] = self.stop_bit_spin.value()
        self.bit_map['infrared_sensor'] = self.infrared_bit_spin.value()

        # 更新数据类型配置
        self.data_types['read_write_bits'] = [self.manual_auto_bit_spin.value(), self.reset_bit_spin.value(),
                                             self.start_bit_spin.value(), self.stop_bit_spin.value()]
        self.data_types['read_only_bits'] = [self.resetting_bit_spin.value(), self.standby_bit_spin.value(),
                                           self.running_bit_spin.value(), self.emergency_bit_spin.value(),
                                           self.infrared_bit_spin.value()]

        # 更新主界面的端口信息显示
        self.update_port_info_display()

        # 更新隐藏的组件（为了保持代码兼容性）
        current_port_index = self.port_combo.findText(self.modbus.port)
        if current_port_index >= 0:
            self.port_combo.setCurrentIndex(current_port_index)
        else:
            # 如果端口不在列表中，添加它
            self.port_combo.addItem(self.modbus.port)
            self.port_combo.setCurrentText(self.modbus.port)

        # 更新隐藏的波特率选择
        self.baudrate_combo.setCurrentText(str(self.modbus.baudrate))

        # 保存配置到文件
        self.save_config()

        QMessageBox.information(self, "成功", "配置已保存")
        self.status_label.setText("配置已保存")

    def apply_plc_address_mapping(self):
        """应用PLC提供的地址映射"""
        try:
            # 显示确认对话框
            msgBox = QMessageBox(QMessageBox.Question, "确认应用PLC地址映射",
                                "这将应用PLC提供的标准地址映射：\n\n"
                                "寄存器地址（32位）：\n"
                                "• 手动转速: HD1000 → 42088\n"
                                "• 自动转速: HD1002 → 42090\n"
                                "• 第一次相对自动定位距离: HD1004 → 42092\n"
                                "• 每次递增的补偿距离: HD1006 → 42094\n"
                                "• 自动运行的相对距离: HD1008 → 42096\n"
                                "• 设定数量: HD1010 → 42098\n"
                                "• 已完成数量: HD1012 → 42100（只读）\n\n"
                                "位地址：\n"
                                "• 手自动切换: M10 → 10（读写）\n"
                                "• 上位机复位按钮: M11 → 11（读写）\n"
                                "• 复位中: M12 → 12（只读）\n"
                                "• 待机中: M13 → 13（只读）\n"
                                "• 自动运行中: M14 → 14（只读）\n"
                                "• 急停中: M15 → 15（只读）\n\n"
                                "是否继续？",
                                QMessageBox.Yes | QMessageBox.No, self)
            msgBox.button(QMessageBox.Yes).setText("是")
            msgBox.button(QMessageBox.No).setText("否")
            msgBox.setDefaultButton(QMessageBox.No)
            reply = msgBox.exec_()

            if reply == QMessageBox.No:
                return

            # 应用PLC寄存器地址映射
            self.length_reg_spin.setValue(42092)  # 剪切长度 (HD1004)
            self.increment_reg_spin.setValue(42094)  # 增移量 (HD1006)
            self.speed_reg_spin.setValue(42088)  # 伺服速度 (HD1000)
            self.error_adjust_reg_spin.setValue(42092)  # 第一次相对自动定位距离
            self.roots_reg_spin.setValue(42098)  # 根数 (HD1010)
            self.machines_reg_spin.setValue(42104)  # 台数 (HD1016)
            # 启动停止和复位已改为位地址控制，不再设置寄存器
            self.current_length_reg_spin.setValue(3000)  # 保持原有状态寄存器
            self.completed_count_reg_spin.setValue(42100)  # 已完成数量 (HD1012)
            self.machine_status_reg_spin.setValue(3002)  # 保持原有状态寄存器

            # 应用PLC位地址映射
            self.manual_auto_bit_spin.setValue(10)  # M10
            self.reset_bit_spin.setValue(11)  # M11
            self.resetting_bit_spin.setValue(12)  # M12
            self.standby_bit_spin.setValue(13)  # M13
            self.running_bit_spin.setValue(14)  # M14
            self.emergency_bit_spin.setValue(15)  # M15
            self.start_bit_spin.setValue(16)  # M16 启动位
            self.stop_bit_spin.setValue(17)  # M17 停止位
            self.infrared_bit_spin.setValue(7)  # X7 红外感应器

            QMessageBox.information(self, "成功",
                                  "PLC地址映射已应用！\n\n"
                                  "请点击'保存寄存器配置'或'保存位地址配置'按钮保存更改。")
            self.status_label.setText("PLC地址映射已应用，请保存配置")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"应用PLC地址映射时发生错误: {str(e)}")
            print(f"应用PLC地址映射错误: {str(e)}")


    def save_config(self):
        """保存配置到文件"""
        try:
            config = {
                'protocol_type': self.protocol_type,
                'port': self.modbus.port,
                'baudrate': self.modbus.baudrate,
                'bytesize': self.modbus.bytesize,
                'parity': self.modbus.parity,
                'stopbits': self.modbus.stopbits,
                'timeout': self.modbus.timeout,
                'tcp_host': getattr(self, 'tcp_host', '*************'),
                'tcp_port': getattr(self, 'tcp_port', 502),
                'tcp_timeout': self.modbus_tcp.timeout,
                'register_map': self.register_map,
                'bit_map': self.bit_map,
                'data_types': self.data_types,
                'infrared_enabled': self.infrared_enabled,  # 保存红外感应功能开关状态
                'splitter_sizes': self.main_splitter.sizes() if hasattr(self, 'main_splitter') else [200, 500]
            }

            # 由于一些值不能直接序列化为JSON，需要转换
            if 'bytesize' in config:
                config['bytesize'] = int(config['bytesize'])
            if 'parity' in config:
                config['parity'] = str(config['parity'])
            if 'stopbits' in config:
                config['stopbits'] = float(config['stopbits'])

            with open(self.config_file, 'w') as f:
                json.dump(config, f, indent=4)
        except Exception as e:
            print(f"保存配置错误: {e}")

    def restore_splitter_state(self):
        """从配置中恢复分割器状态"""
        try:
            if hasattr(self, 'main_splitter') and os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                    if 'splitter_sizes' in config:
                        sizes = config['splitter_sizes']
                        # 确保尺寸有效
                        if len(sizes) == 2 and all(s > 50 for s in sizes):
                            self.main_splitter.setSizes(sizes)
        except Exception as e:
            print(f"恢复分割器状态失败: {e}")
            # 使用默认尺寸
            if hasattr(self, 'main_splitter'):
                self.main_splitter.setSizes([200, 500])

    def create_production_order_ui(self, layout):
        """创建排产单管理界面"""
        # 添加滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # 创建滚动内容部件
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        scroll_layout.setSpacing(15)
        scroll_layout.setContentsMargins(20, 20, 20, 20)

        # 设置滚动区域
        scroll_area.setWidget(scroll_content)
        layout.addWidget(scroll_area)

        # 统一的按钮样式
        button_style = """
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: bold;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
        """

        # 创建排产单导入区域
        import_group = QGroupBox("📁 排产单导入")
        import_group.setStyleSheet("""
            QGroupBox {
                border: 2px solid #e8f6f3;
                border-radius: 8px;
                margin-top: 10px;
                margin-bottom: 10px;
                background-color: #f0fdf4;
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 5px 10px;
                background-color: white;
                border-radius: 4px;
            }
        """)
        import_layout = QHBoxLayout()
        import_layout.setSpacing(15)
        import_layout.setContentsMargins(20, 20, 20, 15)

        # 文件路径显示
        self.order_file_path = QLineEdit()
        self.order_file_path.setStyleSheet("""
            QLineEdit {
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 13px;
                background-color: white;
                min-height: 20px;
            }
            QLineEdit:focus {
                border-color: #27ae60;
                background-color: #f8fff8;
            }
        """)
        self.order_file_path.setReadOnly(True)
        self.order_file_path.setPlaceholderText("请选择排产单文件...")
        import_layout.addWidget(self.order_file_path, 3)

        # 浏览按钮
        browse_btn = QPushButton("📂 浏览文件")
        browse_btn.setStyleSheet(button_style)
        browse_btn.clicked.connect(self.browse_order_file)
        import_layout.addWidget(browse_btn, 1)

        # 导入按钮
        import_btn = QPushButton("📥 导入数据")
        import_btn.setStyleSheet(button_style.replace("#27ae60", "#3498db").replace("#229954", "#2980b9").replace("#1e8449", "#1f618d"))
        import_btn.clicked.connect(self.import_order_file)
        import_layout.addWidget(import_btn, 1)

        import_group.setLayout(import_layout)
        scroll_layout.addWidget(import_group)

        # 创建排产单列表区域
        orders_group = QGroupBox("📋 排产单列表")
        orders_group.setStyleSheet("""
            QGroupBox {
                border: 2px solid #e8f4fd;
                border-radius: 8px;
                margin-top: 10px;
                margin-bottom: 10px;
                background-color: #f8fcff;
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 5px 10px;
                background-color: white;
                border-radius: 4px;
            }
        """)
        orders_layout = QVBoxLayout()
        orders_layout.setSpacing(15)
        orders_layout.setContentsMargins(20, 20, 20, 15)

        # 排产单表格
        self.orders_table = QTableWidget()
        self.orders_table.setStyleSheet("""
            QTableWidget {
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                background-color: white;
                gridline-color: #f0f0f0;
                font-size: 13px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
            }
            QTableWidget::item:selected {
                background-color: #e8f4fd;
                color: #2c3e50;
            }
            QHeaderView::section {
                background-color: #f8f9fa;
                border: 1px solid #e0e0e0;
                padding: 8px;
                font-weight: bold;
                color: #2c3e50;
            }
        """)
        self.orders_table.setColumnCount(8)  # 增加一列用于复选框
        self.orders_table.setHorizontalHeaderLabels(["选择", "代码", "型号", "合同号", "做法", "台数", "剩余台数", "操作"])
        self.orders_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.orders_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.orders_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        orders_layout.addWidget(self.orders_table)

        # 批量操作区域
        batch_group = QGroupBox("⚡ 批量操作")
        batch_group.setStyleSheet("""
            QGroupBox {
                border: 2px solid #fff2e6;
                border-radius: 8px;
                margin-top: 10px;
                margin-bottom: 10px;
                background-color: #fffaf7;
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 5px 10px;
                background-color: white;
                border-radius: 4px;
            }
        """)
        batch_layout = QHBoxLayout()
        batch_layout.setSpacing(15)
        batch_layout.setContentsMargins(20, 20, 20, 15)

        # 全选/取消全选复选框
        self.select_all_checkbox = QCheckBox("全选/取消全选")
        self.select_all_checkbox.setStyleSheet("""
            QCheckBox {
                font-size: 13px;
                font-weight: bold;
                color: #2c3e50;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #e0e0e0;
                border-radius: 4px;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                background-color: #27ae60;
                border-color: #27ae60;
            }
        """)
        self.select_all_checkbox.clicked.connect(self.toggle_select_all)
        batch_layout.addWidget(self.select_all_checkbox)

        batch_layout.addStretch()

        # 一键创建选中任务按钮
        create_selected_btn = QPushButton("📝 创建选中任务")
        create_selected_btn.setStyleSheet(button_style.replace("#27ae60", "#f39c12").replace("#229954", "#d35400").replace("#1e8449", "#ba4a00"))
        create_selected_btn.clicked.connect(self.create_selected_tasks)
        batch_layout.addWidget(create_selected_btn)

        # 一键创建所有任务按钮
        create_all_btn = QPushButton("📋 创建所有任务")
        create_all_btn.setStyleSheet(button_style.replace("#27ae60", "#e74c3c").replace("#229954", "#c0392b").replace("#1e8449", "#a93226"))
        create_all_btn.clicked.connect(self.create_all_tasks)
        batch_layout.addWidget(create_all_btn)

        batch_group.setLayout(batch_layout)
        orders_layout.addWidget(batch_group)

        # 操作按钮区域
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)
        buttons_layout.addStretch()

        # 创建任务按钮
        create_task_btn = QPushButton("➕ 创建任务")
        create_task_btn.setStyleSheet(button_style.replace("#27ae60", "#e74c3c").replace("#229954", "#c0392b").replace("#1e8449", "#a93226"))
        create_task_btn.clicked.connect(self.create_task_from_order)
        buttons_layout.addWidget(create_task_btn)

        # 调整台数按钮
        adjust_quantity_btn = QPushButton("🔧 调整台数")
        adjust_quantity_btn.setStyleSheet(button_style.replace("#27ae60", "#9b59b6").replace("#229954", "#8e44ad").replace("#1e8449", "#6c3483"))
        adjust_quantity_btn.clicked.connect(self.adjust_order_quantity)
        buttons_layout.addWidget(adjust_quantity_btn)

        buttons_layout.addStretch()
        orders_layout.addLayout(buttons_layout)
        orders_group.setLayout(orders_layout)
        scroll_layout.addWidget(orders_group)

    def show_task_details(self, item):
        """双击任务时显示详细信息，并允许修改和删除"""
        row = item.row()
        task = self.task_planner.get_task_by_index(row)
        if not task:
            return

        # 创建现代化的详细信息对话框
        details_dialog = QDialog(self)
        details_dialog.setWindowTitle(f"📋 任务详细信息 - {task['code']}")
        details_dialog.setMinimumWidth(700)
        details_dialog.setMinimumHeight(600)
        details_dialog.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
                border-radius: 8px;
            }
            QGroupBox {
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                margin-top: 10px;
                margin-bottom: 10px;
                background-color: white;
                font-size: 13px;
                font-weight: bold;
                color: #2c3e50;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 5px 10px;
                background-color: #f8f9fa;
                border-radius: 4px;
                border: 1px solid #e0e0e0;
            }
            QLabel {
                font-size: 12px;
                color: #2c3e50;
            }
            QDoubleSpinBox, QSpinBox {
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 14px;
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                background-color: white;
                min-height: 20px;
                color: #2c3e50;
            }
            QDoubleSpinBox:focus, QSpinBox:focus {
                border-color: #3498db;
                background-color: #f8fcff;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 12px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #1f618d;
            }
        """)

        # 创建布局
        layout = QVBoxLayout(details_dialog)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 添加基本信息
        info_group = QGroupBox("📄 基本信息")
        info_layout = QGridLayout()
        info_layout.setSpacing(12)
        info_layout.setContentsMargins(15, 15, 15, 15)

        # 标签样式（与基本信息字体大小一致）
        label_style = "font-weight: bold; color: #34495e; font-size: 13px;"
        value_style = "color: #2c3e50; font-size: 13px; background-color: #f8f9fa; padding: 4px 8px; border-radius: 4px;"

        # 不可修改的信息
        code_label = QLabel("代码:")
        code_label.setStyleSheet(label_style)
        info_layout.addWidget(code_label, 0, 0)
        code_value = QLabel(task['code'])
        code_value.setStyleSheet(value_style)
        info_layout.addWidget(code_value, 0, 1)

        model_label = QLabel("型号:")
        model_label.setStyleSheet(label_style)
        info_layout.addWidget(model_label, 0, 2)
        model_value = QLabel(task.get('model', ''))
        model_value.setStyleSheet(value_style)
        info_layout.addWidget(model_value, 0, 3)

        contract_label = QLabel("合同号:")
        contract_label.setStyleSheet(label_style)
        info_layout.addWidget(contract_label, 1, 0)
        contract_value = QLabel(task['contract'])
        contract_value.setStyleSheet(value_style)
        info_layout.addWidget(contract_value, 1, 1)

        copper_label = QLabel("铜带规格:")
        copper_label.setStyleSheet(label_style)
        info_layout.addWidget(copper_label, 1, 2)
        copper_value = QLabel(task.get('copper_spec', ''))
        copper_value.setStyleSheet(value_style)
        info_layout.addWidget(copper_value, 1, 3)

        # 可修改的信息
        info_layout.addWidget(QLabel("长度(mm):"), 2, 0)
        length_spin = QDoubleSpinBox()
        length_spin.setRange(0, 99999.99)
        length_spin.setDecimals(2)
        length_spin.setSingleStep(1.0)
        length_spin.setValue(task['length'])
        info_layout.addWidget(length_spin, 2, 1)

        info_layout.addWidget(QLabel("递增量(mm):"), 2, 2)
        increment_spin = QDoubleSpinBox()
        increment_spin.setRange(-327.68, 327.67)
        increment_spin.setDecimals(2)
        increment_spin.setSingleStep(0.1)
        increment_spin.setValue(task['increment'])
        info_layout.addWidget(increment_spin, 2, 3)

        info_layout.addWidget(QLabel("根数:"), 3, 0)
        roots_spin = QSpinBox()
        roots_spin.setRange(0, 100)
        roots_spin.setValue(task.get('roots', 0))
        info_layout.addWidget(roots_spin, 3, 1)

        info_layout.addWidget(QLabel("总台数:"), 3, 2)
        quantity_spin = QSpinBox()
        quantity_spin.setRange(task['completed_quantity'], 9999)
        quantity_spin.setValue(task['total_quantity'])
        info_layout.addWidget(quantity_spin, 3, 3)

        info_layout.addWidget(QLabel("速度(%):"), 4, 0)
        speed_spin = QSpinBox()
        speed_spin.setRange(1, 100)
        speed_spin.setValue(task['speed'])
        info_layout.addWidget(speed_spin, 4, 1)

        info_layout.addWidget(QLabel("误差调整(mm):"), 4, 2)
        error_adjust_spin = QDoubleSpinBox()
        error_adjust_spin.setRange(-10, 10)
        error_adjust_spin.setDecimals(2)
        error_adjust_spin.setSingleStep(0.01)
        error_adjust_spin.setValue(task['error_adjust'])
        info_layout.addWidget(error_adjust_spin, 4, 3)

        # 不可修改的信息
        completed_qty_label = QLabel("已完成台数:")
        completed_qty_label.setStyleSheet(label_style)
        info_layout.addWidget(completed_qty_label, 5, 0)
        completed_qty_value = QLabel(str(task['completed_quantity']))
        completed_qty_value.setStyleSheet(value_style)
        info_layout.addWidget(completed_qty_value, 5, 1)

        remaining_qty_label = QLabel("剩余台数:")
        remaining_qty_label.setStyleSheet(label_style)
        info_layout.addWidget(remaining_qty_label, 5, 2)
        remaining_qty_value = QLabel(str(task['remaining_quantity']))
        remaining_qty_value.setStyleSheet(value_style)
        info_layout.addWidget(remaining_qty_value, 5, 3)

        status_info_label = QLabel("状态:")
        status_info_label.setStyleSheet(label_style)
        info_layout.addWidget(status_info_label, 6, 0)
        status_info_value = QLabel(task['status'])
        status_info_value.setStyleSheet(value_style)
        info_layout.addWidget(status_info_value, 6, 1)

        created_time_label = QLabel("创建时间:")
        created_time_label.setStyleSheet(label_style)
        info_layout.addWidget(created_time_label, 6, 2)
        created_time_value = QLabel(task['created_time'])
        created_time_value.setStyleSheet(value_style)
        info_layout.addWidget(created_time_value, 6, 3)

        updated_time_label = QLabel("最后更新:")
        updated_time_label.setStyleSheet(label_style)
        info_layout.addWidget(updated_time_label, 7, 0)
        updated_time_value = QLabel(task['last_updated'])
        updated_time_value.setStyleSheet(value_style)
        info_layout.addWidget(updated_time_value, 7, 1)

        info_group.setLayout(info_layout)
        layout.addWidget(info_group)

        # 添加做法信息
        process_group = QGroupBox("📝 做法")
        process_group.setStyleSheet("""
            QGroupBox {
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                margin-top: 10px;
                margin-bottom: 10px;
                background-color: white;
                font-size: 13px;
                font-weight: bold;
                color: #2c3e50;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 5px 10px;
                background-color: #f8f9fa;
                border-radius: 4px;
                border: 1px solid #e0e0e0;
            }
        """)
        process_layout = QVBoxLayout()
        process_layout.setContentsMargins(15, 15, 15, 15)

        process_text = QTextEdit()
        process_text.setReadOnly(True)
        process_text.setPlainText(task['process'])
        process_text.setStyleSheet("""
            QTextEdit {
                border: 1px solid #e0e0e0;
                border-radius: 4px;
                background-color: #f8f9fa;
                color: #2c3e50;
                font-size: 14px;
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                line-height: 1.4;
                padding: 10px;
            }
        """)
        process_text.setMinimumHeight(120)
        process_layout.addWidget(process_text)

        process_group.setLayout(process_layout)
        layout.addWidget(process_group)

        # 添加按钮布局
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)
        buttons_layout.addStretch()

        # 关闭按钮
        close_btn = QPushButton("❌ 关闭")
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 13px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
            QPushButton:pressed {
                background-color: #6c7a7c;
            }
        """)
        close_btn.clicked.connect(details_dialog.reject)
        buttons_layout.addWidget(close_btn)

        # 删除按钮
        delete_btn = QPushButton("🗑️ 删除")
        delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 13px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:pressed {
                background-color: #a93226;
            }
        """)
        delete_btn.clicked.connect(lambda: self.delete_task_from_dialog(row, details_dialog))
        buttons_layout.addWidget(delete_btn)

        # 修改按钮
        update_btn = QPushButton("✏️ 修改")
        update_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 13px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #1f618d;
            }
        """)
        update_btn.clicked.connect(lambda: self.update_task_from_dialog(
            row,
            length_spin.value(),
            increment_spin.value(),
            roots_spin.value(),
            speed_spin.value(),
            error_adjust_spin.value(),
            quantity_spin.value(),
            details_dialog
        ))
        buttons_layout.addWidget(update_btn)

        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)

        # 显示对话框
        details_dialog.exec_()

    def update_task_from_dialog(self, row, length, increment, roots, speed, error_adjust, quantity, dialog):
        """从对话框更新任务参数"""
        try:
            # 获取当前任务信息
            task = self.task_planner.get_task_by_index(row)
            if not task:
                return

            # 弹出确认对话框
            confirm_message = (
                f"确定要修改以下任务参数吗？\n\n"
                f"任务: {task['code']} - {task['contract']}\n\n"
                f"长度: {task['length']:.2f}mm → {length:.2f}mm\n"
                f"递增量: {task['increment']:.2f}mm → {increment:.2f}mm\n"
                f"根数: {task.get('roots', 0)} → {roots}\n"
                f"速度: {task['speed']}% → {speed}%\n"
                f"误差调整: {task['error_adjust']:.2f}mm → {error_adjust:.2f}mm\n"
                f"总台数: {task['total_quantity']} → {quantity}\n"
            )

            msgBox = QMessageBox(QMessageBox.Question, "确认修改", confirm_message,
                              QMessageBox.Yes | QMessageBox.No, dialog)
            msgBox.button(QMessageBox.Yes).setText("确认修改")
            msgBox.button(QMessageBox.No).setText("取消")
            msgBox.setDefaultButton(QMessageBox.No)
            reply = msgBox.exec_()

            if reply == QMessageBox.Yes:
                # 更新任务参数
                self.task_planner.update_task_parameters(
                    row, length, increment, roots, speed, error_adjust
                )

                # 更新任务数量
                self.task_planner.update_task_quantity(row, quantity)

                # 获取更新后的任务
                task = self.task_planner.get_task_by_index(row)

                # 关闭对话框
                dialog.accept()

                # 刷新任务列表
                self.refresh_tasks_table()

                # 更新状态
                self.status_label.setText(f"已更新任务: {task['code']} - {task['contract']}")
                QMessageBox.information(self, "成功", f"已更新任务: {task['code']} - {task['contract']}")

                # 如果当前选中的是这个任务，更新任务详情区域
                if self.current_task_index == row:
                    self.task_length_spin.setValue(task['length'])
                    self.task_increment_spin.setValue(task['increment'])
                    self.task_roots_spin.setValue(task.get('roots', 0))
                    self.task_speed_spin.setValue(task['speed'])
                    self.task_error_adjust_spin.setValue(task['error_adjust'])
                    self.task_quantity_spin.setValue(task['total_quantity'])
                    self.task_completed_label.setText(str(task['completed_quantity']))
                    self.task_remaining_label.setText(str(task['remaining_quantity']))
                    self.task_status_label.setText(task['status'])

                    # 更新进度条
                    if task['total_quantity'] > 0:
                        self.task_progress_bar.setMaximum(task['total_quantity'])
                        self.task_progress_bar.setValue(task['completed_quantity'])
                        progress = int((task['completed_quantity'] / task['total_quantity']) * 100)
                        self.task_progress_bar.setFormat(f"{task['completed_quantity']}/{task['total_quantity']} ({progress}%)")
                    else:
                        self.task_progress_bar.setMaximum(1)
                        self.task_progress_bar.setValue(0)
                        self.task_progress_bar.setFormat("0/0 (0%)")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"更新任务时发生错误: {str(e)}")
            print(f"更新任务错误: {str(e)}")

    def delete_task_from_dialog(self, row, dialog):
        """从对话框删除任务"""
        try:
            # 获取任务
            task = self.task_planner.get_task_by_index(row)
            if not task:
                return

            # 弹出确认对话框
            msgBox = QMessageBox(QMessageBox.Question, "确认", f"确定要删除任务 {task['code']} - {task['contract']} 吗？",
                              QMessageBox.Yes | QMessageBox.No, dialog)
            msgBox.button(QMessageBox.Yes).setText("是")
            msgBox.button(QMessageBox.No).setText("否")
            msgBox.setDefaultButton(QMessageBox.No)
            reply = msgBox.exec_()

            if reply == QMessageBox.Yes:
                # 删除任务
                if self.task_planner.delete_task(row):
                    # 关闭对话框
                    dialog.accept()

                    # 如果当前选中的是这个任务，清空任务详情显示
                    if self.current_task_index == row:
                        self.current_task_index = -1

                        # 清空任务详情显示
                        self.task_length_spin.setValue(0)
                        self.task_increment_spin.setValue(0)
                        self.task_speed_spin.setValue(80)
                        self.task_error_adjust_spin.setValue(0)
                        self.task_quantity_spin.setValue(1)
                        self.task_completed_label.setText("0")
                        self.task_remaining_label.setText("0")
                        self.task_status_label.setText("未开始")

                        # 重置进度条
                        self.task_progress_bar.setMaximum(1)
                        self.task_progress_bar.setValue(0)
                        self.task_progress_bar.setFormat("0/0 (0%)")

                    # 刷新任务列表
                    self.refresh_tasks_table()

                    self.status_label.setText(f"已删除任务: {task['code']} - {task['contract']}")
                    QMessageBox.information(self, "成功", f"已删除任务: {task['code']} - {task['contract']}")
                else:
                    self.status_label.setText("删除任务失败")
                    QMessageBox.warning(self, "警告", "删除任务失败")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"删除任务时发生错误: {str(e)}")
            print(f"删除任务错误: {str(e)}")

    def create_task_plan_ui(self, layout):
        """创建任务计划界面"""
        # 添加滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # 创建滚动内容部件
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        scroll_layout.setSpacing(15)
        scroll_layout.setContentsMargins(20, 20, 20, 20)

        # 设置滚动区域
        scroll_area.setWidget(scroll_content)
        layout.addWidget(scroll_area)

        # 统一的按钮样式
        button_style = """
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: bold;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
                color: #7f8c8d;
            }
        """

        # 统一的输入框样式
        input_style = """
            QDoubleSpinBox, QSpinBox {
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 13px;
                background-color: white;
                min-height: 20px;
            }
            QDoubleSpinBox:focus, QSpinBox:focus {
                border-color: #27ae60;
                background-color: #f8fff8;
            }
            QDoubleSpinBox::up-button, QSpinBox::up-button,
            QDoubleSpinBox::down-button, QSpinBox::down-button {
                width: 20px;
                border: none;
                background-color: #f0f0f0;
            }
            QDoubleSpinBox::up-button:hover, QSpinBox::up-button:hover,
            QDoubleSpinBox::down-button:hover, QSpinBox::down-button:hover {
                background-color: #27ae60;
            }
        """

        # 紧凑的输入框样式
        compact_input_style = """
            QDoubleSpinBox, QSpinBox {
                border: 1px solid #e0e0e0;
                border-radius: 4px;
                padding: 4px 8px;
                font-size: 14px;
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                background-color: white;
                min-height: 16px;
                max-height: 28px;
                color: #2c3e50;
            }
            QDoubleSpinBox:focus, QSpinBox:focus {
                border-color: #27ae60;
                background-color: #f8fff8;
            }
            QDoubleSpinBox::up-button, QSpinBox::up-button,
            QDoubleSpinBox::down-button, QSpinBox::down-button {
                width: 16px;
                border: none;
                background-color: #f0f0f0;
            }
            QDoubleSpinBox::up-button:hover, QSpinBox::up-button:hover,
            QDoubleSpinBox::down-button:hover, QSpinBox::down-button:hover {
                background-color: #27ae60;
            }
        """

        # 创建任务列表区域
        tasks_group = QGroupBox("📋 任务计划列表")
        tasks_group.setStyleSheet("""
            QGroupBox {
                border: 2px solid #e8f4fd;
                border-radius: 8px;
                margin-top: 10px;
                margin-bottom: 10px;
                background-color: #f8fcff;
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 5px 10px;
                background-color: white;
                border-radius: 4px;
            }
        """)
        tasks_layout = QVBoxLayout()
        tasks_layout.setSpacing(15)
        tasks_layout.setContentsMargins(20, 20, 20, 15)

        # 任务表格
        self.tasks_table = QTableWidget()
        self.tasks_table.setStyleSheet("""
            QTableWidget {
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                background-color: white;
                gridline-color: #f0f0f0;
                font-size: 13px;
                selection-background-color: #e8f4fd;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 10px 8px;
                border-bottom: 1px solid #f0f0f0;
                border-right: 1px solid #f8f9fa;
            }
            QTableWidget::item:selected {
                background-color: #e8f4fd;
                color: #2c3e50;
            }
            QTableWidget::item:hover {
                background-color: #f1f8ff;
            }
            QHeaderView::section {
                background-color: #f8f9fa;
                border: 1px solid #e0e0e0;
                padding: 12px 8px;
                font-weight: bold;
                color: #2c3e50;
                font-size: 13px;
            }
            QHeaderView::section:hover {
                background-color: #e9ecef;
            }
        """)
        self.tasks_table.setColumnCount(12)  # 恢复原来的列数
        self.tasks_table.setHorizontalHeaderLabels(["代码", "型号", "合同号", "铜带规格", "长度(mm)", "递增量(mm)", "根数", "总台数", "已完成", "剩余", "状态", "操作"])

        # 设置列的调整模式：除了"状态"和"操作"列外，其他列可以调整大小
        header = self.tasks_table.horizontalHeader()

        # 设置默认列宽
        default_widths = {
            0: 90,   # 代码
            1: 110,  # 型号
            2: 120,  # 合同号
            3: 70,   # 铜带规格
            4: 80,   # 长度
            5: 80,   # 递增量
            6: 60,   # 根数
            7: 60,   # 总台数
            8: 60,   # 已完成
            9: 60,   # 剩余
            10: 80,  # 状态
            11: 100  # 操作（增大以适应更大的按钮）
        }

        for i in range(12):
            if i == 10:  # "状态"列
                header.setSectionResizeMode(i, QHeaderView.Fixed)
                self.tasks_table.setColumnWidth(i, default_widths[i])
            elif i == 11:  # "操作"列
                header.setSectionResizeMode(i, QHeaderView.Fixed)
                self.tasks_table.setColumnWidth(i, default_widths[i])
            else:
                header.setSectionResizeMode(i, QHeaderView.Interactive)  # 可以手动调整大小
                self.tasks_table.setColumnWidth(i, default_widths[i])  # 设置初始宽度

        self.tasks_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.tasks_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.tasks_table.setAlternatingRowColors(True)  # 启用斑马纹效果

        # 设置垂直表头（行号）样式
        vertical_header = self.tasks_table.verticalHeader()
        vertical_header.setVisible(True)  # 确保行号可见
        vertical_header.setDefaultSectionSize(45)  # 增加行高以适应按钮
        vertical_header.setStyleSheet("""
            QHeaderView::section {
                background-color: #f8f9fa;
                border: 1px solid #e0e0e0;
                padding: 4px;
                font-weight: bold;
                color: #6c757d;
                font-size: 12px;
                text-align: center;
            }
        """)

        self.tasks_table.itemClicked.connect(self.on_task_selected)
        self.tasks_table.itemDoubleClicked.connect(self.show_task_details)  # 添加双击事件处理
        tasks_layout.addWidget(self.tasks_table)

        tasks_group.setLayout(tasks_layout)
        scroll_layout.addWidget(tasks_group)

        # 创建任务详情区域（紧凑版）
        details_group = QGroupBox("🔧 任务详情")
        details_group.setStyleSheet("""
            QGroupBox {
                border: 2px solid #e8f6f3;
                border-radius: 8px;
                margin-top: 10px;
                margin-bottom: 5px;
                background-color: #f0fdf4;
                font-size: 13px;
                font-weight: bold;
                color: #2c3e50;
                max-height: 200px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 3px 8px;
                background-color: white;
                border-radius: 4px;
                font-size: 12px;
            }
        """)
        details_layout = QGridLayout()
        details_layout.setSpacing(8)  # 减少间距
        details_layout.setContentsMargins(15, 10, 15, 10)  # 减少边距

        # 标签样式（与任务计划表格字体大小一致）
        compact_label_style = "font-weight: bold; color: #2c3e50; font-size: 13px;"

        # 第一行：长度、递增量、根数、速度
        length_label = QLabel("长度(mm)")
        length_label.setStyleSheet(compact_label_style)
        details_layout.addWidget(length_label, 0, 0)
        self.task_length_spin = QDoubleSpinBox()
        self.task_length_spin.setStyleSheet(compact_input_style)
        self.task_length_spin.setRange(0, 99999.99)
        self.task_length_spin.setDecimals(2)
        self.task_length_spin.setSingleStep(0.01)
        self.task_length_spin.setValue(0.00)
        details_layout.addWidget(self.task_length_spin, 0, 1)

        increment_label = QLabel("递增量(mm)")
        increment_label.setStyleSheet(compact_label_style)
        details_layout.addWidget(increment_label, 0, 2)
        self.task_increment_spin = QDoubleSpinBox()
        self.task_increment_spin.setStyleSheet(compact_input_style)
        self.task_increment_spin.setRange(-100, 100)
        self.task_increment_spin.setDecimals(2)
        self.task_increment_spin.setSingleStep(0.01)
        self.task_increment_spin.setValue(0.00)
        details_layout.addWidget(self.task_increment_spin, 0, 3)

        roots_label = QLabel("根数")
        roots_label.setStyleSheet(compact_label_style)
        details_layout.addWidget(roots_label, 0, 4)
        self.task_roots_spin = QSpinBox()
        self.task_roots_spin.setStyleSheet(compact_input_style)
        self.task_roots_spin.setRange(0, 100)
        self.task_roots_spin.setValue(0)
        details_layout.addWidget(self.task_roots_spin, 0, 5)

        # 第二行：速度、误差、总台数、状态
        speed_label = QLabel("速度(%)")
        speed_label.setStyleSheet(compact_label_style)
        details_layout.addWidget(speed_label, 1, 0)
        self.task_speed_spin = QSpinBox()
        self.task_speed_spin.setStyleSheet(compact_input_style)
        self.task_speed_spin.setRange(1, 100)
        self.task_speed_spin.setValue(80)
        details_layout.addWidget(self.task_speed_spin, 1, 1)

        error_adjust_label = QLabel("误差(mm)")
        error_adjust_label.setStyleSheet(compact_label_style)
        details_layout.addWidget(error_adjust_label, 1, 2)
        self.task_error_adjust_spin = QDoubleSpinBox()
        self.task_error_adjust_spin.setStyleSheet(compact_input_style)
        self.task_error_adjust_spin.setRange(-10, 10)
        self.task_error_adjust_spin.setDecimals(2)
        self.task_error_adjust_spin.setSingleStep(0.01)
        self.task_error_adjust_spin.setValue(0.00)
        details_layout.addWidget(self.task_error_adjust_spin, 1, 3)

        quantity_label = QLabel("总台数")
        quantity_label.setStyleSheet(compact_label_style)
        details_layout.addWidget(quantity_label, 1, 4)
        self.task_quantity_spin = QSpinBox()
        self.task_quantity_spin.setStyleSheet(compact_input_style)
        self.task_quantity_spin.setRange(1, 9999)
        self.task_quantity_spin.setValue(1)
        details_layout.addWidget(self.task_quantity_spin, 1, 5)

        # 第三行：已完成、剩余、状态、进度条
        completed_label = QLabel("已完成")
        completed_label.setStyleSheet(compact_label_style)
        details_layout.addWidget(completed_label, 2, 0)
        self.task_completed_label = QLabel("0")
        self.task_completed_label.setStyleSheet("color: #27ae60; font-weight: bold; font-size: 13px;")
        details_layout.addWidget(self.task_completed_label, 2, 1)

        remaining_label = QLabel("剩余")
        remaining_label.setStyleSheet(compact_label_style)
        details_layout.addWidget(remaining_label, 2, 2)
        self.task_remaining_label = QLabel("0")
        self.task_remaining_label.setStyleSheet("color: #e74c3c; font-weight: bold; font-size: 13px;")
        details_layout.addWidget(self.task_remaining_label, 2, 3)

        status_label = QLabel("状态")
        status_label.setStyleSheet(compact_label_style)
        details_layout.addWidget(status_label, 2, 4)
        self.task_status_label = QLabel("未开始")
        self.task_status_label.setStyleSheet("color: #f39c12; font-weight: bold; font-size: 13px;")
        details_layout.addWidget(self.task_status_label, 2, 5)

        # 第四行：进度条（跨列）
        progress_label = QLabel("进度")
        progress_label.setStyleSheet(compact_label_style)
        details_layout.addWidget(progress_label, 3, 0)
        self.task_progress_bar = QProgressBar()
        self.task_progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #e0e0e0;
                border-radius: 4px;
                text-align: center;
                font-weight: bold;
                background-color: white;
                min-height: 18px;
                max-height: 22px;
                font-size: 13px;
            }
            QProgressBar::chunk {
                background-color: #27ae60;
                border-radius: 3px;
            }
        """)
        self.task_progress_bar.setRange(0, 100)
        self.task_progress_bar.setValue(0)
        self.task_progress_bar.setTextVisible(True)
        self.task_progress_bar.setFormat("%v/%m (%p%)")
        details_layout.addWidget(self.task_progress_bar, 3, 1, 1, 5)

        # 紧凑的控制按钮区域
        control_layout = QHBoxLayout()
        control_layout.setSpacing(8)
        control_layout.addStretch()

        # 按钮样式（与任务计划表格字体大小一致）
        compact_button_style = """
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 14px;
                font-size: 13px;
                font-weight: bold;
                min-width: 70px;
                max-height: 28px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
                color: #7f8c8d;
            }
        """

        # 启动按钮
        self.task_start_btn = QPushButton("▶️ 启动")
        self.task_start_btn.setStyleSheet(compact_button_style)
        self.task_start_btn.clicked.connect(self.start_cutting)
        self.task_start_btn.setEnabled(False)
        control_layout.addWidget(self.task_start_btn)

        # 停止按钮
        self.task_stop_btn = QPushButton("⏹️ 停止")
        self.task_stop_btn.setStyleSheet(compact_button_style.replace("#27ae60", "#e74c3c").replace("#229954", "#c0392b").replace("#1e8449", "#a93226"))
        self.task_stop_btn.clicked.connect(self.stop_cutting)
        self.task_stop_btn.setEnabled(False)
        control_layout.addWidget(self.task_stop_btn)

        # 复位按钮
        self.task_reset_btn = QPushButton("🔄 复位")
        self.task_reset_btn.setStyleSheet(compact_button_style.replace("#27ae60", "#f39c12").replace("#229954", "#d35400").replace("#1e8449", "#ba4a00"))
        self.task_reset_btn.clicked.connect(self.reset_machine)
        self.task_reset_btn.setEnabled(False)
        control_layout.addWidget(self.task_reset_btn)

        control_layout.addStretch()

        details_layout.addLayout(control_layout, 4, 0, 1, 6)

        details_group.setLayout(details_layout)
        scroll_layout.addWidget(details_group)

        # 创建固定的操作按钮区域（不在滚动区域内）

        # 创建固定的操作按钮区域
        fixed_buttons_widget = QWidget()
        fixed_buttons_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                border-top: 2px solid #e0e0e0;
                border-radius: 0px;
                min-height: 55px;
                max-height: 55px;
            }
        """)
        fixed_buttons_layout = QHBoxLayout(fixed_buttons_widget)
        fixed_buttons_layout.setSpacing(12)
        fixed_buttons_layout.setContentsMargins(15, 12, 15, 12)

        # 左侧信息标签
        info_label = QLabel("任务操作:")
        info_label.setStyleSheet("""
            QLabel {
                font-size: 13px;
                font-weight: bold;
                color: #2c3e50;
                padding: 8px 0px;
            }
        """)
        fixed_buttons_layout.addWidget(info_label)

        fixed_buttons_layout.addStretch()

        # 紧凑的操作按钮样式
        operation_button_style = """
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 6px 12px;
                font-size: 12px;
                font-weight: bold;
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                text-align: center;
                min-width: 80px;
                max-height: 30px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
                color: #7f8c8d;
            }
        """

        # 更新任务按钮
        update_task_btn = QPushButton("🔄 更新任务")
        update_task_btn.setStyleSheet(operation_button_style.replace("#27ae60", "#3498db").replace("#229954", "#2980b9").replace("#1e8449", "#1f618d"))
        update_task_btn.setToolTip("更新选中任务的参数")
        update_task_btn.clicked.connect(self.update_task)
        fixed_buttons_layout.addWidget(update_task_btn)

        # 发送参数按钮
        send_params_btn = QPushButton("📤 发送参数")
        send_params_btn.setStyleSheet(operation_button_style.replace("#27ae60", "#e74c3c").replace("#229954", "#c0392b").replace("#1e8449", "#a93226"))
        send_params_btn.setToolTip("将任务参数发送到剪切机")
        send_params_btn.clicked.connect(self.send_task_params)
        fixed_buttons_layout.addWidget(send_params_btn)

        # 更新完成数量按钮
        update_completed_btn = QPushButton("📊 更新数量")
        update_completed_btn.setStyleSheet(operation_button_style)
        update_completed_btn.setToolTip("更新任务完成数量")
        update_completed_btn.clicked.connect(self.update_completed_quantity)
        fixed_buttons_layout.addWidget(update_completed_btn)

        # 删除任务按钮
        delete_task_btn = QPushButton("🗑️ 删除任务")
        delete_task_btn.setStyleSheet(operation_button_style.replace("#27ae60", "#95a5a6").replace("#229954", "#7f8c8d").replace("#1e8449", "#6c7a7c"))
        delete_task_btn.setToolTip("删除选中的任务")
        delete_task_btn.clicked.connect(self.delete_task)
        fixed_buttons_layout.addWidget(delete_task_btn)

        fixed_buttons_layout.addStretch()

        # 将固定按钮区域添加到主布局（不是滚动布局）
        layout.addWidget(fixed_buttons_widget)

        # 初始化任务列表
        self.refresh_tasks_table()

    def load_config(self):
        """从文件加载配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    config = json.load(f)

                # 加载协议类型
                if 'protocol_type' in config:
                    self.protocol_type = config['protocol_type']

                # 加载RTU配置
                if 'port' in config:
                    self.modbus.port = config['port']
                if 'baudrate' in config:
                    self.modbus.baudrate = config['baudrate']
                if 'bytesize' in config:
                    self.modbus.bytesize = config['bytesize']
                if 'parity' in config:
                    self.modbus.parity = config['parity']
                if 'stopbits' in config:
                    self.modbus.stopbits = config['stopbits']
                if 'timeout' in config:
                    self.modbus.timeout = config['timeout']

                # 加载TCP配置
                if 'tcp_host' in config:
                    self.tcp_host = config['tcp_host']
                    self.modbus_tcp.host = self.tcp_host
                if 'tcp_port' in config:
                    self.tcp_port = config['tcp_port']
                    self.modbus_tcp.port = self.tcp_port
                if 'tcp_timeout' in config:
                    self.modbus_tcp.timeout = config['tcp_timeout']
                elif 'timeout' in config:
                    self.modbus_tcp.timeout = config['timeout']

                # 加载寄存器映射
                if 'register_map' in config:
                    self.register_map = config['register_map']

                # 加载位地址映射
                if 'bit_map' in config:
                    self.bit_map = config['bit_map']

                # 加载数据类型配置
                if 'data_types' in config:
                    self.data_types = config['data_types']

                # 加载红外感应功能开关状态
                if 'infrared_enabled' in config:
                    self.infrared_enabled = config['infrared_enabled']

                # 更新主界面的端口信息显示（在初始化UI后调用）
                QTimer.singleShot(100, self.update_port_info_display)

                # 更新红外感应功能按钮状态（在初始化UI后调用）
                QTimer.singleShot(200, self.update_infrared_button_state)
        except Exception as e:
            print(f"加载配置错误: {e}")

    def update_port_info_display(self):
        """更新端口信息显示"""
        # 更新主界面的端口信息显示
        if hasattr(self, 'port_info_label'):
            if self.protocol_type == "TCP":
                tcp_host = getattr(self, 'tcp_host', '*************')
                tcp_port = getattr(self, 'tcp_port', 502)
                self.port_info_label.setText(f"TCP: {tcp_host}:{tcp_port}")
            else:
                if self.modbus.port:
                    self.port_info_label.setText(f"端口: {self.modbus.port}")
                else:
                    self.port_info_label.setText("端口: 未配置")

            # 更新主界面的波特率信息显示
            if hasattr(self, 'baudrate_info_label'):
                if self.protocol_type == "TCP":
                    self.baudrate_info_label.setText("协议: Modbus TCP")
                else:
                    self.baudrate_info_label.setText(f"波特率: {self.modbus.baudrate}")

# 排产单管理相关方法
    def browse_order_file(self):
        """浏览排产单文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择排产单文件", "",
            "Excel文件 (*.xlsx *.xls);;CSV文件 (*.csv);;所有文件 (*)"
        )
        if file_path:
            self.order_file_path.setText(file_path)
            self.status_label.setText(f"已选择文件: {os.path.basename(file_path)}")

    def import_order_file(self):
        """导入排产单文件"""
        file_path = self.order_file_path.text()
        if not file_path:
            QMessageBox.warning(self, "警告", "请先选择排产单文件")
            return

        # 显示导入中提示
        self.status_label.setText("正在导入排产单...")
        QApplication.processEvents()  # 确保UI更新

        # 导入排产单
        success, message = self.production_order.import_from_file(file_path)

        if success:
            self.status_label.setText(message)
            self.refresh_orders_table()
            QMessageBox.information(self, "成功", message)
        else:
            self.status_label.setText(f"导入失败: {message}")
            QMessageBox.critical(self, "错误", f"导入失败: {message}")

    def refresh_orders_table(self):
        """刷新排产单表格"""
        self.orders_table.setRowCount(0)

        orders = self.production_order.get_orders()
        if not orders:
            return

        # 打印调试信息
        print(f"排产单数量: {len(orders)}")
        if orders:
            print(f"第一条排产单数据: {orders[0]}")
            print(f"排产单表格列数: {self.orders_table.columnCount()}")

        for i, order in enumerate(orders):
            self.orders_table.insertRow(i)

            # 添加复选框
            checkbox = QCheckBox()
            checkbox_widget = QWidget()
            checkbox_layout = QHBoxLayout(checkbox_widget)
            checkbox_layout.addWidget(checkbox)
            checkbox_layout.setAlignment(Qt.AlignCenter)
            checkbox_layout.setContentsMargins(0, 0, 0, 0)
            self.orders_table.setCellWidget(i, 0, checkbox_widget)

            # 设置单元格内容
            self.orders_table.setItem(i, 1, QTableWidgetItem(order['code']))
            self.orders_table.setItem(i, 2, QTableWidgetItem(order.get('model', '')))  # 型号列
            self.orders_table.setItem(i, 3, QTableWidgetItem(order['contract']))
            self.orders_table.setItem(i, 4, QTableWidgetItem(order['process']))
            self.orders_table.setItem(i, 5, QTableWidgetItem(str(order['quantity'])))
            self.orders_table.setItem(i, 6, QTableWidgetItem(str(order['remaining'])))

            # 创建操作按钮
            create_btn = QPushButton("创建任务")
            create_btn.setStyleSheet("background-color: #3498db; color: white;")
            create_btn.clicked.connect(lambda _, row=i: self.create_task_from_order_row(row))

            # 将按钮添加到单元格
            self.orders_table.setCellWidget(i, 7, create_btn)

    def create_task_from_order_row(self, row):
        """从排产单行创建任务"""
        if row < 0 or row >= len(self.production_order.get_orders()):
            return

        order = self.production_order.get_order_by_index(row)
        if not order:
            return

        # 创建任务
        task_index = self.task_planner.create_task_from_order(order)
        if task_index >= 0:
            self.status_label.setText(f"已创建任务: {order['code']} - {order['contract']}")
            self.refresh_tasks_table()
            QMessageBox.information(self, "成功", f"已创建任务: {order['code']} - {order['contract']}")
        else:
            self.status_label.setText("创建任务失败")
            QMessageBox.warning(self, "警告", "创建任务失败")

    def create_task_from_order(self):
        """从选中的排产单创建任务"""
        selected_rows = self.orders_table.selectionModel().selectedRows()
        if not selected_rows:
            QMessageBox.warning(self, "警告", "请先选择排产单")
            return

        row = selected_rows[0].row()
        self.create_task_from_order_row(row)

    def toggle_select_all(self):
        """全选/取消全选"""
        checked = self.select_all_checkbox.isChecked()

        # 遍历所有行，设置复选框状态
        for row in range(self.orders_table.rowCount()):
            checkbox_widget = self.orders_table.cellWidget(row, 0)
            if checkbox_widget:
                checkbox = checkbox_widget.findChild(QCheckBox)
                if checkbox:
                    checkbox.setChecked(checked)

        if checked:
            self.status_label.setText("已全选所有排产单")
        else:
            self.status_label.setText("已取消全选")

    def create_selected_tasks(self):
        """创建选中的任务"""
        selected_rows = []

        # 遍历所有行，查找选中的复选框
        for row in range(self.orders_table.rowCount()):
            checkbox_widget = self.orders_table.cellWidget(row, 0)
            if checkbox_widget:
                checkbox = checkbox_widget.findChild(QCheckBox)
                if checkbox and checkbox.isChecked():
                    selected_rows.append(row)

        if not selected_rows:
            QMessageBox.warning(self, "警告", "请先选择排产单")
            return

        # 创建选中的任务
        created_count = 0
        for row in selected_rows:
            order = self.production_order.get_order_by_index(row)
            if order:
                task_index = self.task_planner.create_task_from_order(order)
                if task_index >= 0:
                    created_count += 1

        # 刷新任务列表
        self.refresh_tasks_table()

        if created_count > 0:
            self.status_label.setText(f"已创建 {created_count} 个任务")
            QMessageBox.information(self, "成功", f"已创建 {created_count} 个任务")
        else:
            self.status_label.setText("没有创建任何任务")
            QMessageBox.warning(self, "警告", "没有创建任何任务")

    def create_all_tasks(self):
        """创建所有任务"""
        orders = self.production_order.get_orders()
        if not orders:
            QMessageBox.warning(self, "警告", "没有可用的排产单")
            return

        # 确认对话框
        reply = QMessageBox.question(
            self, "确认", f"确定要创建所有 {len(orders)} 个排产单的任务吗？",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        # 创建所有任务
        created_count = 0
        for order in orders:
            task_index = self.task_planner.create_task_from_order(order)
            if task_index >= 0:
                created_count += 1

        # 刷新任务列表
        self.refresh_tasks_table()

        if created_count > 0:
            self.status_label.setText(f"已创建 {created_count} 个任务")
            QMessageBox.information(self, "成功", f"已创建 {created_count} 个任务")
        else:
            self.status_label.setText("没有创建任何任务")
            QMessageBox.warning(self, "警告", "没有创建任何任务")

    def adjust_order_quantity(self):
        """调整排产单台数"""
        selected_rows = self.orders_table.selectionModel().selectedRows()
        if not selected_rows:
            QMessageBox.warning(self, "警告", "请先选择排产单")
            return

        row = selected_rows[0].row()
        order = self.production_order.get_order_by_index(row)
        if not order:
            return

        # 弹出输入对话框
        dialog = QInputDialog(self)
        dialog.setWindowTitle("调整台数")
        dialog.setLabelText("请输入新的台数:")
        dialog.setIntRange(1, 9999)
        dialog.setIntValue(order['quantity'])
        dialog.setIntStep(1)
        dialog.setOkButtonText("确定")
        dialog.setCancelButtonText("取消")
        if dialog.exec_():
            new_quantity = dialog.intValue()
            ok = True
        else:
            ok = False

        if ok:
            # 更新台数
            if self.production_order.update_quantity(row, new_quantity):
                self.status_label.setText(f"已调整台数: {order['code']} - {new_quantity}台")
                self.refresh_orders_table()
            else:
                self.status_label.setText("调整台数失败")
                QMessageBox.warning(self, "警告", "调整台数失败")

    # 任务计划相关方法
    def refresh_tasks_table(self):
        """刷新任务计划表格"""
        self.tasks_table.setRowCount(0)

        tasks = self.task_planner.get_all_tasks()
        if not tasks:
            return

        for i, task in enumerate(tasks):
            self.tasks_table.insertRow(i)

            # 设置单元格内容
            self.tasks_table.setItem(i, 0, QTableWidgetItem(task['code']))
            self.tasks_table.setItem(i, 1, QTableWidgetItem(task.get('model', '')))  # 型号列
            self.tasks_table.setItem(i, 2, QTableWidgetItem(task['contract']))
            self.tasks_table.setItem(i, 3, QTableWidgetItem(task.get('copper_spec', '')))  # 铜带规格列
            self.tasks_table.setItem(i, 4, QTableWidgetItem(f"{task['length']:.2f}"))
            self.tasks_table.setItem(i, 5, QTableWidgetItem(f"{task['increment']:.2f}"))
            self.tasks_table.setItem(i, 6, QTableWidgetItem(str(task.get('roots', 0))))  # 根数列
            self.tasks_table.setItem(i, 7, QTableWidgetItem(str(task['total_quantity'])))
            self.tasks_table.setItem(i, 8, QTableWidgetItem(str(task['completed_quantity'])))
            self.tasks_table.setItem(i, 9, QTableWidgetItem(str(task['remaining_quantity'])))
            self.tasks_table.setItem(i, 10, QTableWidgetItem(task['status']))

            # 设置状态单元格颜色和文字颜色
            status_item = self.tasks_table.item(i, 10)  # 状态列索引为10
            status_item.setTextAlignment(Qt.AlignCenter)
            if task['status'] == '未开始':
                status_item.setBackground(QColor(255, 243, 205))  # 浅橙色背景
                status_item.setForeground(QColor(230, 126, 34))   # 橙色文字
            elif task['status'] == '进行中':
                status_item.setBackground(QColor(212, 237, 218))  # 浅绿色背景
                status_item.setForeground(QColor(40, 167, 69))    # 绿色文字
            elif task['status'] == '已完成':
                status_item.setBackground(QColor(233, 221, 246))  # 浅紫色背景
                status_item.setForeground(QColor(142, 68, 173))   # 紫色文字

            # 添加生产按钮
            produce_btn = QPushButton("🚀 生产")
            produce_btn.setStyleSheet("""
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 6px 8px;
                    font-size: 12px;
                    font-weight: bold;
                    font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                    text-align: center;
                    margin: 2px;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                }
                QPushButton:pressed {
                    background-color: #a93226;
                }
                QPushButton:disabled {
                    background-color: #bdc3c7;
                    color: #7f8c8d;
                }
            """)

            # 设置按钮的最小和最大尺寸
            produce_btn.setMinimumHeight(35)
            produce_btn.setMaximumHeight(41)

            # 如果任务已完成，禁用生产按钮
            if task['status'] == '已完成' or task['remaining_quantity'] <= 0:
                produce_btn.setEnabled(False)

            produce_btn.clicked.connect(lambda _, row=i: self.produce_task(row))

            # 直接将按钮设置到表格单元格中
            self.tasks_table.setCellWidget(i, 11, produce_btn)

        # 设置正确的行号（从1开始）
        for i in range(self.tasks_table.rowCount()):
            self.tasks_table.setVerticalHeaderItem(i, QTableWidgetItem(str(i + 1)))

    def on_task_selected(self, item):
        """任务选中事件处理"""
        row = item.row()
        task = self.task_planner.get_task_by_index(row)
        if not task:
            return

        # 更新当前选中的任务索引
        self.current_task_index = row

        # 更新任务详情区域
        self.task_length_spin.setValue(task['length'])
        self.task_increment_spin.setValue(task['increment'])
        self.task_roots_spin.setValue(task.get('roots', 0))  # 显示根数
        self.task_speed_spin.setValue(task['speed'])
        self.task_error_adjust_spin.setValue(task['error_adjust'])
        self.task_quantity_spin.setValue(task['total_quantity'])
        self.task_completed_label.setText(str(task['completed_quantity']))
        self.task_remaining_label.setText(str(task['remaining_quantity']))
        self.task_status_label.setText(task['status'])

        # 更新进度条
        if task['total_quantity'] > 0:
            self.task_progress_bar.setMaximum(task['total_quantity'])
            self.task_progress_bar.setValue(task['completed_quantity'])
            progress = int((task['completed_quantity'] / task['total_quantity']) * 100)
            self.task_progress_bar.setFormat(f"{task['completed_quantity']}/{task['total_quantity']} ({progress}%)")
        else:
            self.task_progress_bar.setMaximum(1)
            self.task_progress_bar.setValue(0)
            self.task_progress_bar.setFormat("0/0 (0%)")

        self.status_label.setText(f"已选择任务: {task['code']} - {task['contract']}")

    def update_task(self):
        """更新任务参数"""
        if self.current_task_index < 0:
            QMessageBox.warning(self, "警告", "请先选择任务")
            return

        # 获取参数值
        length = self.task_length_spin.value()
        increment = self.task_increment_spin.value()
        roots = self.task_roots_spin.value()  # 获取根数
        speed = self.task_speed_spin.value()
        error_adjust = self.task_error_adjust_spin.value()
        quantity = self.task_quantity_spin.value()

        # 更新任务参数
        self.task_planner.update_task_parameters(
            self.current_task_index, length, increment, roots, speed, error_adjust
        )

        # 更新任务数量
        self.task_planner.update_task_quantity(self.current_task_index, quantity)

        # 刷新任务列表
        self.refresh_tasks_table()

        # 更新状态
        task = self.task_planner.get_task_by_index(self.current_task_index)
        self.status_label.setText(f"已更新任务: {task['code']} - {task['contract']}")
        QMessageBox.information(self, "成功", f"已更新任务: {task['code']} - {task['contract']}")

    def send_task_params(self):
        """发送任务参数到剪切机"""
        if self.current_task_index < 0:
            QMessageBox.warning(self, "警告", "请先选择任务")
            return

        if not self.modbus.is_connected():
            QMessageBox.warning(self, "警告", "请先连接剪切机")
            return

        # 获取任务
        task = self.task_planner.get_task_by_index(self.current_task_index)
        if not task:
            return

        # 检查剩余数量
        if task['remaining_quantity'] <= 0:
            QMessageBox.warning(self, "警告", "该任务已完成，无法继续生产")
            return

        # 获取从站地址
        slave_addr = self.slave_addr_spin.value()

        # 弹出对话框让用户输入想要生产的台数，默认值为剩余台数
        dialog = QInputDialog(self)
        dialog.setWindowTitle("输入生产台数")
        dialog.setLabelText("请输入要生产的台数:")
        dialog.setIntRange(1, task['remaining_quantity'])
        dialog.setIntValue(task['remaining_quantity'])
        dialog.setIntStep(1)
        dialog.setOkButtonText("确定")
        dialog.setCancelButtonText("取消")
        if dialog.exec_():
            quantity_to_produce = dialog.intValue()
            ok = True
        else:
            ok = False

        # 如果用户取消了输入，则退出
        if not ok:
            return

        try:
            # 转换参数为寄存器值并进行有效性验证
            # 长度参数验证 (0.01mm单位，支持大长度值)
            length_value = float(task['length'])
            if length_value < 0:
                QMessageBox.warning(self, "警告", f"剪切长度不能为负值: {length_value}mm")
                return
            # 支持大长度值，如15米 = 1500000个0.01mm单位
            # 不再限制最大长度，由write_large_value方法处理大数值
            length = int(length_value * 100)  # 转换为整数，单位0.01mm

            # 递增量参数验证 (-327.68mm到327.67mm)
            increment_value = float(task['increment'])
            if increment_value < -327.68 or increment_value > 327.67:
                QMessageBox.warning(self, "警告", f"递增量超出有效范围: {increment_value}mm，有效范围为-327.68mm到327.67mm")
                return
            increment = int(increment_value * 100)  # 转换为整数，单位0.01mm

            # 速度参数验证 (1-100)
            speed_value = int(task['speed'])
            if speed_value < 1 or speed_value > 100:
                QMessageBox.warning(self, "警告", f"伺服速度超出有效范围: {speed_value}，有效范围为1-100")
                return
            speed = speed_value

            # 误差调整参数验证 (-327.68mm到327.67mm)
            error_adjust_value = float(task['error_adjust'])
            if error_adjust_value < -327.68 or error_adjust_value > 327.67:
                QMessageBox.warning(self, "警告", f"误差调整超出有效范围: {error_adjust_value}mm，有效范围为-327.68mm到327.67mm")
                return
            error_adjust = int(error_adjust_value * 100)  # 转换为整数，单位0.01mm

            # 根数参数验证 (1-100)
            roots_value = int(task.get('roots', 1))
            if roots_value < 1 or roots_value > 100:
                QMessageBox.warning(self, "警告", f"根数超出有效范围: {roots_value}，有效范围为1-100")
                return
            roots = roots_value

            # 台数参数验证 (1-9999)
            machines_value = min(int(quantity_to_produce), 9999)
            if machines_value < 1:
                QMessageBox.warning(self, "警告", "生产台数必须大于0")
                return
            machines = machines_value

        except (ValueError, TypeError) as e:
            self.logger.error(f"参数转换错误: {e}")
            QMessageBox.warning(self, "警告", f"参数格式错误: {e}")
            return

        # 写入参数到对应寄存器
        success = True
        success &= self.modbus.write_register(slave_addr, self.register_map['length'], length)
        success &= self.modbus.write_register(slave_addr, self.register_map['increment'], increment)
        success &= self.modbus.write_register(slave_addr, self.register_map['speed'], speed)
        success &= self.modbus.write_register(slave_addr, self.register_map['error_adjust'], error_adjust)
        success &= self.modbus.write_register(slave_addr, self.register_map['roots'], roots)
        success &= self.modbus.write_register(slave_addr, self.register_map['machines'], machines)

        if success:
            # 更新主界面参数显示
            self.length_spin.setValue(task['length'])
            self.increment_spin.setValue(task['increment'])
            self.speed_spin.setValue(task['speed'])
            self.error_adjust_spin.setValue(task['error_adjust'])
            self.roots_spin.setValue(roots)
            self.machines_spin.setValue(machines)

            self.status_label.setText(f"已发送任务参数: {task['code']} - {task['contract']} (生产台数: {machines})")
            QMessageBox.information(self, "成功", f"已发送任务参数: {task['code']} - {task['contract']} (生产台数: {machines})")
        else:
            self.status_label.setText("发送参数失败")
            QMessageBox.warning(self, "警告", "发送参数失败")

    def update_completed_quantity(self):
        """更新已完成数量"""
        if self.current_task_index < 0:
            QMessageBox.warning(self, "警告", "请先选择任务")
            return

        # 获取任务
        task = self.task_planner.get_task_by_index(self.current_task_index)
        if not task:
            return

        # 从剪切机读取已完成数量
        if self.modbus.is_connected():
            slave_addr = self.slave_addr_spin.value()
            count_data = self.modbus.read_register(slave_addr, self.register_map['completed_count'])

            if count_data and len(count_data) > 0:
                completed = count_data[0]

                # 弹出确认对话框
                msgBox = QMessageBox(QMessageBox.Question, "确认", f"剪切机报告已完成 {completed} 台，是否更新任务完成数量？",
                                  QMessageBox.Yes | QMessageBox.No, self)
                msgBox.button(QMessageBox.Yes).setText("是")
                msgBox.button(QMessageBox.No).setText("否")
                msgBox.setDefaultButton(QMessageBox.Yes)
                reply = msgBox.exec_()

                if reply == QMessageBox.Yes:
                    # 更新已完成数量
                    remaining = self.task_planner.update_completed_quantity(self.current_task_index, completed)

                    # 更新任务详情显示
                    task = self.task_planner.get_task_by_index(self.current_task_index)
                    self.task_completed_label.setText(str(task['completed_quantity']))
                    self.task_remaining_label.setText(str(task['remaining_quantity']))
                    self.task_status_label.setText(task['status'])

                    # 更新进度条
                    if task['total_quantity'] > 0:
                        self.task_progress_bar.setMaximum(task['total_quantity'])
                        self.task_progress_bar.setValue(task['completed_quantity'])
                        progress = int((task['completed_quantity'] / task['total_quantity']) * 100)
                        self.task_progress_bar.setFormat(f"{task['completed_quantity']}/{task['total_quantity']} ({progress}%)")
                    else:
                        self.task_progress_bar.setMaximum(1)
                        self.task_progress_bar.setValue(0)
                        self.task_progress_bar.setFormat("0/0 (0%)")

                    # 刷新任务列表
                    self.refresh_tasks_table()

                    self.status_label.setText(f"已更新完成数量: {completed}台，剩余: {remaining}台")
                    QMessageBox.information(self, "成功", f"已更新完成数量: {completed}台，剩余: {remaining}台")

                    # 复位剪切机计数器
                    self.modbus.write_register(slave_addr, self.register_map['reset'], 1)
            else:
                QMessageBox.warning(self, "警告", "无法从剪切机读取完成数量")
        else:
            # 手动输入完成数量
            dialog = QInputDialog(self)
            dialog.setWindowTitle("输入完成数量")
            dialog.setLabelText("请输入已完成台数:")
            dialog.setIntRange(0, task['remaining_quantity'])
            dialog.setIntValue(0)
            dialog.setIntStep(1)
            dialog.setOkButtonText("确定")
            dialog.setCancelButtonText("取消")
            if dialog.exec_():
                completed = dialog.intValue()
                ok = True
            else:
                ok = False

            if ok and completed > 0:
                # 更新已完成数量
                remaining = self.task_planner.update_completed_quantity(self.current_task_index, completed)

                # 更新任务详情显示
                task = self.task_planner.get_task_by_index(self.current_task_index)
                self.task_completed_label.setText(str(task['completed_quantity']))
                self.task_remaining_label.setText(str(task['remaining_quantity']))
                self.task_status_label.setText(task['status'])

                # 更新进度条
                if task['total_quantity'] > 0:
                    self.task_progress_bar.setMaximum(task['total_quantity'])
                    self.task_progress_bar.setValue(task['completed_quantity'])
                    progress = int((task['completed_quantity'] / task['total_quantity']) * 100)
                    self.task_progress_bar.setFormat(f"{task['completed_quantity']}/{task['total_quantity']} ({progress}%)")
                else:
                    self.task_progress_bar.setMaximum(1)
                    self.task_progress_bar.setValue(0)
                    self.task_progress_bar.setFormat("0/0 (0%)")

                # 刷新任务列表
                self.refresh_tasks_table()

                self.status_label.setText(f"已更新完成数量: {completed}台，剩余: {remaining}台")
                QMessageBox.information(self, "成功", f"已更新完成数量: {completed}台，剩余: {remaining}台")

    def delete_task(self):
        """删除任务"""
        if self.current_task_index < 0:
            QMessageBox.warning(self, "警告", "请先选择任务")
            return

        # 获取任务
        task = self.task_planner.get_task_by_index(self.current_task_index)
        if not task:
            return

        # 弹出确认对话框
        msgBox = QMessageBox(QMessageBox.Question, "确认", f"确定要删除任务 {task['code']} - {task['contract']} 吗？",
                          QMessageBox.Yes | QMessageBox.No, self)
        msgBox.button(QMessageBox.Yes).setText("是")
        msgBox.button(QMessageBox.No).setText("否")
        msgBox.setDefaultButton(QMessageBox.No)
        reply = msgBox.exec_()

        if reply == QMessageBox.Yes:
            # 删除任务
            if self.task_planner.delete_task(self.current_task_index):
                self.current_task_index = -1

                # 清空任务详情显示
                self.task_length_spin.setValue(0)
                self.task_increment_spin.setValue(0)
                self.task_speed_spin.setValue(80)
                self.task_error_adjust_spin.setValue(0)
                self.task_quantity_spin.setValue(1)
                self.task_completed_label.setText("0")
                self.task_remaining_label.setText("0")
                self.task_status_label.setText("未开始")

                # 重置进度条
                self.task_progress_bar.setMaximum(1)
                self.task_progress_bar.setValue(0)
                self.task_progress_bar.setFormat("0/0 (0%)")

                # 刷新任务列表
                self.refresh_tasks_table()

                self.status_label.setText(f"已删除任务: {task['code']} - {task['contract']}")
                QMessageBox.information(self, "成功", f"已删除任务: {task['code']} - {task['contract']}")
            else:
                self.status_label.setText("删除任务失败")
                QMessageBox.warning(self, "警告", "删除任务失败")

    def produce_task(self, row):
        """处理生产按钮点击事件"""
        try:
            task = self.task_planner.get_task_by_index(row)
            if not task:
                QMessageBox.warning(self, "警告", "无法获取任务信息")
                return

            # 检查剩余数量
            if task['remaining_quantity'] <= 0:
                QMessageBox.warning(self, "警告", "该任务已完成，无法继续生产")
                return

            # 显示生产确认对话框（不检查是否连接剪切机）
            self.show_production_dialog(row)
        except Exception as e:
            QMessageBox.critical(self, "错误", f"处理生产按钮时发生错误: {str(e)}")
            print(f"生产按钮错误: {str(e)}")

    def show_production_dialog(self, row):
        """显示生产确认对话框"""
        try:
            task = self.task_planner.get_task_by_index(row)
            if not task:
                QMessageBox.warning(self, "警告", "无法获取任务信息")
                return

            # 创建对话框
            dialog = QDialog(self)
            dialog.setWindowTitle("🚀 生产确认")
            dialog.setMinimumWidth(500)
            dialog.setMinimumHeight(400)

            # 设置对话框样式
            dialog.setStyleSheet("""
                QDialog {
                    background-color: #ffffff;
                    border-radius: 8px;
                }
                QLabel {
                    font-size: 13px;
                    font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                    color: #2c3e50;
                }
                QSpinBox {
                    border: 2px solid #e0e0e0;
                    border-radius: 6px;
                    padding: 8px 12px;
                    font-size: 13px;
                    font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                    background-color: #ffffff;
                    color: #2c3e50;
                    min-height: 20px;
                }
                QSpinBox:focus {
                    border-color: #3498db;
                    background-color: #f8fcff;
                }
            """)

            # 创建布局
            layout = QVBoxLayout(dialog)
            layout.setSpacing(20)
            layout.setContentsMargins(25, 25, 25, 25)

            # 添加任务信息
            info_group = QGroupBox("📋 任务信息")
            info_group.setStyleSheet("""
                QGroupBox {
                    border: 2px solid #e8f4fd;
                    border-radius: 8px;
                    margin-top: 15px;
                    margin-bottom: 15px;
                    background-color: #f8fcff;
                    font-size: 14px;
                    font-weight: bold;
                    color: #2c3e50;
                    font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                    padding-top: 10px;
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 15px;
                    padding: 5px 10px;
                    background-color: #ffffff;
                    border-radius: 4px;
                    border: 1px solid #e0e0e0;
                }
            """)
            info_layout = QFormLayout()
            info_layout.setSpacing(12)
            info_layout.setContentsMargins(20, 20, 20, 20)

            # 创建样式化的标签
            def create_info_label(text, is_important=False):
                label = QLabel(text)
                if is_important:
                    label.setStyleSheet("""
                        QLabel {
                            font-weight: bold;
                            color: #e74c3c;
                            background-color: #fff5f5;
                            padding: 4px 8px;
                            border-radius: 4px;
                            border: 1px solid #fecaca;
                        }
                    """)
                else:
                    label.setStyleSheet("""
                        QLabel {
                            font-weight: bold;
                            color: #2c3e50;
                            background-color: #f8f9fa;
                            padding: 4px 8px;
                            border-radius: 4px;
                            border: 1px solid #e9ecef;
                        }
                    """)
                return label

            # 代码和合同号
            info_layout.addRow("📝 代码:", create_info_label(task['code']))
            info_layout.addRow("📄 合同号:", create_info_label(task['contract']))

            # 铜带规格
            copper_spec = task.get('copper_spec', '')
            info_layout.addRow("🔧 铜带规格:", create_info_label(copper_spec if copper_spec else "未指定"))

            # 长度和递增量
            info_layout.addRow("📏 长度(mm):", create_info_label(f"{task['length']:.2f}"))
            info_layout.addRow("📐 递增量(mm):", create_info_label(f"{task['increment']:.2f}"))

            # 根数
            roots = task.get('roots', 0)
            info_layout.addRow("🔢 根数:", create_info_label(str(roots)))

            # 生产台数输入框
            machines_spin = QSpinBox()
            machines_spin.setRange(1, task['remaining_quantity'])
            machines_spin.setValue(task['remaining_quantity'])  # 默认为剩余台数
            machines_spin.setStyleSheet("""
                QSpinBox {
                    border: 2px solid #3498db;
                    border-radius: 6px;
                    padding: 8px 12px;
                    font-size: 14px;
                    font-weight: bold;
                    font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                    background-color: #f8fcff;
                    color: #2c3e50;
                    min-height: 25px;
                }
                QSpinBox:focus {
                    border-color: #2980b9;
                    background-color: #ebf3fd;
                }
            """)
            info_layout.addRow("🎯 生产台数:", machines_spin)

            # 剩余台数信息
            info_layout.addRow("📊 剩余台数:", create_info_label(str(task['remaining_quantity']), True))

            info_group.setLayout(info_layout)
            layout.addWidget(info_group)

            # 添加按钮区域
            buttons_layout = QHBoxLayout()
            buttons_layout.setSpacing(15)
            buttons_layout.setContentsMargins(0, 20, 0, 0)

            # 添加弹性空间
            buttons_layout.addStretch()

            # 取消按钮
            cancel_btn = QPushButton("❌ 取消")
            cancel_btn.setStyleSheet("""
                QPushButton {
                    background-color: #95a5a6;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 12px 24px;
                    font-size: 13px;
                    font-weight: bold;
                    font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                    min-width: 100px;
                }
                QPushButton:hover {
                    background-color: #7f8c8d;
                }
                QPushButton:pressed {
                    background-color: #6c7b7d;
                }
            """)
            cancel_btn.clicked.connect(dialog.reject)
            buttons_layout.addWidget(cancel_btn)

            # 确认按钮
            confirm_btn = QPushButton("✅ 确认并发送")
            confirm_btn.setStyleSheet("""
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 12px 24px;
                    font-size: 13px;
                    font-weight: bold;
                    font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                    min-width: 120px;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                }
                QPushButton:pressed {
                    background-color: #a93226;
                }
            """)
            confirm_btn.clicked.connect(lambda: self.send_production_params(row, machines_spin.value(), dialog))
            buttons_layout.addWidget(confirm_btn)

            # 添加弹性空间
            buttons_layout.addStretch()

            layout.addLayout(buttons_layout)

            # 显示对话框
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"显示生产对话框时发生错误: {str(e)}")
            print(f"生产对话框错误: {str(e)}")

    def send_production_params(self, row, machines, dialog):
        """发送生产参数到剪切机"""
        try:
            task = self.task_planner.get_task_by_index(row)
            if not task:
                QMessageBox.warning(self, "警告", "无法获取任务信息")
                return

            # 关闭对话框
            dialog.accept()

            # 检查是否连接剪切机（移到这里检查）
            if not self.modbus.is_connected():
                QMessageBox.warning(self, "警告", "请先连接剪切机")
                return

            # 获取从站地址
            slave_addr = self.slave_addr_spin.value()

            # 发送参数到剪切机
            try:
                # 转换参数为寄存器值并进行有效性验证
                # 长度参数验证 (0.01mm单位，支持大长度值)
                length_value = float(task['length'])
                if length_value < 0:
                    QMessageBox.warning(self, "警告", f"剪切长度不能为负值: {length_value}mm")
                    return
                length = int(length_value * 100)  # 转换为整数，单位0.01mm

                # 递增量参数验证 (-327.68mm到327.67mm)
                increment_value = float(task['increment'])
                if increment_value < -327.68 or increment_value > 327.67:
                    QMessageBox.warning(self, "警告", f"递增量超出有效范围: {increment_value}mm，有效范围为-327.68mm到327.67mm")
                    return
                increment = int(increment_value * 100)  # 转换为整数，单位0.01mm

                # 速度参数验证 (0-100%)
                speed = int(task['speed'])
                if speed < 0 or speed > 100:
                    QMessageBox.warning(self, "警告", f"速度超出有效范围: {speed}%，有效范围为0-100%")
                    return

                # 误差调整参数验证 (-10mm到10mm)
                error_adjust_value = float(task['error_adjust'])
                if error_adjust_value < -10 or error_adjust_value > 10:
                    QMessageBox.warning(self, "警告", f"误差调整超出有效范围: {error_adjust_value}mm，有效范围为-10mm到10mm")
                    return
                error_adjust = int(error_adjust_value * 100)  # 转换为整数，单位0.01mm

                # 根数参数验证 (1-100)
                roots = int(task.get('roots', 0))
                if roots < 0 or roots > 100:
                    QMessageBox.warning(self, "警告", f"根数超出有效范围: {roots}，有效范围为0-100")
                    return

                # 台数参数验证 (1-9999)
                if machines < 1 or machines > 9999:
                    QMessageBox.warning(self, "警告", f"台数超出有效范围: {machines}，有效范围为1-9999")
                    return

            except ValueError as e:
                QMessageBox.warning(self, "警告", f"参数格式错误: {str(e)}")
                return

            # 写入参数到对应寄存器
            success = True
            success &= self.modbus.write_register(slave_addr, self.register_map['length'], length)
            success &= self.modbus.write_register(slave_addr, self.register_map['increment'], increment)
            success &= self.modbus.write_register(slave_addr, self.register_map['speed'], speed)
            success &= self.modbus.write_register(slave_addr, self.register_map['error_adjust'], error_adjust)
            success &= self.modbus.write_register(slave_addr, self.register_map['roots'], roots)
            success &= self.modbus.write_register(slave_addr, self.register_map['machines'], machines)

            if success:
                # 更新主界面参数显示
                self.length_spin.setValue(task['length'])
                self.increment_spin.setValue(task['increment'])
                self.speed_spin.setValue(task['speed'])
                self.error_adjust_spin.setValue(task['error_adjust'])
                self.roots_spin.setValue(roots)
                self.machines_spin.setValue(machines)

                self.status_label.setText(f"已发送生产参数: {task['code']} - {task['contract']} (生产台数: {machines})")

                # 显示生产控制对话框
                self.show_production_control_dialog(row, task, machines)
            else:
                self.status_label.setText("发送参数失败")
                QMessageBox.warning(self, "警告", "发送参数失败")



        except Exception as e:
            QMessageBox.critical(self, "错误", f"发送生产参数时发生错误: {str(e)}")
            print(f"发送参数错误: {str(e)}")

    def show_production_control_dialog(self, row, task, machines):
        """显示生产控制对话框，允许用户停止生产"""
        try:
            # 创建对话框
            control_dialog = QDialog(self)
            control_dialog.setWindowTitle(f"🎛️ 生产控制 - {task['code']}")
            control_dialog.setMinimumWidth(500)
            control_dialog.setMinimumHeight(450)

            # 设置对话框样式
            control_dialog.setStyleSheet("""
                QDialog {
                    background-color: #ffffff;
                    border-radius: 8px;
                }
                QLabel {
                    font-size: 13px;
                    font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                    color: #2c3e50;
                }
            """)

            # 创建布局
            layout = QVBoxLayout(control_dialog)
            layout.setSpacing(20)
            layout.setContentsMargins(25, 25, 25, 25)

            # 添加任务信息
            info_group = QGroupBox("🎯 当前生产任务")
            info_group.setStyleSheet("""
                QGroupBox {
                    border: 2px solid #e8f6f3;
                    border-radius: 8px;
                    margin-top: 15px;
                    margin-bottom: 15px;
                    background-color: #f0fdf4;
                    font-size: 14px;
                    font-weight: bold;
                    color: #2c3e50;
                    font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                    padding-top: 10px;
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 15px;
                    padding: 5px 10px;
                    background-color: #ffffff;
                    border-radius: 4px;
                    border: 1px solid #e0e0e0;
                }
            """)
            info_layout = QFormLayout()
            info_layout.setSpacing(12)
            info_layout.setContentsMargins(20, 20, 20, 20)

            # 创建样式化的标签函数
            def create_control_label(text, is_highlight=False):
                label = QLabel(text)
                if is_highlight:
                    label.setStyleSheet("""
                        QLabel {
                            font-weight: bold;
                            color: #27ae60;
                            background-color: #f0fdf4;
                            padding: 4px 8px;
                            border-radius: 4px;
                            border: 1px solid #bbf7d0;
                        }
                    """)
                else:
                    label.setStyleSheet("""
                        QLabel {
                            font-weight: bold;
                            color: #2c3e50;
                            background-color: #f8f9fa;
                            padding: 4px 8px;
                            border-radius: 4px;
                            border: 1px solid #e9ecef;
                        }
                    """)
                return label

            # 代码和合同号
            info_layout.addRow("📝 代码:", create_control_label(task['code']))
            info_layout.addRow("📄 合同号:", create_control_label(task['contract']))

            # 铜带规格
            copper_spec = task.get('copper_spec', '')
            info_layout.addRow("🔧 铜带规格:", create_control_label(copper_spec if copper_spec else "未指定"))

            # 长度和递增量
            info_layout.addRow("📏 长度(mm):", create_control_label(f"{task['length']:.2f}"))
            info_layout.addRow("📐 递增量(mm):", create_control_label(f"{task['increment']:.2f}"))

            # 根数
            roots = task.get('roots', 0)
            info_layout.addRow("🔢 根数:", create_control_label(str(roots)))

            # 生产台数
            info_layout.addRow("🎯 生产台数:", create_control_label(str(machines), True))

            info_group.setLayout(info_layout)
            layout.addWidget(info_group)

            # 添加状态显示
            status_group = QGroupBox("📊 生产状态")
            status_group.setStyleSheet("""
                QGroupBox {
                    border: 2px solid #fff2e6;
                    border-radius: 8px;
                    margin-top: 15px;
                    margin-bottom: 15px;
                    background-color: #fffaf7;
                    font-size: 14px;
                    font-weight: bold;
                    color: #2c3e50;
                    font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                    padding-top: 10px;
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 15px;
                    padding: 5px 10px;
                    background-color: #ffffff;
                    border-radius: 4px;
                    border: 1px solid #e0e0e0;
                }
            """)
            status_layout = QVBoxLayout()
            status_layout.setSpacing(15)
            status_layout.setContentsMargins(20, 20, 20, 20)

            # 状态标签
            self.production_status_label = QLabel("🟡 准备就绪")
            self.production_status_label.setAlignment(Qt.AlignCenter)
            self.production_status_label.setStyleSheet("""
                QLabel {
                    font-size: 16px;
                    font-weight: bold;
                    color: #f39c12;
                    background-color: #fff9e6;
                    padding: 12px 20px;
                    border-radius: 6px;
                    border: 2px solid #fde68a;
                }
            """)
            status_layout.addWidget(self.production_status_label)

            # 进度条
            self.production_progress_bar = QProgressBar()
            self.production_progress_bar.setRange(0, machines)
            self.production_progress_bar.setValue(0)
            self.production_progress_bar.setFormat("0/" + str(machines) + " (0%)")
            status_layout.addWidget(self.production_progress_bar)

            status_group.setLayout(status_layout)
            layout.addWidget(status_group)

            # 添加按钮
            buttons_layout = QHBoxLayout()
            buttons_layout.setSpacing(15)
            buttons_layout.setContentsMargins(0, 20, 0, 0)

            # 添加弹性空间
            buttons_layout.addStretch()

            # 启动按钮
            start_btn = QPushButton("▶️ 启动生产")
            start_btn.setStyleSheet("""
                QPushButton {
                    background-color: #27ae60;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 12px 20px;
                    font-size: 13px;
                    font-weight: bold;
                    font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                    min-width: 110px;
                }
                QPushButton:hover {
                    background-color: #229954;
                }
                QPushButton:pressed {
                    background-color: #1e8449;
                }
            """)
            start_btn.clicked.connect(lambda: self.start_production(control_dialog))
            buttons_layout.addWidget(start_btn)

            # 停止按钮
            stop_btn = QPushButton("⏹️ 停止生产")
            stop_btn.setStyleSheet("""
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 12px 20px;
                    font-size: 13px;
                    font-weight: bold;
                    font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                    min-width: 110px;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                }
                QPushButton:pressed {
                    background-color: #a93226;
                }
            """)
            stop_btn.clicked.connect(lambda: self.stop_production(control_dialog))
            buttons_layout.addWidget(stop_btn)

            # 关闭按钮
            close_btn = QPushButton("❌ 关闭")
            close_btn.setStyleSheet("""
                QPushButton {
                    background-color: #95a5a6;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 12px 20px;
                    font-size: 13px;
                    font-weight: bold;
                    font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                    min-width: 110px;
                }
                QPushButton:hover {
                    background-color: #7f8c8d;
                }
                QPushButton:pressed {
                    background-color: #6c7b7d;
                }
            """)
            close_btn.clicked.connect(control_dialog.accept)
            buttons_layout.addWidget(close_btn)

            # 添加弹性空间
            buttons_layout.addStretch()

            layout.addLayout(buttons_layout)

            # 创建定时器，用于更新状态
            production_timer = QTimer()
            production_timer.timeout.connect(lambda: self.update_production_status(task, machines))
            production_timer.start(1000)  # 每秒更新一次

            # 注册定时器到管理器
            self.register_dialog_timer(production_timer)

            # 对话框关闭时的清理函数
            def on_dialog_finished():
                try:
                    if production_timer.isActive():
                        production_timer.stop()
                    if production_timer in self.dialog_timers:
                        self.dialog_timers.remove(production_timer)
                    print("生产控制对话框定时器已清理")
                except Exception as e:
                    print(f"清理生产控制对话框定时器时发生错误: {e}")

            control_dialog.finished.connect(on_dialog_finished)

            # 显示对话框
            control_dialog.exec_()

            # 对话框关闭时停止定时器
            self.production_timer.stop()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"显示生产控制对话框时发生错误: {str(e)}")
            print(f"生产控制对话框错误: {str(e)}")

    def start_production(self, dialog):
        """启动生产"""
        try:
            current_modbus = self.get_current_modbus()
            if not current_modbus.is_connected():
                QMessageBox.warning(self, "警告", "请先连接剪切机")
                return

            slave_addr = self.slave_addr_spin.value()

            # 检查红外感应功能是否启用
            if self.infrared_enabled:
                # 检查红外感应器状态 (X7)
                infrared_status = self.check_infrared_sensor()
                if infrared_status is None:
                    QMessageBox.warning(self, "警告", "无法读取红外感应器状态，请检查连接")
                    return
                elif not infrared_status:
                    QMessageBox.warning(self, "警告", "红外感应器未检测到物料，无法启动生产\n请确保进料口有物料后再启动")
                    self.add_monitor_message("生产启动失败：红外感应器未检测到物料 (X7=OFF)")
                    return

                # 红外感应器检测到物料，可以启动
                self.add_monitor_message("红外感应器检测到物料 (X7=ON)，允许启动生产")
            else:
                # 红外感应功能已禁用，跳过检查
                self.add_monitor_message("红外感应功能已禁用，跳过物料检查")

            # 写入启动命令到位地址M16
            if current_modbus.write_coil(slave_addr, self.bit_map['start'], True):  # True表示启动
                self.production_status_label.setText("🟢 生产中")
                self.production_status_label.setStyleSheet("""
                    QLabel {
                        font-size: 16px;
                        font-weight: bold;
                        color: #27ae60;
                        background-color: #f0fdf4;
                        padding: 12px 20px;
                        border-radius: 6px;
                        border: 2px solid #bbf7d0;
                    }
                """)
                self.machine_status_label.setText("运行中")
                self.status_label.setText("剪切机已启动")
                self.add_monitor_message("生产启动命令已发送到M16")
            else:
                QMessageBox.warning(self, "警告", "启动命令发送失败")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"启动生产时发生错误: {str(e)}")
            print(f"启动生产错误: {str(e)}")

    def stop_production(self, dialog):
        """停止生产"""
        try:
            current_modbus = self.get_current_modbus()
            if not current_modbus.is_connected():
                QMessageBox.warning(self, "警告", "请先连接剪切机")
                return

            slave_addr = self.slave_addr_spin.value()
            # 写入停止命令到位地址M17
            if current_modbus.write_coil(slave_addr, self.bit_map['stop'], True):  # True表示停止
                self.production_status_label.setText("🔴 已停止")
                self.production_status_label.setStyleSheet("""
                    QLabel {
                        font-size: 16px;
                        font-weight: bold;
                        color: #e74c3c;
                        background-color: #fff5f5;
                        padding: 12px 20px;
                        border-radius: 6px;
                        border: 2px solid #fecaca;
                    }
                """)
                self.machine_status_label.setText("已停止")
                self.status_label.setText("剪切机已停止")
                self.add_monitor_message("生产停止命令已发送到M17")

                # 弹出提示
                QMessageBox.information(dialog, "已停止", "剪切机已停止生产")
            else:
                QMessageBox.warning(self, "警告", "停止命令发送失败")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"停止生产时发生错误: {str(e)}")
            print(f"停止生产错误: {str(e)}")

    def update_production_status(self, task, machines):
        """更新生产状态"""
        try:
            if not self.modbus.is_connected():
                self.production_status_label.setText("连接已断开")
                self.production_status_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #e74c3c;")
                return

            slave_addr = self.slave_addr_spin.value()

            # 读取已完成数量
            count_data = self.modbus.read_register(slave_addr, self.register_map['completed_count'])
            if count_data and len(count_data) > 0:
                completed = count_data[0]

                # 更新进度条
                if completed <= machines:
                    self.production_progress_bar.setValue(completed)
                    progress = int((completed / machines) * 100)
                    self.production_progress_bar.setFormat(f"{completed}/{machines} ({progress}%)")

                    # 如果完成了所有台数，更新状态
                    if completed >= machines:
                        self.production_status_label.setText("生产完成")
                        self.production_status_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2ecc71;")

            # 读取机器状态
            status_data = self.modbus.read_register(slave_addr, self.register_map['machine_status'])
            if status_data and len(status_data) > 0:
                status = status_data[0]

                # 根据状态码更新状态显示
                if status == 0:
                    self.production_status_label.setText("已停止")
                    self.production_status_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #e74c3c;")
                elif status == 1:
                    self.production_status_label.setText("生产中")
                    self.production_status_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #27ae60;")
                elif status == 2:
                    self.production_status_label.setText("暂停中")
                    self.production_status_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #f39c12;")
                elif status == 3:
                    self.production_status_label.setText("错误状态")
                    self.production_status_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #e74c3c;")

        except Exception as e:
            print(f"更新生产状态错误: {str(e)}")

def check_expiration():
    """检查软件是否过期"""
    expiration_date = datetime.datetime(2025, 8, 30)  # 设置过期日期为2025年5月30日
    current_date = datetime.datetime.now()

    if current_date > expiration_date:
        QMessageBox.critical(None, "软件已过期", "测试版软件已过期，请联系开发者获取正式版。")
        return False

    days_left = (expiration_date - current_date).days
    if days_left < 7:
        QMessageBox.warning(None, "软件即将过期", f"测试版软件将在{days_left}天后过期。")

    return True

if __name__ == "__main__":
    app = QApplication(sys.argv)

    # 检查软件是否过期
    if not check_expiration():
        sys.exit(1)

    # 显示测试版水印
    splash = QSplashScreen(QPixmap(1, 1))
    splash.show()

    font = QFont()
    font.setPointSize(16)
    font.setBold(True)

    splash.setFont(font)
    splash.showMessage("剪切机自动化系统 - 测试版", Qt.AlignCenter, Qt.red)

    # 启动主窗口
    window = CuttingMachineController()
    window.setWindowTitle("剪切机自动化控制系统 - 测试版")
    window.show()

    # 关闭启动画面
    splash.finish(window)

    sys.exit(app.exec_())
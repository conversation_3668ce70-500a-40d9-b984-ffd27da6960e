# Translations template for Sphinx.
# Copyright (C) 2025 ORGANIZATION
# This file is distributed under the same license as the Sphinx project.
# 
# Translators:
# <PERSON>, 2023
# <PERSON>-<PERSON> <<EMAIL>>, 2018
# <PERSON>-<PERSON> <<EMAIL>>, 2018-2019
# <PERSON>-<PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2011
# <PERSON> <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2013-2018,2020,2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2016,2021
msgid ""
msgstr ""
"Project-Id-Version: Sphinx\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-02-18 00:38+0000\n"
"PO-Revision-Date: 2013-04-02 08:44+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: Spanish (http://app.transifex.com/sphinx-doc/sphinx-1/language/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#: extension.py:58
#, python-format
msgid ""
"The %s extension is required by needs_extensions settings, but it is not "
"loaded."
msgstr "La extensión %s es requerida por la configuración de needs_extensions, pero esta no es cargada."

#: extension.py:79
#, python-format
msgid ""
"This project needs the extension %s at least in version %s and therefore "
"cannot be built with the loaded version (%s)."
msgstr "Este proyecto necesita la extensión %s por lo menos en la versión %s y por lo tanto no puede ser construido con la versión cargada (%s)."

#: application.py:212
#, python-format
msgid "Cannot find source directory (%s)"
msgstr "No se encuentra directorio fuente (%s)"

#: application.py:217
#, python-format
msgid "Output directory (%s) is not a directory"
msgstr "Directorio de salida (%s) no es un directorio"

#: application.py:222
msgid "Source directory and destination directory cannot be identical"
msgstr "Directorio fuente y directorio destino no pueden ser idénticos"

#: application.py:252
#, python-format
msgid "Running Sphinx v%s"
msgstr "Ejecutando Sphinx v%s"

#: application.py:278
#, python-format
msgid ""
"This project needs at least Sphinx v%s and therefore cannot be built with "
"this version."
msgstr "Este proyecto necesita al menos Sphinx v%s y por lo tanto no se puede construir con esta versión."

#: application.py:297
msgid "making output directory"
msgstr "creando directorio de salida"

#: application.py:302 registry.py:538
#, python-format
msgid "while setting up extension %s:"
msgstr "mientras configura la extensión %s:"

#: application.py:309
msgid ""
"'setup' as currently defined in conf.py isn't a Python callable. Please "
"modify its definition to make it a callable function. This is needed for "
"conf.py to behave as a Sphinx extension."
msgstr "'setup' como se define actualmente en el archivo conf.py no es un Python invocable. Por favor, modifique su definición para que sea una función invocable. Esto es necesario para que el archivo conf.py se comporte como una extensión de Sphinx."

#: application.py:346
#, python-format
msgid "loading translations [%s]... "
msgstr "cargando traducciones [%s]... "

#: application.py:370 util/display.py:89
msgid "done"
msgstr "hecho"

#: application.py:372
msgid "not available for built-in messages"
msgstr "no disponible para mensajes incorporados"

#: application.py:386
msgid "loading pickled environment"
msgstr "cargando el ambiente pickled"

#: application.py:394
#, python-format
msgid "failed: %s"
msgstr "fallo: %s"

#: application.py:407
msgid "No builder selected, using default: html"
msgstr "Ningún constructor seleccionado, utilizando el valor predeterminado: html"

#: application.py:439
msgid "build finished with problems."
msgstr ""

#: application.py:441
msgid "build succeeded."
msgstr ""

#: application.py:446
msgid ""
"build finished with problems, 1 warning (with warnings treated as errors)."
msgstr ""

#: application.py:450
msgid "build finished with problems, 1 warning."
msgstr ""

#: application.py:452
msgid "build succeeded, 1 warning."
msgstr ""

#: application.py:458
#, python-format
msgid ""
"build finished with problems, %s warnings (with warnings treated as errors)."
msgstr ""

#: application.py:462
#, python-format
msgid "build finished with problems, %s warnings."
msgstr ""

#: application.py:464
#, python-format
msgid "build succeeded, %s warnings."
msgstr ""

#: application.py:1026
#, python-format
msgid "node class %r is already registered, its visitors will be overridden"
msgstr "la clase de nodo %r ya está registrada, sus visitantes serán reemplazados"

#: application.py:1119
#, python-format
msgid "directive %r is already registered and will not be overridden"
msgstr ""

#: application.py:1145 application.py:1173
#, python-format
msgid "role %r is already registered and will not be overridden"
msgstr ""

#: application.py:1770
#, python-format
msgid ""
"the %s extension does not declare if it is safe for parallel reading, "
"assuming it isn't - please ask the extension author to check and make it "
"explicit"
msgstr "la extensión de %s no declara si es seguro para la lectura en paralelo, asumiendo que no es - consulte con el autor de la extensión para comprobar y hacer explícito"

#: application.py:1775
#, python-format
msgid "the %s extension is not safe for parallel reading"
msgstr "la extensión %s no es segura para lectura paralela"

#: application.py:1779
#, python-format
msgid ""
"the %s extension does not declare if it is safe for parallel writing, "
"assuming it isn't - please ask the extension author to check and make it "
"explicit"
msgstr "la extensión %s no declara si es seguro para la escritura paralela, suponiendo que no lo sea - solicite al autor de la extensión que lo verifique y haga explicito"

#: application.py:1784
#, python-format
msgid "the %s extension is not safe for parallel writing"
msgstr "la extensión %s no es segura para escritura paralela"

#: application.py:1792 application.py:1796
#, python-format
msgid "doing serial %s"
msgstr "realizando serialmente %s"

#: config.py:355
#, python-format
msgid "config directory doesn't contain a conf.py file (%s)"
msgstr "directorio de configuración no contiene un archivo conf.py (%s)"

#: config.py:366
msgid ""
"Invalid configuration value found: 'language = None'. Update your "
"configuration to a valid language code. Falling back to 'en' (English)."
msgstr "Se encontró un valor de configuración no válido: 'language = None'. Actualice su configuración a un código de idioma válido. Volviendo a definir 'en' (Inglés)."

#: config.py:394
#, python-format
msgid "'%s' must be '0' or '1', got '%s'"
msgstr ""

#: config.py:399
#, python-format
msgid ""
"cannot override dictionary config setting %r, ignoring (use %r to set "
"individual elements)"
msgstr "no se puede reemplazar el ajuste de la configuración del diccionario %r, haciendo caso omiso (utilice %r para definir elementos individuales)"

#: config.py:411
#, python-format
msgid "invalid number %r for config value %r, ignoring"
msgstr "número no válido %r de valor de configuración %r, haciendo caso omiso"

#: config.py:419
#, python-format
msgid "cannot override config setting %r with unsupported type, ignoring"
msgstr "no se puede reemplazar los ajustes de configuración %r con tipo no compatible, haciendo caso omiso"

#: config.py:442
#, python-format
msgid "unknown config value %r in override, ignoring"
msgstr "valor de configuración desconocido %r en anulación, ignorando"

#: config.py:496
#, python-format
msgid "No such config value: %r"
msgstr ""

#: config.py:524
#, python-format
msgid "Config value %r already present"
msgstr "Valor de configuración %r ya presente"

#: config.py:561
#, python-format
msgid ""
"cannot cache unpickleable configuration value: %r (because it contains a "
"function, class, or module object)"
msgstr ""

#: config.py:603
#, python-format
msgid "There is a syntax error in your configuration file: %s\n"
msgstr "Hay un error de sintaxis en su archivo de configuración: %s\n"

#: config.py:607
msgid ""
"The configuration file (or one of the modules it imports) called sys.exit()"
msgstr "El archivo de configuración (o uno de los módulos que importa) invocó sys.exit()"

#: config.py:615
#, python-format
msgid ""
"There is a programmable error in your configuration file:\n"
"\n"
"%s"
msgstr "Hay un error programable en su archivo de configuración:\n\n%s"

#: config.py:637
#, python-format
msgid "Failed to convert %r to a frozenset"
msgstr ""

#: config.py:655 config.py:663
#, python-format
msgid "Converting `source_suffix = %r` to `source_suffix = %r`."
msgstr ""

#: config.py:669
#, python-format
msgid ""
"The config value `source_suffix' expects a dictionary, a string, or a list "
"of strings. Got `%r' instead (type %s)."
msgstr ""

#: config.py:690
#, python-format
msgid "Section %s"
msgstr "Sección %s"

#: config.py:691
#, python-format
msgid "Fig. %s"
msgstr "Figura %s"

#: config.py:692
#, python-format
msgid "Table %s"
msgstr "Tabla %s"

#: config.py:693
#, python-format
msgid "Listing %s"
msgstr "Lista %s"

#: config.py:802
#, python-brace-format
msgid ""
"The config value `{name}` has to be a one of {candidates}, but `{current}` "
"is given."
msgstr "El valor de configuración `{name}` tiene que ser uno de {candidates}, pero fue dado `{current}`."

#: config.py:833
#, python-brace-format
msgid ""
"The config value `{name}' has type `{current.__name__}'; expected "
"{permitted}."
msgstr "El valor de configuración `{name}' tiene tipo `{current.__name__}'; esperado {permitted}."

#: config.py:850
#, python-brace-format
msgid ""
"The config value `{name}' has type `{current.__name__}', defaults to "
"`{default.__name__}'."
msgstr "El valor de configuración `{name}' tiene el tipo `{current.__name__}', el valor predeterminado es `{default.__name__}'."

#: config.py:862
#, python-format
msgid "primary_domain %r not found, ignored."
msgstr "primary_domain %r no fue encontrado, se ignora."

#: config.py:882
msgid ""
"Sphinx now uses \"index\" as the master document by default. To keep pre-2.0"
" behaviour, set \"master_doc = 'contents'\"."
msgstr ""

#: highlighting.py:170
#, python-format
msgid "Pygments lexer name %r is not known"
msgstr "El nombre del lexer de pigmentos %r se desconoce"

#: highlighting.py:209
#, python-format
msgid ""
"Lexing literal_block %r as \"%s\" resulted in an error at token: %r. "
"Retrying in relaxed mode."
msgstr ""

#: theming.py:115
#, python-format
msgid ""
"Theme configuration sections other than [theme] and [options] are not "
"supported (tried to get a value from %r)."
msgstr ""

#: theming.py:120
#, python-format
msgid "setting %s.%s occurs in none of the searched theme configs"
msgstr "configuración de %s.%s se produce en ninguna de las configuraciones de tema buscado"

#: theming.py:135
#, python-format
msgid "unsupported theme option %r given"
msgstr "opción de tema no soportada %r fue dada"

#: theming.py:208
#, python-format
msgid "file %r on theme path is not a valid zipfile or contains no theme"
msgstr "archivo %r o ruta del tema no es un archivo zip válido o no contiene ningún tema"

#: theming.py:228
#, python-format
msgid "no theme named %r found (missing theme.toml?)"
msgstr ""

#: theming.py:268
#, python-format
msgid "The %r theme has circular inheritance"
msgstr ""

#: theming.py:276
#, python-format
msgid ""
"The %r theme inherits from %r, which is not a loaded theme. Loaded themes "
"are: %s"
msgstr ""

#: theming.py:282
#, python-format
msgid "The %r theme has too many ancestors"
msgstr ""

#: theming.py:310
#, python-format
msgid "no theme configuration file found in %r"
msgstr ""

#: theming.py:335 theming.py:388
#, python-format
msgid "theme %r doesn't have the \"theme\" table"
msgstr ""

#: theming.py:339
#, python-format
msgid "The %r theme \"[theme]\" table is not a table"
msgstr ""

#: theming.py:343 theming.py:391
#, python-format
msgid "The %r theme must define the \"theme.inherit\" setting"
msgstr ""

#: theming.py:347
#, python-format
msgid "The %r theme \"[options]\" table is not a table"
msgstr ""

#: theming.py:366
#, python-format
msgid "The \"theme.pygments_style\" setting must be a table. Hint: \"%s\""
msgstr ""

#: events.py:77
#, python-format
msgid "Event %r already present"
msgstr "Evento %r ya presente"

#: events.py:370
#, python-format
msgid "Unknown event name: %s"
msgstr "Nombre de evento desconocido: %s"

#: events.py:416
#, python-format
msgid "Handler %r for event %r threw an exception"
msgstr "Manipulador %r para el evento %r lanzó una excepción"

#: project.py:72
#, python-format
msgid ""
"multiple files found for the document \"%s\": %s\n"
"Use %r for the build."
msgstr ""

#: project.py:87
#, python-format
msgid "Ignored unreadable document %r."
msgstr ""

#: registry.py:167
#, python-format
msgid "Builder class %s has no \"name\" attribute"
msgstr "Constructor clase %s no tiene ningún atributo \"name\""

#: registry.py:171
#, python-format
msgid "Builder %r already exists (in module %s)"
msgstr "Constructor %r ya existe (en el módulo %s)"

#: registry.py:187
#, python-format
msgid "Builder name %s not registered or available through entry point"
msgstr "Nombre de constructor %s no registrados o disponibles a través del punto de entrada"

#: registry.py:197
#, python-format
msgid "Builder name %s not registered"
msgstr "Nombre de constructor %s no registrado"

#: registry.py:204
#, python-format
msgid "domain %s already registered"
msgstr "dominio %s ya esta registrado"

#: registry.py:228 registry.py:249 registry.py:262
#, python-format
msgid "domain %s not yet registered"
msgstr "dominio %s no esta registrado"

#: registry.py:235
#, python-format
msgid "The %r directive is already registered to domain %s"
msgstr "La directiva %r ya fue registrada en el dominio %s"

#: registry.py:253
#, python-format
msgid "The %r role is already registered to domain %s"
msgstr "El rol %r ya fue registrado en el dominio %s"

#: registry.py:266
#, python-format
msgid "The %r index is already registered to domain %s"
msgstr "El índice %r ya fue registrado en el dominio %s"

#: registry.py:313
#, python-format
msgid "The %r object_type is already registered"
msgstr "El %r object_type ya está registrado"

#: registry.py:344
#, python-format
msgid "The %r crossref_type is already registered"
msgstr "El %r crossref_type ya está registrado"

#: registry.py:353
#, python-format
msgid "source_suffix %r is already registered"
msgstr "source_suffix %r ya está registrado"

#: registry.py:363
#, python-format
msgid "source_parser for %r is already registered"
msgstr "source_parser para %r ya está registrado"

#: registry.py:372
#, python-format
msgid "Source parser for %s not registered"
msgstr "Analizador de fuentes para %s no registrado"

#: registry.py:390
#, python-format
msgid "Translator for %r already exists"
msgstr "Traductor para %r ya existe"

#: registry.py:407
#, python-format
msgid "kwargs for add_node() must be a (visit, depart) function tuple: %r=%r"
msgstr "kwargs para la función add_node() debe ser una tupla de función (visitar, salir): %r=%r"

#: registry.py:496
#, python-format
msgid "enumerable_node %r already registered"
msgstr "enumerable_node %r ya esta registrado"

#: registry.py:512
#, python-format
msgid "math renderer %s is already registered"
msgstr "el renderizador matemático %s ya está registrado"

#: registry.py:529
#, python-format
msgid ""
"the extension %r was already merged with Sphinx since version %s; this "
"extension is ignored."
msgstr "la extensión %r ya se fusionó con Sphinx desde la versión %s; esta extensión se omite."

#: registry.py:543
msgid "Original exception:\n"
msgstr "Excepción original:\n"

#: registry.py:545
#, python-format
msgid "Could not import extension %s"
msgstr "No puede importar la extensión %s"

#: registry.py:552
#, python-format
msgid ""
"extension %r has no setup() function; is it really a Sphinx extension "
"module?"
msgstr "extensión %r no tiene ninguna función setup(); ¿es realmente un módulo de extensión de Sphinx?"

#: registry.py:565
#, python-format
msgid ""
"The %s extension used by this project needs at least Sphinx v%s; it "
"therefore cannot be built with this version."
msgstr "La extensión %s utilizada por este proyecto necesita al menos la versión de Sphinx v%s; por lo tanto no puede ser construido con esta versión."

#: registry.py:577
#, python-format
msgid ""
"extension %r returned an unsupported object from its setup() function; it "
"should return None or a metadata dictionary"
msgstr "extensión %r devuelve un objeto no soportado de su función setup(); debe devolver un diccionario de metadatos o ninguno"

#: registry.py:612
#, python-format
msgid "`None` is not a valid filetype for %r."
msgstr ""

#: roles.py:206
#, python-format
msgid "Common Vulnerabilities and Exposures; CVE %s"
msgstr ""

#: roles.py:229
#, python-format
msgid "invalid CVE number %s"
msgstr ""

#: roles.py:251
#, python-format
msgid "Common Weakness Enumeration; CWE %s"
msgstr ""

#: roles.py:274
#, python-format
msgid "invalid CWE number %s"
msgstr ""

#: roles.py:294
#, python-format
msgid "Python Enhancement Proposals; PEP %s"
msgstr "Python Enhancement Proposals; PEP %s"

#: roles.py:317
#, python-format
msgid "invalid PEP number %s"
msgstr "número de PEP inválido %s"

#: roles.py:355
#, python-format
msgid "invalid RFC number %s"
msgstr "número RFC inválido %s"

#: ext/linkcode.py:86 ext/viewcode.py:226
msgid "[source]"
msgstr "[fuente]"

#: ext/viewcode.py:289
msgid "highlighting module code... "
msgstr "resaltando el código del módulo... "

#: ext/viewcode.py:320
msgid "[docs]"
msgstr "[documentos]"

#: ext/viewcode.py:346
msgid "Module code"
msgstr "Código de módulo"

#: ext/viewcode.py:353
#, python-format
msgid "<h1>Source code for %s</h1>"
msgstr "<h1>Código fuente para %s</h1>"

#: ext/viewcode.py:380
msgid "Overview: module code"
msgstr "Resumen: código de modulo"

#: ext/viewcode.py:381
msgid "<h1>All modules for which code is available</h1>"
msgstr "<h1>Todos los módulos para los cuales disponen código</h1>"

#: ext/extlinks.py:82
#, python-format
msgid ""
"hardcoded link %r could be replaced by an extlink (try using %r instead)"
msgstr "enlace codificado %r podría reemplazarse por un enlace externo (intente usar %r en su lugar)"

#: ext/autosectionlabel.py:52
#, python-format
msgid "section \"%s\" gets labeled as \"%s\""
msgstr ""

#: domains/std/__init__.py:833 domains/std/__init__.py:960
#: ext/autosectionlabel.py:61
#, python-format
msgid "duplicate label %s, other instance in %s"
msgstr "etiqueta duplicada %s, otra instancia en %s"

#: ext/imgmath.py:387 ext/mathjax.py:60
msgid "Link to this equation"
msgstr ""

#: ext/duration.py:90
msgid ""
"====================== slowest reading durations ======================="
msgstr "====================== duraciones de lectura más lentas ======================="

#: ext/doctest.py:118
#, python-format
msgid "missing '+' or '-' in '%s' option."
msgstr "falta '+' o '-' en la opción '%s'."

#: ext/doctest.py:124
#, python-format
msgid "'%s' is not a valid option."
msgstr "'%s' no es una opción válida."

#: ext/doctest.py:139
#, python-format
msgid "'%s' is not a valid pyversion option"
msgstr "'%s' no es una opción pyversion válida"

#: ext/doctest.py:226
msgid "invalid TestCode type"
msgstr "tipo de TestCode inválido"

#: ext/doctest.py:297
#, python-format
msgid ""
"Testing of doctests in the sources finished, look at the results in "
"%(outdir)s/output.txt."
msgstr "Prueba de doctests en las fuentes terminadas, mira los resultados en %(outdir)s/output.txt."

#: ext/doctest.py:457
#, python-format
msgid "no code/output in %s block at %s:%s"
msgstr "sin código/salida en el bloque %s en %s:%s"

#: ext/doctest.py:568
#, python-format
msgid "ignoring invalid doctest code: %r"
msgstr "ignorando el código doctest no válido: %r"

#: ext/imgmath.py:162
#, python-format
msgid ""
"LaTeX command %r cannot be run (needed for math display), check the "
"imgmath_latex setting"
msgstr "comando LaTeX %r no se puede ejecutar (necesario para la visualización matemática), compruebe la configuración de imgmath_latex"

#: ext/imgmath.py:181
#, python-format
msgid ""
"%s command %r cannot be run (needed for math display), check the imgmath_%s "
"setting"
msgstr "comando %s %r no se puede ejecutar (necesario para la visualización matemática), verifique la configuración imgmath_%s"

#: ext/imgmath.py:344
#, python-format
msgid "display latex %r: %s"
msgstr "visualizar latex %r: %s"

#: ext/imgmath.py:380
#, python-format
msgid "inline latex %r: %s"
msgstr "en línea latex %r: %s"

#: ext/coverage.py:48
#, python-format
msgid "invalid regex %r in %s"
msgstr "expresiones regulares inválidas %r en %s"

#: ext/coverage.py:140 ext/coverage.py:301
#, python-format
msgid "module %s could not be imported: %s"
msgstr "el módulo %s no podía ser importado: %s"

#: ext/coverage.py:148
#, python-format
msgid ""
"the following modules are documented but were not specified in "
"coverage_modules: %s"
msgstr ""

#: ext/coverage.py:158
msgid ""
"the following modules are specified in coverage_modules but were not "
"documented"
msgstr ""

#: ext/coverage.py:172
#, python-brace-format, python-format
msgid ""
"Testing of coverage in the sources finished, look at the results in "
"%(outdir)s{sep}python.txt."
msgstr ""

#: ext/coverage.py:187
#, python-format
msgid "invalid regex %r in coverage_c_regexes"
msgstr "expresiones regulares inválidas %r en coverage_c_regexes"

#: ext/coverage.py:260
#, python-format
msgid "undocumented c api: %s [%s] in file %s"
msgstr "api c indocumentado: %s [%s] en archivo %s"

#: ext/coverage.py:452
#, python-format
msgid "undocumented python function: %s :: %s"
msgstr "función python indocumentada: %s :: %s"

#: ext/coverage.py:473
#, python-format
msgid "undocumented python class: %s :: %s"
msgstr "clase python indocumentada: %s :: %s"

#: ext/coverage.py:492
#, python-format
msgid "undocumented python method: %s :: %s :: %s"
msgstr "método python indocumentado: %s :: %s :: %s"

#: ext/imgconverter.py:44
#, python-format
msgid ""
"Unable to run the image conversion command %r. 'sphinx.ext.imgconverter' requires ImageMagick by default. Ensure it is installed, or set the 'image_converter' option to a custom conversion command.\n"
"\n"
"Traceback: %s"
msgstr "No se puede ejecutar el comando de conversión de imagen %r. 'sphinx.ext.imgconverter' requiere ImageMagick por defecto. Asegúrese de que esté instalado o configure la opción 'image_converter' a un comando de conversión personalizado.\n\nRastrear: %s"

#: ext/imgconverter.py:56 ext/imgconverter.py:90
#, python-format
msgid ""
"convert exited with error:\n"
"[stderr]\n"
"%r\n"
"[stdout]\n"
"%r"
msgstr "convert salió con error:\n[stderr]\n%r\n[stdout]\n%r"

#: ext/imgconverter.py:83
#, python-format
msgid "convert command %r cannot be run, check the image_converter setting"
msgstr "el comando convert %r no puede ejecutar, compruebe el valor de configuración image_converter"

#: ext/graphviz.py:138
msgid "Graphviz directive cannot have both content and a filename argument"
msgstr "Directiva Graphviz no puede tener tanto el contenido y un argumento de nombre de archivo"

#: ext/graphviz.py:153
#, python-format
msgid "External Graphviz file %r not found or reading it failed"
msgstr "Archivo externo Graphviz %r no encontrado o la lectura del mismo fallo"

#: ext/graphviz.py:164
msgid "Ignoring \"graphviz\" directive without content."
msgstr "Ignorando la directiva \"graphviz\" sin contenido."

#: ext/graphviz.py:287
#, python-format
msgid "graphviz_dot executable path must be set! %r"
msgstr ""

#: ext/graphviz.py:328
#, python-format
msgid ""
"dot command %r cannot be run (needed for graphviz output), check the "
"graphviz_dot setting"
msgstr "comando dot %r no se puede ejecutar (necesarios para la salida de graphviz), Compruebe la configuración de graphviz_dot"

#: ext/graphviz.py:339
#, python-format
msgid ""
"dot exited with error:\n"
"[stderr]\n"
"%r\n"
"[stdout]\n"
"%r"
msgstr "dot salió con error:\n[stderr]\n%r\n[stdout]\n%r"

#: ext/graphviz.py:344
#, python-format
msgid ""
"dot did not produce an output file:\n"
"[stderr]\n"
"%r\n"
"[stdout]\n"
"%r"
msgstr "dot no produjo un archivo de salida:\n[stderr]\n%r\n[stdout]\n%r"

#: ext/graphviz.py:367
#, python-format
msgid "graphviz_output_format must be either 'png' or 'svg', but is %r"
msgstr ""

#: ext/graphviz.py:373 ext/graphviz.py:436 ext/graphviz.py:480
#, python-format
msgid "dot code %r: %s"
msgstr "dot código %r: %s"

#: ext/graphviz.py:493 ext/graphviz.py:501
#, python-format
msgid "[graph: %s]"
msgstr "[gráfica: %s]"

#: ext/graphviz.py:495 ext/graphviz.py:503
msgid "[graph]"
msgstr "[gráfica]"

#: ext/todo.py:61
msgid "Todo"
msgstr "Por hacer"

#: ext/todo.py:94
#, python-format
msgid "TODO entry found: %s"
msgstr "Marca TODO encontrada: %s"

#: ext/todo.py:152
msgid "<<original entry>>"
msgstr "<<entrada original>>"

#: ext/todo.py:154
#, python-format
msgid "(The <<original entry>> is located in %s, line %d.)"
msgstr "(La <<entrada original>> se encuentra en %s, línea %d.)"

#: ext/todo.py:166
msgid "original entry"
msgstr "entrada original"

#: directives/code.py:66
msgid "non-whitespace stripped by dedent"
msgstr "no espacios en blanco eliminados por identado"

#: directives/code.py:87
#, python-format
msgid "Invalid caption: %s"
msgstr "Subtítulo inválido: %s"

#: directives/code.py:131 directives/code.py:297 directives/code.py:483
#, python-format
msgid "line number spec is out of range(1-%d): %r"
msgstr "la especificación del número de línea está fuera de range(1-%d): %r"

#: directives/code.py:216
#, python-format
msgid "Cannot use both \"%s\" and \"%s\" options"
msgstr "No puede utilizar ambas opciones \"%s\" y \"%s\""

#: directives/code.py:231
#, python-format
msgid "Include file '%s' not found or reading it failed"
msgstr ""

#: directives/code.py:235
#, python-format
msgid ""
"Encoding %r used for reading included file '%s' seems to be wrong, try "
"giving an :encoding: option"
msgstr ""

#: directives/code.py:276
#, python-format
msgid "Object named %r not found in include file %r"
msgstr "Objeto nombrado %r no encontrado en el archivo incluido %r"

#: directives/code.py:309
msgid "Cannot use \"lineno-match\" with a disjoint set of \"lines\""
msgstr "No puede utilizar a \"lineno-match\" con un conjunto desunido de \"líneas\""

#: directives/code.py:314
#, python-format
msgid "Line spec %r: no lines pulled from include file %r"
msgstr "Línea especifico %r: sin líneas tiradas desde el archivo incluido %r"

#: directives/patches.py:71
msgid ""
"\":file:\" option for csv-table directive now recognizes an absolute path as"
" a relative path from source directory. Please update your document."
msgstr "\":file:\" La opción para la directiva csv-table ahora reconoce una ruta absoluta como una ruta relativa desde el directorio de origen. Actualice su documento."

#: directives/other.py:119
#, python-format
msgid "toctree glob pattern %r didn't match any documents"
msgstr "patrón global toctree %r no coincide con ningún documento"

#: directives/other.py:153 environment/adapters/toctree.py:361
#, python-format
msgid "toctree contains reference to excluded document %r"
msgstr "toctree contiene referencia al documento excluido %r"

#: directives/other.py:156
#, python-format
msgid "toctree contains reference to nonexisting document %r"
msgstr "toctree contiene referencias a documentos inexistentes %r"

#: directives/other.py:169
#, python-format
msgid "duplicated entry found in toctree: %s"
msgstr "entrada duplicada encontrada en toctree: %s"

#: directives/other.py:203
msgid "Section author: "
msgstr "Autor de la sección: "

#: directives/other.py:205
msgid "Module author: "
msgstr "Autor del módulo: "

#: directives/other.py:207
msgid "Code author: "
msgstr "Código del autor: "

#: directives/other.py:209
msgid "Author: "
msgstr "Autor: "

#: directives/other.py:269
msgid ".. acks content is not a list"
msgstr ".. contenido de los reconocimientos no es una lista"

#: directives/other.py:292
msgid ".. hlist content is not a list"
msgstr ".. hlist contenido no es una lista"

#: builders/changes.py:29
#, python-format
msgid "The overview file is in %(outdir)s."
msgstr "El archivo de resumen está en %(outdir)s."

#: builders/changes.py:56
#, python-format
msgid "no changes in version %s."
msgstr "no hay cambios en versión %s."

#: builders/changes.py:58
msgid "writing summary file..."
msgstr "escribiendo archivo de resumen..."

#: builders/changes.py:70
msgid "Builtins"
msgstr "Funciones incorporadas"

#: builders/changes.py:72
msgid "Module level"
msgstr "Nivel de módulo"

#: builders/changes.py:124
msgid "copying source files..."
msgstr "copiando archivos fuente..."

#: builders/changes.py:133
#, python-format
msgid "could not read %r for changelog creation"
msgstr "no se pudo leer %r for para la creación del registro de cambios"

#: builders/manpage.py:37
#, python-format
msgid "The manual pages are in %(outdir)s."
msgstr "Las páginas del manual están en %(outdir)s."

#: builders/manpage.py:45
msgid "no \"man_pages\" config value found; no manual pages will be written"
msgstr "no se encontró el valor de configuración \"man_pages\"; no se escribirán las páginas del manual"

#: builders/latex/__init__.py:347 builders/manpage.py:54
#: builders/singlehtml.py:176 builders/texinfo.py:119
msgid "writing"
msgstr "escribiendo"

#: builders/manpage.py:71
#, python-format
msgid "\"man_pages\" config value references unknown document %s"
msgstr "El valor de configuración \"man_pages\" hace referencia a un documento desconocido %s"

#: builders/__init__.py:224
#, python-format
msgid "a suitable image for %s builder not found: %s (%s)"
msgstr "una imagen adecuada para %s constructor no encontrado: %s (%s)"

#: builders/__init__.py:232
#, python-format
msgid "a suitable image for %s builder not found: %s"
msgstr "una imagen adecuada para %s constructor no encontrado: %s"

#: builders/__init__.py:255
msgid "building [mo]: "
msgstr "compilando [mo]: "

#: builders/__init__.py:258 builders/__init__.py:759 builders/__init__.py:791
msgid "writing output... "
msgstr "escribiendo salida... "

#: builders/__init__.py:275
#, python-format
msgid "all of %d po files"
msgstr "todos los %d archivos po"

#: builders/__init__.py:297
#, python-format
msgid "targets for %d po files that are specified"
msgstr "los objetivos para %d los archivos po que se especifican"

#: builders/__init__.py:309
#, python-format
msgid "targets for %d po files that are out of date"
msgstr "los objetivos para %d los archivos po que estan desactualizados"

#: builders/__init__.py:319
msgid "all source files"
msgstr "todos los archivos fuente"

#: builders/__init__.py:330
#, python-format
msgid "file %r given on command line does not exist, "
msgstr ""

#: builders/__init__.py:337
#, python-format
msgid ""
"file %r given on command line is not under the source directory, ignoring"
msgstr "archivo %r dado en la línea de comandos no está en el directorio fuente, ignorado"

#: builders/__init__.py:348
#, python-format
msgid "file %r given on command line is not a valid document, ignoring"
msgstr ""

#: builders/__init__.py:361
#, python-format
msgid "%d source files given on command line"
msgstr "%d archivos fuente dados en la línea de comandos"

#: builders/__init__.py:377
#, python-format
msgid "targets for %d source files that are out of date"
msgstr "los objetivos para %d los archivos fuentes que estan desactualizados"

#: builders/__init__.py:395 builders/gettext.py:265
#, python-format
msgid "building [%s]: "
msgstr "compilando [%s]: "

#: builders/__init__.py:406
msgid "looking for now-outdated files... "
msgstr "buscando por archivos no actualizados... "

#: builders/__init__.py:410
#, python-format
msgid "%d found"
msgstr "%d encontrado"

#: builders/__init__.py:412
msgid "none found"
msgstr "no encontrado"

#: builders/__init__.py:419
msgid "pickling environment"
msgstr "preparando ambiente"

#: builders/__init__.py:426
msgid "checking consistency"
msgstr "verificando consistencia"

#: builders/__init__.py:430
msgid "no targets are out of date."
msgstr "no hay archivos objetivo desactualizados."

#: builders/__init__.py:469
msgid "updating environment: "
msgstr "actualizando ambiente: "

#: builders/__init__.py:494
#, python-format
msgid "%s added, %s changed, %s removed"
msgstr "%sañadido, %s cambiado, %s removido"

#: builders/__init__.py:531
#, python-format
msgid ""
"Sphinx is unable to load the master document (%s) because it matches a "
"built-in exclude pattern %r. Please move your master document to a different"
" location."
msgstr ""

#: builders/__init__.py:540
#, python-format
msgid ""
"Sphinx is unable to load the master document (%s) because it matches an "
"exclude pattern specified in conf.py, %r. Please remove this pattern from "
"conf.py."
msgstr ""

#: builders/__init__.py:551
#, python-format
msgid ""
"Sphinx is unable to load the master document (%s) because it is not included"
" in the custom include_patterns = %r. Ensure that a pattern in "
"include_patterns matches the master document."
msgstr ""

#: builders/__init__.py:558
#, python-format
msgid ""
"Sphinx is unable to load the master document (%s). The master document must "
"be within the source directory or a subdirectory of it."
msgstr ""

#: builders/__init__.py:576 builders/__init__.py:592
msgid "reading sources... "
msgstr "leyendo fuentes... "

#: builders/__init__.py:713
#, python-format
msgid "docnames to write: %s"
msgstr "docnames para escribir: %s"

#: builders/__init__.py:715
msgid "no docnames to write!"
msgstr ""

#: builders/__init__.py:728
msgid "preparing documents"
msgstr "preparando documentos"

#: builders/__init__.py:731
msgid "copying assets"
msgstr ""

#: builders/__init__.py:883
#, python-format
msgid "undecodable source characters, replacing with \"?\": %r"
msgstr "caracteres fuente no codificables, reemplazando con \"?\": %r"

#: builders/epub3.py:84
#, python-format
msgid "The ePub file is in %(outdir)s."
msgstr "El archivo ePub está en %(outdir)s."

#: builders/epub3.py:189
msgid "writing nav.xhtml file..."
msgstr "escribiendo el archivo nav.xhtml..."

#: builders/epub3.py:221
msgid "conf value \"epub_language\" (or \"language\") should not be empty for EPUB3"
msgstr "el valor de configuración \"epub_language\" (o \"language\") no debe estar vacío para EPUB3"

#: builders/epub3.py:227
msgid "conf value \"epub_uid\" should be XML NAME for EPUB3"
msgstr "el valor de configuración \"epub_uid\" debe ser XML NAME para EPUB3"

#: builders/epub3.py:232
msgid "conf value \"epub_title\" (or \"html_title\") should not be empty for EPUB3"
msgstr "el valor de configuración \"epub_title\" (or \"html_title\") no debe estar vacío para EPUB3"

#: builders/epub3.py:238
msgid "conf value \"epub_author\" should not be empty for EPUB3"
msgstr "el valor de configuración \"epub_author\" no debe estar vacío para EPUB3"

#: builders/epub3.py:242
msgid "conf value \"epub_contributor\" should not be empty for EPUB3"
msgstr "el valor de configuración \"epub_contributor\" no debe estar vacío para EPUB3"

#: builders/epub3.py:247
msgid "conf value \"epub_description\" should not be empty for EPUB3"
msgstr "el valor de configuración \"epub_description\" no debe estar vacío para EPUB3"

#: builders/epub3.py:251
msgid "conf value \"epub_publisher\" should not be empty for EPUB3"
msgstr "el valor de configuración \"epub_publisher\" no debe estar vacío para EPUB3"

#: builders/epub3.py:256
msgid "conf value \"epub_copyright\" (or \"copyright\")should not be empty for EPUB3"
msgstr "el valor de configuración \"epub_copyright\" (or \"copyright\") no debe estar vacío para EPUB3"

#: builders/epub3.py:262
msgid "conf value \"epub_identifier\" should not be empty for EPUB3"
msgstr "el valor de configuración \"epub_identifier\" no debe estar vacío para EPUB3"

#: builders/epub3.py:265
msgid "conf value \"version\" should not be empty for EPUB3"
msgstr "el valor de configuración \"version\" no debe estar vacío para EPUB3"

#: builders/epub3.py:279 builders/html/__init__.py:1291
#, python-format
msgid "invalid css_file: %r, ignored"
msgstr "css_file inválido: %r, ignorado"

#: builders/xml.py:31
#, python-format
msgid "The XML files are in %(outdir)s."
msgstr "Los archivos XML están en %(outdir)s."

#: builders/html/__init__.py:1241 builders/text.py:76 builders/xml.py:90
#, python-format
msgid "error writing file %s: %s"
msgstr "error escribiendo archivo %s: %s"

#: builders/xml.py:101
#, python-format
msgid "The pseudo-XML files are in %(outdir)s."
msgstr "Los archivos pseudo-XML están en %(outdir)s."

#: builders/texinfo.py:45
#, python-format
msgid "The Texinfo files are in %(outdir)s."
msgstr "Los archivos Texinfo están en %(outdir)s."

#: builders/texinfo.py:48
msgid ""
"\n"
"Run 'make' in that directory to run these through makeinfo\n"
"(use 'make info' here to do that automatically)."
msgstr "\nEjecute el comando 'make' en ese directorio para ejecutarlos a través de makeinfo\n(usa el comando 'make info' aquí para hacer esto automáticamente)."

#: builders/texinfo.py:77
msgid "no \"texinfo_documents\" config value found; no documents will be written"
msgstr "no se encontró el valor de configuración \"texinfo_documents\"; no se escribirán documentos"

#: builders/texinfo.py:89
#, python-format
msgid "\"texinfo_documents\" config value references unknown document %s"
msgstr "El valor de configuración \"texinfo_documents\" hace referencia a un documento desconocido %s"

#: builders/latex/__init__.py:325 builders/texinfo.py:113
#, python-format
msgid "processing %s"
msgstr "procesando %s"

#: builders/latex/__init__.py:405 builders/texinfo.py:172
msgid "resolving references..."
msgstr "resolviendo referencias..."

#: builders/latex/__init__.py:416 builders/texinfo.py:182
msgid " (in "
msgstr " (en "

#: builders/_epub_base.py:422 builders/html/__init__.py:779
#: builders/latex/__init__.py:481 builders/texinfo.py:198
msgid "copying images... "
msgstr "copiando imágenes... "

#: builders/_epub_base.py:444 builders/latex/__init__.py:496
#: builders/texinfo.py:215
#, python-format
msgid "cannot copy image file %r: %s"
msgstr "no se puede copiar archivo de imagen %r: %s"

#: builders/texinfo.py:222
msgid "copying Texinfo support files"
msgstr "copiando archivos de soporte Texinfo"

#: builders/texinfo.py:230
#, python-format
msgid "error writing file Makefile: %s"
msgstr "error escribiendo archivo Makefile: %s"

#: builders/_epub_base.py:223
#, python-format
msgid "duplicated ToC entry found: %s"
msgstr "entrada de tabla de contenido duplicada encontrada: %s"

#: builders/_epub_base.py:433
#, python-format
msgid "cannot read image file %r: copying it instead"
msgstr "no puede leer el archivo de imagen %r: en su lugar, lo copia"

#: builders/_epub_base.py:464
#, python-format
msgid "cannot write image file %r: %s"
msgstr "no se puede escribir archivo de imagen %r: %s"

#: builders/_epub_base.py:476
msgid "Pillow not found - copying image files"
msgstr "Pillow no encontrada - copiando archivos de imágenes"

#: builders/_epub_base.py:511
msgid "writing mimetype file..."
msgstr "escribiendo el archivo mimetype..."

#: builders/_epub_base.py:520
msgid "writing META-INF/container.xml file..."
msgstr "escribiendo el archivo META-INF/container.xml..."

#: builders/_epub_base.py:558
msgid "writing content.opf file..."
msgstr "escribiendo el archivo content.opf..."

#: builders/_epub_base.py:591
#, python-format
msgid "unknown mimetype for %s, ignoring"
msgstr "mimetype desconocido para %s, ignorando"

#: builders/_epub_base.py:745
msgid "node has an invalid level"
msgstr ""

#: builders/_epub_base.py:765
msgid "writing toc.ncx file..."
msgstr "escribiendo el archivo toc.ncx..."

#: builders/_epub_base.py:794
#, python-format
msgid "writing %s file..."
msgstr "escribiendo archivo %s..."

#: builders/dummy.py:19
msgid "The dummy builder generates no files."
msgstr "El constructor ficticio no genera archivos."

#: builders/gettext.py:244
#, python-format
msgid "The message catalogs are in %(outdir)s."
msgstr "Los catálogos de mensajes están en %(outdir)s."

#: builders/gettext.py:266
#, python-format
msgid "targets for %d template files"
msgstr "objetivos para los archivos de plantillas %d"

#: builders/gettext.py:271
msgid "reading templates... "
msgstr "leyendo plantillas... "

#: builders/gettext.py:307
msgid "writing message catalogs... "
msgstr "escribiendo catálogos de mensajes... "

#: builders/singlehtml.py:35
#, python-format
msgid "The HTML page is in %(outdir)s."
msgstr "Página HTML está en %(outdir)s."

#: builders/singlehtml.py:171
msgid "assembling single document"
msgstr "ensamblando documento sencillo"

#: builders/singlehtml.py:189
msgid "writing additional files"
msgstr "escribiendo archivos adicionales"

#: builders/linkcheck.py:77
#, python-format
msgid "Look for any errors in the above output or in %(outdir)s/output.txt"
msgstr "Busque cualquier error en la salida anterior o en el archivo %(outdir)s/output.txt"

#: builders/linkcheck.py:149
#, python-format
msgid "broken link: %s (%s)"
msgstr "enlace roto: %s (%s)"

#: builders/linkcheck.py:548
#, python-format
msgid "Anchor '%s' not found"
msgstr "Ancla '%s' no encontrado"

#: builders/linkcheck.py:758
#, python-format
msgid "Failed to compile regex in linkcheck_allowed_redirects: %r %s"
msgstr "Error al compilar expresiones regulares en linkcheck_allowed_redirects: %r %s"

#: builders/text.py:29
#, python-format
msgid "The text files are in %(outdir)s."
msgstr "Los archivos de texto están en %(outdir)s."

#: transforms/i18n.py:227 transforms/i18n.py:302
#, python-brace-format
msgid ""
"inconsistent footnote references in translated message. original: {0}, "
"translated: {1}"
msgstr "referencias de pie de página inconsistentes en el mensaje traducido. original: {0}, traducido: {1}"

#: transforms/i18n.py:272
#, python-brace-format
msgid ""
"inconsistent references in translated message. original: {0}, translated: "
"{1}"
msgstr "referencias inconsistentes en el mensaje traducido. original: {0}, traducido: {1}"

#: transforms/i18n.py:322
#, python-brace-format
msgid ""
"inconsistent citation references in translated message. original: {0}, "
"translated: {1}"
msgstr "referencias de citas inconsistentes en el mensaje traducido. original: {0}, traducido: {1}"

#: transforms/i18n.py:344
#, python-brace-format
msgid ""
"inconsistent term references in translated message. original: {0}, "
"translated: {1}"
msgstr "referencias de término inconsistentes en el mensaje traducido. original: {0}, traducido: {1}"

#: builders/html/__init__.py:486 builders/latex/__init__.py:199
#: transforms/__init__.py:129 writers/manpage.py:98 writers/texinfo.py:220
#, python-format
msgid "%b %d, %Y"
msgstr "%d de %B de %Y"

#: transforms/__init__.py:139
msgid "could not calculate translation progress!"
msgstr ""

#: transforms/__init__.py:144
msgid "no translated elements!"
msgstr ""

#: transforms/__init__.py:253
#, python-format
msgid ""
"4 column based index found. It might be a bug of extensions you use: %r"
msgstr "Índice basado en 4 columnas encontrado. Puede ser un error de extensiones que usted usa: %r"

#: transforms/__init__.py:294
#, python-format
msgid "Footnote [%s] is not referenced."
msgstr "Pie de página [%s] no está referenciado."

#: transforms/__init__.py:303
msgid "Footnote [*] is not referenced."
msgstr ""

#: transforms/__init__.py:314
msgid "Footnote [#] is not referenced."
msgstr "Pie de página [#] no está referenciado."

#: _cli/__init__.py:73
msgid "Usage:"
msgstr ""

#: _cli/__init__.py:75
#, python-brace-format
msgid "{0} [OPTIONS] <COMMAND> [<ARGS>]"
msgstr ""

#: _cli/__init__.py:78
msgid "  The Sphinx documentation generator."
msgstr ""

#: _cli/__init__.py:87
msgid "Commands:"
msgstr ""

#: _cli/__init__.py:98
msgid "Options"
msgstr "Opciones"

#: _cli/__init__.py:113 _cli/__init__.py:181
msgid "For more information, visit https://www.sphinx-doc.org/en/master/man/."
msgstr ""

#: _cli/__init__.py:171
#, python-brace-format
msgid ""
"{0}: error: {1}\n"
"Run '{0} --help' for information"
msgstr ""

#: _cli/__init__.py:179
msgid "   Manage documentation with Sphinx."
msgstr ""

#: _cli/__init__.py:191
msgid "Show the version and exit."
msgstr ""

#: _cli/__init__.py:199
msgid "Show this message and exit."
msgstr ""

#: _cli/__init__.py:203
msgid "Logging"
msgstr ""

#: _cli/__init__.py:210
msgid "Increase verbosity (can be repeated)"
msgstr ""

#: _cli/__init__.py:218
msgid "Only print errors and warnings."
msgstr ""

#: _cli/__init__.py:225
msgid "No output at all"
msgstr ""

#: _cli/__init__.py:231
msgid "<command>"
msgstr ""

#: _cli/__init__.py:263
msgid "See 'sphinx --help'.\n"
msgstr ""

#: environment/__init__.py:86
msgid "new config"
msgstr "nueva configuración"

#: environment/__init__.py:87
msgid "config changed"
msgstr "configuración modificada"

#: environment/__init__.py:88
msgid "extensions changed"
msgstr "extensiones modificadas"

#: environment/__init__.py:253
msgid "build environment version not current"
msgstr "la versión del entorno de compilación no es actual"

#: environment/__init__.py:255
msgid "source directory has changed"
msgstr "directorio fuente ha cambiado"

#: environment/__init__.py:325
#, python-format
msgid "The configuration has changed (1 option: %r)"
msgstr ""

#: environment/__init__.py:330
#, python-format
msgid "The configuration has changed (%d options: %s)"
msgstr ""

#: environment/__init__.py:336
#, python-format
msgid "The configuration has changed (%d options: %s, ...)"
msgstr ""

#: environment/__init__.py:379
msgid ""
"This environment is incompatible with the selected builder, please choose "
"another doctree directory."
msgstr "Este entorno es incompatible con el generador seleccionado, elija otro directorio doctree."

#: environment/__init__.py:493
#, python-format
msgid "Failed to scan documents in %s: %r"
msgstr "Error al escanear los documentos en %s: %r"

#: environment/__init__.py:658 ext/intersphinx/_resolve.py:234
#, python-format
msgid "Domain %r is not registered"
msgstr "Dominio %r no está registrado"

#: environment/__init__.py:813
msgid "document isn't included in any toctree"
msgstr "documento no está incluido en ningún toctree"

#: environment/__init__.py:859
msgid "self referenced toctree found. Ignored."
msgstr "toctree auto referenciado encontrado. Ignorado."

#: environment/__init__.py:889
#, python-format
msgid "document is referenced in multiple toctrees: %s, selecting: %s <- %s"
msgstr ""

#: util/i18n.py:100
#, python-format
msgid "reading error: %s, %s"
msgstr "leyendo error: %s, %s"

#: util/i18n.py:113
#, python-format
msgid "writing error: %s, %s"
msgstr "escribiendo error: %s, %s"

#: util/i18n.py:146
#, python-format
msgid "locale_dir %s does not exist"
msgstr ""

#: util/i18n.py:236
#, python-format
msgid "Invalid Babel locale: %r."
msgstr ""

#: util/i18n.py:245
#, python-format
msgid ""
"Invalid date format. Quote the string by single quote if you want to output "
"it directly: %s"
msgstr "Formato de fecha inválido. Cite la cadena con comillas simples si desea generarla directamente: %s"

#: util/docfields.py:103
#, python-format
msgid ""
"Problem in %s domain: field is supposed to use role '%s', but that role is "
"not in the domain."
msgstr "Problema en el dominio %s: se supone que el campo debe usar el rol '%s', pero ese rol no está en el dominio."

#: util/nodes.py:423
#, python-format
msgid ""
"%r is deprecated for index entries (from entry %r). Use 'pair: %s' instead."
msgstr ""

#: util/nodes.py:490
#, python-format
msgid "toctree contains ref to nonexisting file %r"
msgstr "toctree contiene referencia al archivo inexistente %r"

#: util/nodes.py:706
#, python-format
msgid "exception while evaluating only directive expression: %s"
msgstr "excepción al evaluar solamente la expresión directiva: %s"

#: util/display.py:82
msgid "skipped"
msgstr "omitido"

#: util/display.py:87
msgid "failed"
msgstr "fallado"

#: util/osutil.py:131
#, python-format
msgid ""
"Aborted attempted copy from %s to %s (the destination path has existing "
"data)."
msgstr ""

#: util/docutils.py:309
#, python-format
msgid "unknown directive name: %s"
msgstr ""

#: util/docutils.py:345
#, python-format
msgid "unknown role name: %s"
msgstr ""

#: util/docutils.py:789
#, python-format
msgid "unknown node type: %r"
msgstr "tipo de nodo desconocido: %r"

#: util/fileutil.py:76
#, python-format
msgid ""
"Aborted attempted copy from rendered template %s to %s (the destination path"
" has existing data)."
msgstr ""

#: util/fileutil.py:89
#, python-format
msgid "Writing evaluated template result to %s"
msgstr ""

#: util/rst.py:73
#, python-format
msgid "default role %s not found"
msgstr "rol por defecto %s no encontrado"

#: util/inventory.py:147
#, python-format
msgid "inventory <%s> contains duplicate definitions of %s"
msgstr ""

#: util/inventory.py:166
#, python-format
msgid "inventory <%s> contains multiple definitions for %s"
msgstr ""

#: writers/latex.py:1097 writers/manpage.py:259 writers/texinfo.py:663
msgid "Footnotes"
msgstr "Notas a pie de página"

#: writers/manpage.py:289 writers/text.py:945
#, python-format
msgid "[image: %s]"
msgstr "[imagen: %s]"

#: writers/manpage.py:290 writers/text.py:946
msgid "[image]"
msgstr "[imagen]"

#: builders/latex/__init__.py:206 domains/std/__init__.py:771
#: domains/std/__init__.py:784 templates/latex/latex.tex.jinja:106
#: themes/basic/genindex-single.html:22 themes/basic/genindex-single.html:48
#: themes/basic/genindex-split.html:3 themes/basic/genindex-split.html:6
#: themes/basic/genindex.html:3 themes/basic/genindex.html:26
#: themes/basic/genindex.html:59 themes/basic/layout.html:127
#: writers/texinfo.py:514
msgid "Index"
msgstr "Índice"

#: writers/latex.py:743 writers/texinfo.py:646
msgid ""
"encountered title node not in section, topic, table, admonition or sidebar"
msgstr "no se encontró el nodo de título en la sección, tema, tabla, advertencia o barra lateral"

#: writers/texinfo.py:1217
msgid "caption not inside a figure."
msgstr "subtítulo no dentro de una figura."

#: writers/texinfo.py:1303
#, python-format
msgid "unimplemented node type: %r"
msgstr "tipo de nodo no implementado: %r"

#: writers/latex.py:361
#, python-format
msgid "unknown %r toplevel_sectioning for class %r"
msgstr "desconocida %r toplevel_sectioning para la clase %r"

#: builders/latex/__init__.py:224 writers/latex.py:411
#, python-format
msgid "no Babel option known for language %r"
msgstr "No se conoce la opción de Babel para el idioma %r"

#: writers/latex.py:429
msgid "too large :maxdepth:, ignored."
msgstr "demasiado grande :maxdepth:, ignorado."

#: writers/latex.py:591
#, python-format
msgid "template %s not found; loading from legacy %s instead"
msgstr ""

#: writers/latex.py:707
msgid "document title is not a single Text node"
msgstr "título del documento no es un nodo de Texto único"

#: writers/html5.py:572 writers/latex.py:1106
#, python-format
msgid "unsupported rubric heading level: %s"
msgstr ""

#: writers/latex.py:1183
msgid ""
"both tabularcolumns and :widths: option are given. :widths: is ignored."
msgstr "ambas columnas tabulares y la opción :widths: se dan. La opción :widths: se ignora."

#: writers/latex.py:1580
#, python-format
msgid "dimension unit %s is invalid. Ignored."
msgstr "la unidad de dimensión %s no es válida. Ignorado."

#: writers/latex.py:1939
#, python-format
msgid "unknown index entry type %s found"
msgstr "tipo de entrada de índice desconocido %s encontrado"

#: domains/math.py:128 writers/latex.py:2495
#, python-format
msgid "Invalid math_eqref_format: %r"
msgstr "No válido math_eqref_format: %r"

#: writers/html5.py:96 writers/html5.py:105
msgid "Link to this definition"
msgstr ""

#: writers/html5.py:431
#, python-format
msgid "numfig_format is not defined for %s"
msgstr "numfig_format no está definido para %s"

#: writers/html5.py:441
#, python-format
msgid "Any IDs not assigned for %s node"
msgstr "Cualquier ID no asignado para el nodo %s"

#: writers/html5.py:496
msgid "Link to this term"
msgstr ""

#: writers/html5.py:548 writers/html5.py:553
msgid "Link to this heading"
msgstr ""

#: writers/html5.py:558
msgid "Link to this table"
msgstr ""

#: writers/html5.py:636
msgid "Link to this code"
msgstr ""

#: writers/html5.py:638
msgid "Link to this image"
msgstr ""

#: writers/html5.py:640
msgid "Link to this toctree"
msgstr ""

#: writers/html5.py:766
msgid "Could not obtain image size. :scale: option is ignored."
msgstr "No se pudo obtener el tamaño de la imagen. La opción :scale: se ignora."

#: domains/__init__.py:322
#, python-format
msgid "%s %s"
msgstr "%s %s"

#: domains/math.py:73
#, python-format
msgid "duplicate label of equation %s, other instance in %s"
msgstr "etiqueta duplicada de la ecuación %s, otra instancia en %s"

#: domains/javascript.py:182
#, python-format
msgid "%s() (built-in function)"
msgstr "%s() (función incorporada)"

#: domains/javascript.py:183 domains/python/__init__.py:287
#, python-format
msgid "%s() (%s method)"
msgstr "%s() (método de %s)"

#: domains/javascript.py:185
#, python-format
msgid "%s() (class)"
msgstr "%s() (clase)"

#: domains/javascript.py:187
#, python-format
msgid "%s (global variable or constant)"
msgstr "%s (variable global o constante)"

#: domains/javascript.py:189 domains/python/__init__.py:378
#, python-format
msgid "%s (%s attribute)"
msgstr "%s (atributo de %s)"

#: domains/javascript.py:273
msgid "Arguments"
msgstr "Argumentos"

#: domains/cpp/__init__.py:489 domains/javascript.py:280
msgid "Throws"
msgstr "Lanzamientos"

#: domains/c/__init__.py:339 domains/cpp/__init__.py:502
#: domains/javascript.py:287 domains/python/_object.py:221
msgid "Returns"
msgstr "Devuelve"

#: domains/c/__init__.py:345 domains/javascript.py:293
#: domains/python/_object.py:227
msgid "Return type"
msgstr "Tipo del valor devuelto"

#: domains/javascript.py:370
#, python-format
msgid "%s (module)"
msgstr "%s (módulo)"

#: domains/c/__init__.py:751 domains/cpp/__init__.py:941
#: domains/javascript.py:415 domains/python/__init__.py:740
msgid "function"
msgstr "función"

#: domains/javascript.py:416 domains/python/__init__.py:744
msgid "method"
msgstr "método"

#: domains/cpp/__init__.py:939 domains/javascript.py:417
#: domains/python/__init__.py:742
msgid "class"
msgstr "clase"

#: domains/javascript.py:418 domains/python/__init__.py:741
msgid "data"
msgstr "dato"

#: domains/javascript.py:419 domains/python/__init__.py:747
msgid "attribute"
msgstr "atributo"

#: domains/javascript.py:420 domains/python/__init__.py:750
msgid "module"
msgstr "módulo"

#: domains/javascript.py:454
#, python-format
msgid "duplicate %s description of %s, other %s in %s"
msgstr "duplicada %s descripción de %s, otra %s en %s"

#: domains/changeset.py:26
#, python-format
msgid "Added in version %s"
msgstr ""

#: domains/changeset.py:27
#, python-format
msgid "Changed in version %s"
msgstr "Distinto en la versión %s"

#: domains/changeset.py:28
#, python-format
msgid "Deprecated since version %s"
msgstr "Obsoleto desde la versión %s"

#: domains/changeset.py:29
#, python-format
msgid "Removed in version %s"
msgstr ""

#: domains/rst.py:131 domains/rst.py:190
#, python-format
msgid "%s (directive)"
msgstr "%s (directiva)"

#: domains/rst.py:191 domains/rst.py:202
#, python-format
msgid ":%s: (directive option)"
msgstr ":%s: (opción directiva)"

#: domains/rst.py:224
#, python-format
msgid "%s (role)"
msgstr "%s (rol)"

#: domains/rst.py:234
msgid "directive"
msgstr "directiva"

#: domains/rst.py:235
msgid "directive-option"
msgstr "directive-option"

#: domains/rst.py:236
msgid "role"
msgstr "rol"

#: domains/rst.py:262
#, python-format
msgid "duplicate description of %s %s, other instance in %s"
msgstr "descripción duplicada de %s %s, otra instancia en %s"

#: domains/citation.py:75
#, python-format
msgid "duplicate citation %s, other instance in %s"
msgstr "citación duplicada %s, otra instancia en %s"

#: domains/citation.py:92
#, python-format
msgid "Citation [%s] is not referenced."
msgstr "Citación [%s] no está referenciada."

#: locale/__init__.py:228
msgid "Attention"
msgstr "Atención"

#: locale/__init__.py:229
msgid "Caution"
msgstr "Prudencia"

#: locale/__init__.py:230
msgid "Danger"
msgstr "Peligro"

#: locale/__init__.py:231
msgid "Error"
msgstr "Error"

#: locale/__init__.py:232
msgid "Hint"
msgstr "Consejo"

#: locale/__init__.py:233
msgid "Important"
msgstr "Importante"

#: locale/__init__.py:234
msgid "Note"
msgstr "Nota"

#: locale/__init__.py:235
msgid "See also"
msgstr "Ver también"

#: locale/__init__.py:236
msgid "Tip"
msgstr "Truco"

#: locale/__init__.py:237
msgid "Warning"
msgstr "Advertencia"

#: cmd/quickstart.py:52
msgid "automatically insert docstrings from modules"
msgstr "insertar automáticamente docstrings de los módulos"

#: cmd/quickstart.py:53
msgid "automatically test code snippets in doctest blocks"
msgstr "probar automáticamente fragmentos de código en bloques doctest"

#: cmd/quickstart.py:54
msgid "link between Sphinx documentation of different projects"
msgstr "enlace entre la documentación de Sphinx de diferentes proyectos"

#: cmd/quickstart.py:55
msgid "write \"todo\" entries that can be shown or hidden on build"
msgstr "escribir entradas de \"todo\" que se pueden mostrar u ocultar en la compilación"

#: cmd/quickstart.py:56
msgid "checks for documentation coverage"
msgstr "verificación para el cubrimiento de la documentación"

#: cmd/quickstart.py:57
msgid "include math, rendered as PNG or SVG images"
msgstr "incluir expresiones matemáticas, mostradas como imágenes PNG o SVG"

#: cmd/quickstart.py:58
msgid "include math, rendered in the browser by MathJax"
msgstr "incluir matemática, mostrada en el navegador por MathJax"

#: cmd/quickstart.py:59
msgid "conditional inclusion of content based on config values"
msgstr "inclusión condicional de contenido basado en valores de configuración"

#: cmd/quickstart.py:60
msgid "include links to the source code of documented Python objects"
msgstr "incluir enlaces al código fuente de objetos documentados de Python"

#: cmd/quickstart.py:61
msgid "create .nojekyll file to publish the document on GitHub pages"
msgstr "crear archivo .nojekyll para publicar el documento en páginas GitHub"

#: cmd/quickstart.py:110
msgid "Please enter a valid path name."
msgstr "Por favor, ingrese un nombre de ruta válido."

#: cmd/quickstart.py:126
msgid "Please enter some text."
msgstr "Por favor, ingrese algún texto."

#: cmd/quickstart.py:133
#, python-format
msgid "Please enter one of %s."
msgstr "Por favor, ingrese uno de %s."

#: cmd/quickstart.py:141
msgid "Please enter either 'y' or 'n'."
msgstr "Por favor, ingrese cualquiera de 'y' o 'n'."

#: cmd/quickstart.py:147
msgid "Please enter a file suffix, e.g. '.rst' or '.txt'."
msgstr "Por favor, ingrese un archivo de sufijo, por ejemplo, '.rst' o '.txt'."

#: cmd/quickstart.py:229
#, python-format
msgid "Welcome to the Sphinx %s quickstart utility."
msgstr "Bienvenido a la utilidad de inicio rápido de Sphinx %s."

#: cmd/quickstart.py:234
msgid ""
"Please enter values for the following settings (just press Enter to\n"
"accept a default value, if one is given in brackets)."
msgstr "Ingrese los valores para las siguientes configuraciones (solo presione Entrar para\naceptar un valor predeterminado, si se da uno entre paréntesis)."

#: cmd/quickstart.py:241
#, python-format
msgid "Selected root path: %s"
msgstr "Ruta raíz seleccionada: %s"

#: cmd/quickstart.py:244
msgid "Enter the root path for documentation."
msgstr "Ingrese la ruta raíz para la documentación."

#: cmd/quickstart.py:245
msgid "Root path for the documentation"
msgstr "Ruta raíz para la documentación"

#: cmd/quickstart.py:254
msgid "Error: an existing conf.py has been found in the selected root path."
msgstr "Error: un archivo conf.py ya existe en la ruta raíz seleccionada."

#: cmd/quickstart.py:259
msgid "sphinx-quickstart will not overwrite existing Sphinx projects."
msgstr "sphinx-quickstart no sobreescribirá proyectos existentes de Sphinx."

#: cmd/quickstart.py:262
msgid "Please enter a new root path (or just Enter to exit)"
msgstr "Por favor, ingrese una nueva ruta raíz (o ingrese Enter para salir)"

#: cmd/quickstart.py:273
msgid ""
"You have two options for placing the build directory for Sphinx output.\n"
"Either, you use a directory \"_build\" within the root path, or you separate\n"
"\"source\" and \"build\" directories within the root path."
msgstr "Tiene dos opciones para colocar el directorio de compilación para la salida de Sphinx.\nO usas un directorio \"_build\" dentro de la ruta raíz, o separas\ndirectorios \"fuente\" y \"compilación\" dentro de la ruta raíz."

#: cmd/quickstart.py:279
msgid "Separate source and build directories (y/n)"
msgstr "Separar directorios fuente y compilado (y/n)"

#: cmd/quickstart.py:286
msgid ""
"Inside the root directory, two more directories will be created; \"_templates\"\n"
"for custom HTML templates and \"_static\" for custom stylesheets and other static\n"
"files. You can enter another prefix (such as \".\") to replace the underscore."
msgstr "Dentro del directorio raíz, se crearán dos directorios más; \"_templates\"\npara plantillas HTML personalizadas y \"_static\" para hojas de estilo personalizadas y otras archivos\nestáticos. Puede ingresar otro prefijo (como \".\") Para reemplazar el guión bajo."

#: cmd/quickstart.py:291
msgid "Name prefix for templates and static dir"
msgstr "Prefijo de nombre para directorios de plantillas y estático"

#: cmd/quickstart.py:297
msgid ""
"The project name will occur in several places in the built documentation."
msgstr "El nombre del proyecto aparecerá en varios lugares en la documentación construida."

#: cmd/quickstart.py:300
msgid "Project name"
msgstr "Nombre de proyecto"

#: cmd/quickstart.py:302
msgid "Author name(s)"
msgstr "Autor(es)"

#: cmd/quickstart.py:308
msgid ""
"Sphinx has the notion of a \"version\" and a \"release\" for the\n"
"software. Each version can have multiple releases. For example, for\n"
"Python the version is something like 2.5 or 3.0, while the release is\n"
"something like 2.5.1 or 3.0a1. If you don't need this dual structure,\n"
"just set both to the same value."
msgstr "Sphinx tiene la noción de una \"versión\" y un \"lanzamiento\" para el\nsoftware. Cada versión puede tener varios lanzamientos. Por ejemplo, para\nPython, la versión es algo así como 2.5 o 3.0, mientras que el lanzamiento es\nalgo así como 2.5.1 o 3.0a1. Si no necesita esta estructura dual, simplemente\nconfigure ambas con el mismo valor."

#: cmd/quickstart.py:315
msgid "Project version"
msgstr "Versión del proyecto"

#: cmd/quickstart.py:317
msgid "Project release"
msgstr "Liberación del proyecto"

#: cmd/quickstart.py:323
msgid ""
"If the documents are to be written in a language other than English,\n"
"you can select a language here by its language code. Sphinx will then\n"
"translate text that it generates into that language.\n"
"\n"
"For a list of supported codes, see\n"
"https://www.sphinx-doc.org/en/master/usage/configuration.html#confval-language."
msgstr "Si los documentos deben escribirse en un idioma que no sea inglés,\npuede seleccionar un idioma aquí por su código de idioma. Sphinx entonces\ntraducir el texto que genera a ese idioma.\n\nPara obtener una lista de códigos compatibles, vea\nhttps://www.sphinx-doc.org/en/master/usage/configuration.html#confval-language."

#: cmd/quickstart.py:331
msgid "Project language"
msgstr "Lenguaje del proyecto"

#: cmd/quickstart.py:339
msgid ""
"The file name suffix for source files. Commonly, this is either \".txt\"\n"
"or \".rst\". Only files with this suffix are considered documents."
msgstr "El sufijo del nombre de archivo para los archivos de fuente. Comúnmente, esto es \".txt\"\no \".rst\". Solo los archivos con este sufijo se consideran documentos."

#: cmd/quickstart.py:343
msgid "Source file suffix"
msgstr "Sufijo del archivo fuente"

#: cmd/quickstart.py:349
msgid ""
"One document is special in that it is considered the top node of the\n"
"\"contents tree\", that is, it is the root of the hierarchical structure\n"
"of the documents. Normally, this is \"index\", but if your \"index\"\n"
"document is a custom template, you can also set this to another filename."
msgstr "Un documento es especial porque se considera el nodo superior del\n\"contents tree\", es decir, es la raíz de la estructura jerárquica\nde los documentos. Normalmente, esto es \"index\", pero si su documento \"index\"\nes una plantilla personalizada, también puede establecerlo en otro nombre de archivo."

#: cmd/quickstart.py:356
msgid "Name of your master document (without suffix)"
msgstr "Nombre del documento maestro (sin sufijo)"

#: cmd/quickstart.py:367
#, python-format
msgid ""
"Error: the master file %s has already been found in the selected root path."
msgstr "Error: el archivo maestro %s ya se ha encontrado en la ruta raíz seleccionada."

#: cmd/quickstart.py:373
msgid "sphinx-quickstart will not overwrite the existing file."
msgstr "sphinx-quickstart no sobreescribirá el archivo existente."

#: cmd/quickstart.py:377
msgid ""
"Please enter a new file name, or rename the existing file and press Enter"
msgstr "Ingrese un nuevo nombre de archivo o cambie el nombre del archivo existente y presione Enter"

#: cmd/quickstart.py:385
msgid "Indicate which of the following Sphinx extensions should be enabled:"
msgstr "Indique cuál de las siguientes extensiones de Sphinx deben habilitarse:"

#: cmd/quickstart.py:396
msgid ""
"Note: imgmath and mathjax cannot be enabled at the same time. imgmath has "
"been deselected."
msgstr "Nota: imgmath y mathjax no se pueden habilitar al mismo tiempo. imgmath ha sido deseleccionado."

#: cmd/quickstart.py:406
msgid ""
"A Makefile and a Windows command file can be generated for you so that you\n"
"only have to run e.g. `make html' instead of invoking sphinx-build\n"
"directly."
msgstr "Se puede generar un archivo Makefile y un archivo de comandos de Windows para que usted\nsolo tiene que ejecutar, por ejemplo, `make html' en lugar de invocar sphinx-build\ndirectamente."

#: cmd/quickstart.py:411
msgid "Create Makefile? (y/n)"
msgstr "¿Crear Makefile? (y/n)"

#: cmd/quickstart.py:415
msgid "Create Windows command file? (y/n)"
msgstr "¿Crear archivo de comandos para Windows? (y/n)"

#: cmd/quickstart.py:467 ext/apidoc/_generate.py:76
#, python-format
msgid "Creating file %s."
msgstr "Creando archivo %s."

#: cmd/quickstart.py:472 ext/apidoc/_generate.py:73
#, python-format
msgid "File %s already exists, skipping."
msgstr "El archivo %s ya existe, omitiendo."

#: cmd/quickstart.py:515
msgid "Finished: An initial directory structure has been created."
msgstr "Terminado: se ha creado una estructura de directorio inicial."

#: cmd/quickstart.py:519
#, python-format
msgid ""
"You should now populate your master file %s and create other documentation\n"
"source files. "
msgstr "Ahora debe completar su archivo maestro %s y crear otros archivos fuente\nde documentación. "

#: cmd/quickstart.py:526
msgid ""
"Use the Makefile to build the docs, like so:\n"
"   make builder"
msgstr "Use el archivo Makefile para compilar los documentos, así ejecute el comando:\n   make builder"

#: cmd/quickstart.py:530
#, python-format
msgid ""
"Use the sphinx-build command to build the docs, like so:\n"
"   sphinx-build -b builder %s %s"
msgstr "Use el comando sphinx-build para compilar los documentos, así ejecute el comando:\n   sphinx-build -b builder %s %s"

#: cmd/quickstart.py:537
msgid ""
"where \"builder\" is one of the supported builders, e.g. html, latex or "
"linkcheck."
msgstr "donde \"builder\" es uno de los constructores compatibles, por ejemplo, html, latex o linkcheck."

#: cmd/quickstart.py:572
msgid ""
"\n"
"Generate required files for a Sphinx project.\n"
"\n"
"sphinx-quickstart is an interactive tool that asks some questions about your\n"
"project and then generates a complete documentation directory and sample\n"
"Makefile to be used with sphinx-build.\n"
msgstr "\nGenere los archivos necesarios para un proyecto Sphinx.\n\nsphinx-quickstart es una herramienta interactiva que hace algunas preguntas sobre su\nproyecto y luego genera un directorio completo de documentación y un ejemplo del archivo\nMakefilepara ser utilizado con el comando sphinx-build.\n"

#: cmd/build.py:73 cmd/quickstart.py:581 ext/apidoc/_cli.py:27
#: ext/autosummary/generate.py:835
msgid "For more information, visit <https://www.sphinx-doc.org/>."
msgstr "Para más información visite <https://www.sphinx-doc.org/>."

#: cmd/quickstart.py:591
msgid "quiet mode"
msgstr "modo silencioso"

#: cmd/quickstart.py:601
msgid "project root"
msgstr "raíz del proyecto"

#: cmd/quickstart.py:604
msgid "Structure options"
msgstr "Opciones de estructura"

#: cmd/quickstart.py:610
msgid "if specified, separate source and build dirs"
msgstr "si se especifica, separe los directorios de fuentes y de compilación"

#: cmd/quickstart.py:616
msgid "if specified, create build dir under source dir"
msgstr "si se especifica, cree un directorio de compilación en el directorio de origen"

#: cmd/quickstart.py:622
msgid "replacement for dot in _templates etc."
msgstr "reemplazo para punto en _templates, etc."

#: cmd/quickstart.py:625
msgid "Project basic options"
msgstr "Opciones básicas del proyecto"

#: cmd/quickstart.py:627
msgid "project name"
msgstr "nombre del proyecto"

#: cmd/quickstart.py:630
msgid "author names"
msgstr "autores"

#: cmd/quickstart.py:637
msgid "version of project"
msgstr "versión del proyecto"

#: cmd/quickstart.py:644
msgid "release of project"
msgstr "liberación del proyecto"

#: cmd/quickstart.py:651
msgid "document language"
msgstr "lenguaje del documento"

#: cmd/quickstart.py:654
msgid "source file suffix"
msgstr "sufijo de archivo fuente"

#: cmd/quickstart.py:657
msgid "master document name"
msgstr "nombre de documento maestro"

#: cmd/quickstart.py:660
msgid "use epub"
msgstr "usar epub"

#: cmd/quickstart.py:663
msgid "Extension options"
msgstr "Opciones de extensión"

#: cmd/quickstart.py:670
#, python-format
msgid "enable %s extension"
msgstr "habilitada extensión %s"

#: cmd/quickstart.py:677
msgid "enable arbitrary extensions"
msgstr "habilitar extensiones arbitrarias"

#: cmd/quickstart.py:680
msgid "Makefile and Batchfile creation"
msgstr "creación del Makefile y Batchfile"

#: cmd/quickstart.py:686
msgid "create makefile"
msgstr "crear makefile"

#: cmd/quickstart.py:692
msgid "do not create makefile"
msgstr "no crear makefile"

#: cmd/quickstart.py:699
msgid "create batchfile"
msgstr "crear batchfile"

#: cmd/quickstart.py:705
msgid "do not create batchfile"
msgstr "no crear batchfile"

#: cmd/quickstart.py:714
msgid "use make-mode for Makefile/make.bat"
msgstr "use el modo make para Makefile/make.bat"

#: cmd/quickstart.py:717 ext/apidoc/_cli.py:243
msgid "Project templating"
msgstr "Plantillas de proyecto"

#: cmd/quickstart.py:723 ext/apidoc/_cli.py:249
msgid "template directory for template files"
msgstr "directorio de plantillas para archivos de plantillas"

#: cmd/quickstart.py:730
msgid "define a template variable"
msgstr "definir una variable de proyceto"

#: cmd/quickstart.py:766
msgid "\"quiet\" is specified, but any of \"project\" or \"author\" is not specified."
msgstr "se especifica \"quiet\", pero no se especifica ninguno de \"project\" o \"author\"."

#: cmd/quickstart.py:785
msgid ""
"Error: specified path is not a directory, or sphinx files already exist."
msgstr "Error: la ruta especificada no es un directorio, o ya existen archivos sphinx."

#: cmd/quickstart.py:792
msgid ""
"sphinx-quickstart only generate into a empty directory. Please specify a new"
" root path."
msgstr "sphinx-quickstart solo se genera en un directorio vacío. Por favor, especifique una nueva ruta raíz."

#: cmd/quickstart.py:809
#, python-format
msgid "Invalid template variable: %s"
msgstr "Variable de plantilla inválida: %s"

#: cmd/build.py:64
msgid "job number should be a positive number"
msgstr "número de trabajo debe ser un número positivo"

#: cmd/build.py:74
msgid ""
"\n"
"Generate documentation from source files.\n"
"\n"
"sphinx-build generates documentation from the files in SOURCEDIR and places it\n"
"in OUTPUTDIR. It looks for 'conf.py' in SOURCEDIR for the configuration\n"
"settings. The 'sphinx-quickstart' tool may be used to generate template files,\n"
"including 'conf.py'\n"
"\n"
"sphinx-build can create documentation in different formats. A format is\n"
"selected by specifying the builder name on the command line; it defaults to\n"
"HTML. Builders can also perform other tasks related to documentation\n"
"processing.\n"
"\n"
"By default, everything that is outdated is built. Output only for selected\n"
"files can be built by specifying individual filenames.\n"
msgstr "\nGenerar documentación a partir de archivos fuente.\n\nsphinx-build genera documentación a partir de los archivos en SOURCEDIR y la\ncoloca en OUTPUTDIR. Busca 'conf.py' en SOURCEDIR para los ajustes de configuración.\nLa herramienta 'sphinx-quickstart' se puede usar para generar archivos de plantilla,\nincluido 'conf.py'\n\nsphinx-build puede crear documentación en diferentes formatos. Se selecciona un\nformato especificando el nombre del constructor en la línea de comando; por defecto\nes HTML. Los constructores también pueden realizar otras tareas relacionadas con\nel procesamiento de la documentación.\n\nDe forma predeterminada, se construye todo lo que está desactualizado. La salida solo\npara archivos seleccionados se puede generar especificando nombres de archivo individuales.\n"

#: cmd/build.py:100
msgid "path to documentation source files"
msgstr "ruta a los archivos fuente de la documentación"

#: cmd/build.py:103
msgid "path to output directory"
msgstr "ruta al directorio de salida"

#: cmd/build.py:109
msgid ""
"(optional) a list of specific files to rebuild. Ignored if --write-all is "
"specified"
msgstr ""

#: cmd/build.py:114
msgid "general options"
msgstr "opciones generales"

#: cmd/build.py:121
msgid "builder to use (default: 'html')"
msgstr ""

#: cmd/build.py:131
msgid ""
"run in parallel with N processes, when possible. 'auto' uses the number of "
"CPU cores"
msgstr ""

#: cmd/build.py:140
msgid "write all files (default: only write new and changed files)"
msgstr "escribir todos los archivos (por defecto: solo escribir archivos nuevos y modificados)"

#: cmd/build.py:147
msgid "don't use a saved environment, always read all files"
msgstr "no usar un entorno guardado, siempre leer todos los archivos"

#: cmd/build.py:150
msgid "path options"
msgstr ""

#: cmd/build.py:157
msgid ""
"directory for doctree and environment files (default: OUTPUT_DIR/.doctrees)"
msgstr ""

#: cmd/build.py:166
msgid "directory for the configuration file (conf.py) (default: SOURCE_DIR)"
msgstr ""

#: cmd/build.py:175
msgid "use no configuration file, only use settings from -D options"
msgstr ""

#: cmd/build.py:184
msgid "override a setting in configuration file"
msgstr "sobreescribir un ajuste en el fichero de configuración"

#: cmd/build.py:193
msgid "pass a value into HTML templates"
msgstr "pasar un valor a la plantilla HTML"

#: cmd/build.py:202
msgid "define tag: include \"only\" blocks with TAG"
msgstr "define la etiqueta: incluye bloques \"only\" con TAG"

#: cmd/build.py:209
msgid "nitpicky mode: warn about all missing references"
msgstr ""

#: cmd/build.py:212
msgid "console output options"
msgstr "opciones de salida de consola"

#: cmd/build.py:219
msgid "increase verbosity (can be repeated)"
msgstr "aumentar la verbosidad (puede repetirse)"

#: cmd/build.py:226 ext/apidoc/_cli.py:66
msgid "no output on stdout, just warnings on stderr"
msgstr "sin salida en salida estándar, solo advertencias en los mensajes de error estándar"

#: cmd/build.py:233
msgid "no output at all, not even warnings"
msgstr "sin salida, ni siquiera advertencias"

#: cmd/build.py:241
msgid "do emit colored output (default: auto-detect)"
msgstr "emitir salida de color (predeterminado: detección automática)"

#: cmd/build.py:249
msgid "do not emit colored output (default: auto-detect)"
msgstr "no emite salida de color (predeterminado: detección automática)"

#: cmd/build.py:252
msgid "warning control options"
msgstr ""

#: cmd/build.py:258
msgid "write warnings (and errors) to given file"
msgstr "escribir avisos (y errores) al fichero indicado"

#: cmd/build.py:265
msgid "turn warnings into errors"
msgstr "convertir advertencias en errores"

#: cmd/build.py:273
msgid "show full traceback on exception"
msgstr "mostrar rastreo completo en excepción"

#: cmd/build.py:276
msgid "run Pdb on exception"
msgstr "ejecutar Pdb en excepción"

#: cmd/build.py:282
msgid "raise an exception on warnings"
msgstr ""

#: cmd/build.py:325
msgid "cannot combine -a option and filenames"
msgstr "no se puede combinar la opción -a y nombres de archivo"

#: cmd/build.py:357
#, python-format
msgid "cannot open warning file '%s': %s"
msgstr ""

#: cmd/build.py:376
msgid "-D option argument must be in the form name=value"
msgstr "argumento de la opción -D debe estar en la forma nombre=valor"

#: cmd/build.py:383
msgid "-A option argument must be in the form name=value"
msgstr "argumento de la opción -A debe estar en la forma nombre=valor"

#: themes/classic/layout.html:12 themes/classic/static/sidebar.js.jinja:51
msgid "Collapse sidebar"
msgstr "Contraer barra lateral"

#: themes/agogo/layout.html:29 themes/basic/globaltoc.html:2
#: themes/basic/localtoc.html:4 themes/scrolls/layout.html:32
msgid "Table of Contents"
msgstr "Tabla de contenido"

#: themes/agogo/layout.html:34 themes/basic/layout.html:130
#: themes/basic/search.html:3 themes/basic/search.html:15
msgid "Search"
msgstr "Búsqueda"

#: themes/agogo/layout.html:37 themes/basic/searchbox.html:8
#: themes/basic/searchfield.html:12
msgid "Go"
msgstr "Ir a"

#: themes/agogo/layout.html:81 themes/basic/sourcelink.html:7
msgid "Show Source"
msgstr "Mostrar el código"

#: themes/haiku/layout.html:16
msgid "Contents"
msgstr "Contenidos"

#: themes/basic/opensearch.xml:4
#, python-format
msgid "Search %(docstitle)s"
msgstr "Buscar en %(docstitle)s"

#: themes/basic/defindex.html:4
msgid "Overview"
msgstr "Resumen"

#: themes/basic/defindex.html:8
msgid "Welcome! This is"
msgstr "¡Bienvenido! Este es"

#: themes/basic/defindex.html:9
msgid "the documentation for"
msgstr "la documentación para"

#: themes/basic/defindex.html:10
msgid "last updated"
msgstr "actualizado por última vez el"

#: themes/basic/defindex.html:13
msgid "Indices and tables:"
msgstr "Índices y tablas:"

#: themes/basic/defindex.html:16
msgid "Complete Table of Contents"
msgstr "Índice de contenidos completo"

#: themes/basic/defindex.html:17
msgid "lists all sections and subsections"
msgstr "muestra todas las secciones y subsecciones"

#: domains/std/__init__.py:773 domains/std/__init__.py:786
#: themes/basic/defindex.html:18
msgid "Search Page"
msgstr "Página de Búsqueda"

#: themes/basic/defindex.html:19
msgid "search this documentation"
msgstr "buscar en esta documentación"

#: themes/basic/defindex.html:21
msgid "Global Module Index"
msgstr "Índice Global de Módulos"

#: themes/basic/defindex.html:22
msgid "quick access to all modules"
msgstr "acceso rápido a todos los módulos"

#: builders/html/__init__.py:507 themes/basic/defindex.html:23
msgid "General Index"
msgstr "Índice General"

#: themes/basic/defindex.html:24
msgid "all functions, classes, terms"
msgstr "todas las funciones, clases, términos"

#: themes/basic/sourcelink.html:4
msgid "This Page"
msgstr "Esta página"

#: themes/basic/genindex-single.html:26
#, python-format
msgid "Index &#x2013; %(key)s"
msgstr ""

#: themes/basic/genindex-single.html:54 themes/basic/genindex-split.html:16
#: themes/basic/genindex-split.html:30 themes/basic/genindex.html:65
msgid "Full index on one page"
msgstr "Índice completo en una página"

#: themes/basic/searchbox.html:4
msgid "Quick search"
msgstr "Búsqueda rápida"

#: themes/basic/genindex-split.html:8
msgid "Index pages by letter"
msgstr "Índice alfabético de páginas"

#: themes/basic/genindex-split.html:17
msgid "can be huge"
msgstr "puede ser muy grande"

#: themes/basic/relations.html:4
msgid "Previous topic"
msgstr "Tema anterior"

#: themes/basic/relations.html:6
msgid "previous chapter"
msgstr "capítulo anterior"

#: themes/basic/relations.html:11
msgid "Next topic"
msgstr "Próximo tema"

#: themes/basic/relations.html:13
msgid "next chapter"
msgstr "próximo capítulo"

#: themes/basic/layout.html:18
msgid "Navigation"
msgstr "Navegación"

#: themes/basic/layout.html:115
#, python-format
msgid "Search within %(docstitle)s"
msgstr "Buscar en %(docstitle)s"

#: themes/basic/layout.html:124
msgid "About these documents"
msgstr "Sobre este documento"

#: themes/basic/layout.html:133 themes/basic/layout.html:177
#: themes/basic/layout.html:179
msgid "Copyright"
msgstr "Copyright"

#: themes/basic/layout.html:183 themes/basic/layout.html:189
#, python-format
msgid "&#169; %(copyright_prefix)s %(copyright)s."
msgstr ""

#: themes/basic/layout.html:201
#, python-format
msgid "Last updated on %(last_updated)s."
msgstr "Actualizado por última vez en %(last_updated)s."

#: themes/basic/layout.html:204
#, python-format
msgid ""
"Created using <a href=\"https://www.sphinx-doc.org/\">Sphinx</a> "
"%(sphinx_version)s."
msgstr "Creado usando <a href=\"https://www.sphinx-doc.org/\">Sphinx</a> %(sphinx_version)s."

#: themes/basic/search.html:20
msgid ""
"Please activate JavaScript to enable the search\n"
"    functionality."
msgstr "Por favor, active JavaScript para habilitar la funcionalidad\n    de búsqueda."

#: themes/basic/search.html:28
msgid ""
"Searching for multiple words only shows matches that contain\n"
"    all words."
msgstr "La búsqueda de varias palabras solo muestra coincidencias que contienen\n    todas las palabras."

#: themes/basic/search.html:35
msgid "search"
msgstr "buscar"

#: themes/basic/static/sphinx_highlight.js:112
msgid "Hide Search Matches"
msgstr "Ocultar coincidencias de la búsqueda"

#: themes/basic/static/searchtools.js:117
msgid "Search Results"
msgstr "Resultados de la búsqueda"

#: themes/basic/static/searchtools.js:119
msgid ""
"Your search did not match any documents. Please make sure that all words are"
" spelled correctly and that you've selected enough categories."
msgstr "Su búsqueda no coincide con ningún documentos. Por favor, asegúrese de que todas las palabras estén correctamente escritas y que usted allá seleccionado las suficientes categorías."

#: themes/basic/static/searchtools.js:123
#, python-brace-format
msgid "Search finished, found one page matching the search query."
msgid_plural ""
"Search finished, found ${resultCount} pages matching the search query."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: themes/basic/static/searchtools.js:253
msgid "Searching"
msgstr "Buscando"

#: themes/basic/static/searchtools.js:270
msgid "Preparing search..."
msgstr "Preparando búsqueda..."

#: themes/basic/static/searchtools.js:474
msgid ", in "
msgstr ", en "

#: themes/basic/changes/rstsource.html:5
#, python-format
msgid "%(filename)s &#8212; %(docstitle)s"
msgstr "%(filename)s &#8212; %(docstitle)s"

#: themes/basic/changes/frameset.html:5
#: themes/basic/changes/versionchanges.html:12
#, python-format
msgid "Changes in Version %(version)s &#8212; %(docstitle)s"
msgstr "Cambios en la versión %(version)s &#8212; %(docstitle)s"

#: themes/basic/changes/versionchanges.html:17
#, python-format
msgid "Automatically generated list of changes in version %(version)s"
msgstr "Lista de cambios generada automáticamente en la versión %(version)s"

#: themes/basic/changes/versionchanges.html:18
msgid "Library changes"
msgstr "Cambios en la biblioteca"

#: themes/basic/changes/versionchanges.html:23
msgid "C API changes"
msgstr "Cambios en la API C"

#: themes/basic/changes/versionchanges.html:25
msgid "Other changes"
msgstr "Otros cambios"

#: themes/classic/static/sidebar.js.jinja:42
msgid "Expand sidebar"
msgstr "Expandir barra lateral"

#: domains/python/_annotations.py:529
msgid "Positional-only parameter separator (PEP 570)"
msgstr ""

#: domains/python/_annotations.py:540
msgid "Keyword-only parameters separator (PEP 3102)"
msgstr ""

#: domains/python/__init__.py:113 domains/python/__init__.py:278
#, python-format
msgid "%s() (in module %s)"
msgstr "%s() (en el módulo %s)"

#: domains/python/__init__.py:180 domains/python/__init__.py:374
#: domains/python/__init__.py:434 domains/python/__init__.py:474
#, python-format
msgid "%s (in module %s)"
msgstr "%s (en el módulo %s)"

#: domains/python/__init__.py:182
#, python-format
msgid "%s (built-in variable)"
msgstr "%s (variable incorporada)"

#: domains/python/__init__.py:217
#, python-format
msgid "%s (built-in class)"
msgstr "%s (clase incorporada)"

#: domains/python/__init__.py:218
#, python-format
msgid "%s (class in %s)"
msgstr "%s (clase en %s)"

#: domains/python/__init__.py:283
#, python-format
msgid "%s() (%s class method)"
msgstr "%s() (método de clase de %s)"

#: domains/python/__init__.py:285
#, python-format
msgid "%s() (%s static method)"
msgstr "%s() (método estático de %s)"

#: domains/python/__init__.py:438
#, python-format
msgid "%s (%s property)"
msgstr "%s (%s propiedad)"

#: domains/python/__init__.py:478
#, python-format
msgid "%s (type alias in %s)"
msgstr ""

#: domains/python/__init__.py:638
msgid "Python Module Index"
msgstr "Índice de Módulos Python"

#: domains/python/__init__.py:639
msgid "modules"
msgstr "módulos"

#: domains/python/__init__.py:717
msgid "Deprecated"
msgstr "Obsoleto"

#: domains/python/__init__.py:743
msgid "exception"
msgstr "excepción"

#: domains/python/__init__.py:745
msgid "class method"
msgstr "método de la clase"

#: domains/python/__init__.py:746
msgid "static method"
msgstr "método estático"

#: domains/python/__init__.py:748
msgid "property"
msgstr "propiedad"

#: domains/python/__init__.py:749
msgid "type alias"
msgstr ""

#: domains/python/__init__.py:818
#, python-format
msgid ""
"duplicate object description of %s, other instance in %s, use :no-index: for"
" one of them"
msgstr ""

#: domains/python/__init__.py:978
#, python-format
msgid "more than one target found for cross-reference %r: %s"
msgstr "se encontró más de un objetivo para la referencia cruzada %r: %s"

#: domains/python/__init__.py:1052
msgid " (deprecated)"
msgstr " (obsoleto)"

#: domains/c/__init__.py:326 domains/cpp/__init__.py:483
#: domains/python/_object.py:190 ext/napoleon/docstring.py:974
msgid "Parameters"
msgstr "Parámetros"

#: domains/python/_object.py:206
msgid "Variables"
msgstr "Variables"

#: domains/python/_object.py:214
msgid "Raises"
msgstr "Muestra"

#: domains/cpp/__init__.py:159
msgid "Template Parameters"
msgstr "Parametros de Plantilla"

#: domains/cpp/__init__.py:302
#, python-format
msgid "%s (C++ %s)"
msgstr "%s (C++ %s)"

#: domains/cpp/__init__.py:392 domains/cpp/_symbol.py:942
#, python-format
msgid ""
"Duplicate C++ declaration, also defined at %s:%s.\n"
"Declaration is '.. cpp:%s:: %s'."
msgstr "Declaración de C++ duplicada, también definida en %s:%s.\nLa declaración es '.. cpp:%s:: %s'."

#: domains/c/__init__.py:333 domains/cpp/__init__.py:496
msgid "Return values"
msgstr "Valores devueltos"

#: domains/c/__init__.py:754 domains/cpp/__init__.py:940
msgid "union"
msgstr "unión"

#: domains/c/__init__.py:749 domains/cpp/__init__.py:942
msgid "member"
msgstr "miembro"

#: domains/c/__init__.py:757 domains/cpp/__init__.py:943
msgid "type"
msgstr "tipo"

#: domains/cpp/__init__.py:944
msgid "concept"
msgstr "concepto"

#: domains/c/__init__.py:755 domains/cpp/__init__.py:945
msgid "enum"
msgstr "enum"

#: domains/c/__init__.py:756 domains/cpp/__init__.py:946
msgid "enumerator"
msgstr "enumeración"

#: domains/c/__init__.py:760 domains/cpp/__init__.py:949
msgid "function parameter"
msgstr "parámetro de función"

#: domains/cpp/__init__.py:952
msgid "template parameter"
msgstr "parámetro de plantilla"

#: domains/c/__init__.py:211
#, python-format
msgid "%s (C %s)"
msgstr "%s (C %s)"

#: domains/c/__init__.py:277 domains/c/_symbol.py:557
#, python-format
msgid ""
"Duplicate C declaration, also defined at %s:%s.\n"
"Declaration is '.. c:%s:: %s'."
msgstr "Declaración de C duplicada, también definida en %s:%s.\nLa declaración es '.. c:%s:: %s'."

#: domains/c/__init__.py:750
msgid "variable"
msgstr "variable"

#: domains/c/__init__.py:752
msgid "macro"
msgstr "macro"

#: domains/c/__init__.py:753
msgid "struct"
msgstr "estructura"

#: domains/std/__init__.py:91 domains/std/__init__.py:111
#, python-format
msgid "environment variable; %s"
msgstr "variables de entorno; %s"

#: domains/std/__init__.py:119
#, python-format
msgid "%s; configuration value"
msgstr ""

#: domains/std/__init__.py:175
msgid "Type"
msgstr ""

#: domains/std/__init__.py:185
msgid "Default"
msgstr ""

#: domains/std/__init__.py:242
#, python-format
msgid ""
"Malformed option description %r, should look like \"opt\", \"-opt args\", \""
"--opt args\", \"/opt args\" or \"+opt args\""
msgstr "Descripción de la opción con formato incorrecto %r, debe verse como \"opt\", \"-opt args\", \"--opt args\", \"/opt args\" o \"+opt args\""

#: domains/std/__init__.py:319
#, python-format
msgid "%s command line option"
msgstr "opción de línea de comando %s"

#: domains/std/__init__.py:321
msgid "command line option"
msgstr "opción de línea de comando"

#: domains/std/__init__.py:461
msgid "glossary term must be preceded by empty line"
msgstr "el término del glosario debe ir precedido de una línea vacía"

#: domains/std/__init__.py:474
msgid "glossary terms must not be separated by empty lines"
msgstr "los términos del glosario no deben estar separados por líneas vacías"

#: domains/std/__init__.py:486 domains/std/__init__.py:504
msgid "glossary seems to be misformatted, check indentation"
msgstr "el glosario parece estar mal formateado, verifique la sangría"

#: domains/std/__init__.py:729
msgid "glossary term"
msgstr "termino de glosario"

#: domains/std/__init__.py:730
msgid "grammar token"
msgstr "gramática simbólica"

#: domains/std/__init__.py:731
msgid "reference label"
msgstr "etiqueta de referencia"

#: domains/std/__init__.py:733
msgid "environment variable"
msgstr "variables de entorno"

#: domains/std/__init__.py:734
msgid "program option"
msgstr "opción de programa"

#: domains/std/__init__.py:735
msgid "document"
msgstr "documento"

#: domains/std/__init__.py:772 domains/std/__init__.py:785
msgid "Module Index"
msgstr "Índice de Módulos"

#: domains/std/__init__.py:857
#, python-format
msgid "duplicate %s description of %s, other instance in %s"
msgstr "duplicada %s descripción de %s, otra instancia en %s"

#: domains/std/__init__.py:1113
msgid "numfig is disabled. :numref: is ignored."
msgstr "numfig está deshabilitado. :numref: se ignora."

#: domains/std/__init__.py:1124
#, python-format
msgid "Failed to create a cross reference. Any number is not assigned: %s"
msgstr "Error al crear una referencia cruzada. No se asigna ningún número: %s"

#: domains/std/__init__.py:1138
#, python-format
msgid "the link has no caption: %s"
msgstr "el enlace no tiene subtítulo: %s"

#: domains/std/__init__.py:1153
#, python-format
msgid "invalid numfig_format: %s (%r)"
msgstr "inválido numfig_format: %s (%r)"

#: domains/std/__init__.py:1157
#, python-format
msgid "invalid numfig_format: %s"
msgstr "inválido numfig_format: %s"

#: domains/std/__init__.py:1453
#, python-format
msgid "undefined label: %r"
msgstr "etiqueta indefinida: %r"

#: domains/std/__init__.py:1456
#, python-format
msgid "Failed to create a cross reference. A title or caption not found: %r"
msgstr "No se pudo crear una referencia cruzada. Un título o subtítulo no encontrado: %r"

#: environment/adapters/toctree.py:324
#, python-format
msgid "circular toctree references detected, ignoring: %s <- %s"
msgstr "referencias circulares de toctree detectadas, ignorando: %s <- %s"

#: environment/adapters/toctree.py:349
#, python-format
msgid ""
"toctree contains reference to document %r that doesn't have a title: no link"
" will be generated"
msgstr "toctree contiene una referencia al documento %r que no tiene título: no se generará ningún enlace"

#: environment/adapters/toctree.py:364
#, python-format
msgid "toctree contains reference to non-included document %r"
msgstr "el árbol de la tabla de contenido contiene una referencia a un documento no incluido %r"

#: environment/adapters/toctree.py:367
#, python-format
msgid "toctree contains reference to non-existing document %r"
msgstr ""

#: environment/adapters/indexentries.py:123
#, python-format
msgid "see %s"
msgstr "ver %s"

#: environment/adapters/indexentries.py:133
#, python-format
msgid "see also %s"
msgstr "ver también %s"

#: environment/adapters/indexentries.py:141
#, python-format
msgid "unknown index entry type %r"
msgstr "tipo de entrada de índice desconocido %r"

#: environment/adapters/indexentries.py:268
#: templates/latex/sphinxmessages.sty.jinja:11
msgid "Symbols"
msgstr "Símbolos"

#: environment/collectors/asset.py:98
#, python-format
msgid "image file not readable: %s"
msgstr "archivo de imagen no legible: %s"

#: environment/collectors/asset.py:126
#, python-format
msgid "image file %s not readable: %s"
msgstr "archivo de imagen %s no legible: %s"

#: environment/collectors/asset.py:163
#, python-format
msgid "download file not readable: %s"
msgstr "el archivo de descarga no es legible: %s"

#: environment/collectors/toctree.py:259
#, python-format
msgid "%s is already assigned section numbers (nested numbered toctree?)"
msgstr "%s ya tiene asignados números de sección (¿número de árbol anidado?)"

#: _cli/util/errors.py:190
msgid "Interrupted!"
msgstr "¡Interrumpido!"

#: _cli/util/errors.py:194
msgid "reStructuredText markup error!"
msgstr ""

#: _cli/util/errors.py:200
msgid "Encoding error!"
msgstr ""

#: _cli/util/errors.py:203
msgid "Recursion error!"
msgstr ""

#: _cli/util/errors.py:207
msgid ""
"This can happen with very large or deeply nested source files. You can "
"carefully increase the default Python recursion limit of 1,000 in conf.py "
"with e.g.:"
msgstr ""

#: _cli/util/errors.py:227
msgid "Starting debugger:"
msgstr ""

#: _cli/util/errors.py:235
msgid "The full traceback has been saved in:"
msgstr ""

#: _cli/util/errors.py:240
msgid ""
"To report this error to the developers, please open an issue at "
"<https://github.com/sphinx-doc/sphinx/issues/>. Thanks!"
msgstr ""

#: _cli/util/errors.py:246
msgid ""
"Please also report this if it was a user error, so that a better error "
"message can be provided next time."
msgstr "Por favor, informe también esto si fue un error del usuario, de modo que la próxima vez se pueda proporcionar un mejor mensaje de error."

#: transforms/post_transforms/__init__.py:88
msgid ""
"Could not determine the fallback text for the cross-reference. Might be a "
"bug."
msgstr "No se pudo determinar el texto alternativo para la referencia cruzada. Podría ser un error."

#: transforms/post_transforms/__init__.py:237
#, python-format
msgid "more than one target found for 'any' cross-reference %r: could be %s"
msgstr "más de un objetivo destino encontrado para 'cualquier' referencia cruzada %r: podría ser %s"

#: transforms/post_transforms/__init__.py:299
#, python-format
msgid "%s:%s reference target not found: %s"
msgstr "%s:%s objetivo de referencia no encontrado: %s"

#: transforms/post_transforms/__init__.py:305
#, python-format
msgid "%r reference target not found: %s"
msgstr "%r objetivo de referencia no encontrado: %s"

#: transforms/post_transforms/images.py:79
#, python-format
msgid "Could not fetch remote image: %s [%s]"
msgstr "No se pudo recuperar la imagen remota: %s [%s]"

#: transforms/post_transforms/images.py:96
#, python-format
msgid "Could not fetch remote image: %s [%d]"
msgstr "No se pudo recuperar la imagen remota: %s [%d]"

#: transforms/post_transforms/images.py:143
#, python-format
msgid "Unknown image format: %s..."
msgstr "Formato de imagen desconocido: %s..."

#: builders/html/__init__.py:113
#, python-format
msgid "The HTML pages are in %(outdir)s."
msgstr "Las páginas HTML están en %(outdir)s."

#: builders/html/__init__.py:348
#, python-format
msgid "Failed to read build info file: %r"
msgstr "Error al leer la información de compilación del fichero: %r"

#: builders/html/__init__.py:364
msgid "build_info mismatch, copying .buildinfo to .buildinfo.bak"
msgstr ""

#: builders/html/__init__.py:366
msgid "building [html]: "
msgstr ""

#: builders/html/__init__.py:383
#, python-format
msgid ""
"template %s has been changed since the previous build, all docs will be "
"rebuilt"
msgstr ""

#: builders/html/__init__.py:507
msgid "index"
msgstr "índice"

#: builders/html/__init__.py:560
#, python-format
msgid "Logo of %s"
msgstr ""

#: builders/html/__init__.py:589
msgid "next"
msgstr "siguiente"

#: builders/html/__init__.py:598
msgid "previous"
msgstr "anterior"

#: builders/html/__init__.py:696
msgid "generating indices"
msgstr "generando índices"

#: builders/html/__init__.py:711
msgid "writing additional pages"
msgstr "escribiendo páginas adicionales"

#: builders/html/__init__.py:794
#, python-format
msgid "cannot copy image file '%s': %s"
msgstr ""

#: builders/html/__init__.py:806
msgid "copying downloadable files... "
msgstr "copiando archivos descargables... "

#: builders/html/__init__.py:818
#, python-format
msgid "cannot copy downloadable file %r: %s"
msgstr "no se puede copiar archivo descargable %r: %s"

#: builders/html/__init__.py:864
#, python-format
msgid "Failed to copy a file in the theme's 'static' directory: %s: %r"
msgstr ""

#: builders/html/__init__.py:882
#, python-format
msgid "Failed to copy a file in html_static_file: %s: %r"
msgstr "Error al copiar un archivo en html_static_file: %s: %r"

#: builders/html/__init__.py:917
msgid "copying static files"
msgstr "copiar archivos estáticos"

#: builders/html/__init__.py:934
#, python-format
msgid "cannot copy static file %r"
msgstr "no se puede copiar archivo estático %r"

#: builders/html/__init__.py:939
msgid "copying extra files"
msgstr "copiando archivos extras"

#: builders/html/__init__.py:949
#, python-format
msgid "cannot copy extra file %r"
msgstr "no se puede copiar archivo extra %r"

#: builders/html/__init__.py:955
#, python-format
msgid "Failed to write build info file: %r"
msgstr "Error al escribir el archivo de información de compilación: %r"

#: builders/html/__init__.py:1005
msgid ""
"search index couldn't be loaded, but not all documents will be built: the "
"index will be incomplete."
msgstr "no se pudo cargar el índice de búsqueda, pero no se crearán todos los documentos: el índice estará incompleto."

#: builders/html/__init__.py:1052
#, python-format
msgid "page %s matches two patterns in html_sidebars: %r and %r"
msgstr "página %s coincide con dos patrones en html_sidebars: %r y %r"

#: builders/html/__init__.py:1216
#, python-format
msgid ""
"a Unicode error occurred when rendering the page %s. Please make sure all "
"config values that contain non-ASCII content are Unicode strings."
msgstr "Se produjo un error Unicode al representar la página %s. Asegúrese de que todos los valores de configuración que contengan contenido que no sea ASCII sean cadenas Unicode."

#: builders/html/__init__.py:1224
#, python-format
msgid ""
"An error happened in rendering the page %s.\n"
"Reason: %r"
msgstr "Ha ocurrido un error al renderizar la pagina %s.\nRazón: %r"

#: builders/html/__init__.py:1257
msgid "dumping object inventory"
msgstr "volcar inventario de objetos"

#: builders/html/__init__.py:1265
#, python-format
msgid "dumping search index in %s"
msgstr "volcar el índice de búsqueda en %s"

#: builders/html/__init__.py:1308
#, python-format
msgid "invalid js_file: %r, ignored"
msgstr "js_file inválido: %r, ignorado"

#: builders/html/__init__.py:1342
msgid "Many math_renderers are registered. But no math_renderer is selected."
msgstr "Muchos math_renderers están registrados. Pero no se ha seleccionado math_renderer."

#: builders/html/__init__.py:1346
#, python-format
msgid "Unknown math_renderer %r is given."
msgstr "Desconocido math_renderer %r es dado."

#: builders/html/__init__.py:1360
#, python-format
msgid "html_extra_path entry %r is placed inside outdir"
msgstr "entrada html_extra_path %r se coloca dentro de outdir"

#: builders/html/__init__.py:1365
#, python-format
msgid "html_extra_path entry %r does not exist"
msgstr "entrada html_extra_path %r no existe"

#: builders/html/__init__.py:1380
#, python-format
msgid "html_static_path entry %r is placed inside outdir"
msgstr "entrada html_static_path %r se coloca dentro de outdir"

#: builders/html/__init__.py:1385
#, python-format
msgid "html_static_path entry %r does not exist"
msgstr "entrada html_static_path %r no existe"

#: builders/html/__init__.py:1396 builders/latex/__init__.py:504
#, python-format
msgid "logo file %r does not exist"
msgstr "archivo de logo %r no existe"

#: builders/html/__init__.py:1407
#, python-format
msgid "favicon file %r does not exist"
msgstr "el archivo %r usado para el favicon no existe"

#: builders/html/__init__.py:1420
#, python-format
msgid ""
"Values in 'html_sidebars' must be a list of strings. At least one pattern "
"has a string value: %s. Change to `html_sidebars = %r`."
msgstr ""

#: builders/html/__init__.py:1433
msgid ""
"HTML 4 is no longer supported by Sphinx. (\"html4_writer=True\" detected in "
"configuration options)"
msgstr ""

#: builders/html/__init__.py:1449
#, python-format
msgid "%s %s documentation"
msgstr "documentación de %s - %s"

#: builders/html/_build_info.py:32
msgid "failed to read broken build info file (unknown version)"
msgstr ""

#: builders/html/_build_info.py:36
msgid "failed to read broken build info file (missing config entry)"
msgstr ""

#: builders/html/_build_info.py:39
msgid "failed to read broken build info file (missing tags entry)"
msgstr ""

#: builders/latex/__init__.py:118
#, python-format
msgid "The LaTeX files are in %(outdir)s."
msgstr "Los archivos LaTeX están en %(outdir)s."

#: builders/latex/__init__.py:121
msgid ""
"\n"
"Run 'make' in that directory to run these through (pdf)latex\n"
"(use `make latexpdf' here to do that automatically)."
msgstr "\nEjecuta el comando 'make' en este directorio para compilarlos usando (pdf)latex\n(usa el comando 'make latexpdf' aquí para hacer esto automáticamente)."

#: builders/latex/__init__.py:159
msgid "no \"latex_documents\" config value found; no documents will be written"
msgstr "no se encontró el valor de configuración \"latex_documents\"; no se escribirán documentos"

#: builders/latex/__init__.py:170
#, python-format
msgid "\"latex_documents\" config value references unknown document %s"
msgstr "valor de configuración \"latex_documents\" hace referencia a un documento desconocido %s"

#: builders/latex/__init__.py:209 templates/latex/latex.tex.jinja:91
msgid "Release"
msgstr "Versión"

#: builders/latex/__init__.py:428
msgid "copying TeX support files"
msgstr "copiando archivos de soporte TeX"

#: builders/latex/__init__.py:465
msgid "copying additional files"
msgstr "copiando archivos adicionales"

#: builders/latex/__init__.py:536
#, python-format
msgid "Unknown configure key: latex_elements[%r], ignored."
msgstr "Clave de configuración desconocida: latex_elements[%r], ignorada."

#: builders/latex/__init__.py:544
#, python-format
msgid "Unknown theme option: latex_theme_options[%r], ignored."
msgstr "Opción de tema desconocida: latex_theme_options[%r], ignorado."

#: builders/latex/transforms.py:120
msgid "Failed to get a docname!"
msgstr ""

#: builders/latex/transforms.py:121
#, python-format
msgid "Failed to get a docname for source %r!"
msgstr ""

#: builders/latex/transforms.py:487
#, python-format
msgid "No footnote was found for given reference node %r"
msgstr ""

#: builders/latex/theming.py:88
#, python-format
msgid "%r doesn't have \"theme\" setting"
msgstr "%r no tiene configuración de \"tema\""

#: builders/latex/theming.py:91
#, python-format
msgid "%r doesn't have \"%s\" setting"
msgstr "%r no tiene configuración de \"%s\""

#: templates/latex/longtable.tex.jinja:52
#: templates/latex/sphinxmessages.sty.jinja:8
msgid "continued from previous page"
msgstr "proviene de la página anterior"

#: templates/latex/longtable.tex.jinja:63
#: templates/latex/sphinxmessages.sty.jinja:9
msgid "continues on next page"
msgstr "continúe en la próxima página"

#: templates/latex/sphinxmessages.sty.jinja:10
msgid "Non-alphabetical"
msgstr "No alfabético"

#: templates/latex/sphinxmessages.sty.jinja:12
msgid "Numbers"
msgstr "Números"

#: templates/latex/sphinxmessages.sty.jinja:13
msgid "page"
msgstr "página"

#: ext/napoleon/__init__.py:356 ext/napoleon/docstring.py:940
msgid "Keyword Arguments"
msgstr "Argumentos de palabras clave"

#: ext/napoleon/docstring.py:176
#, python-format
msgid "invalid value set (missing closing brace): %s"
msgstr "conjunto de valores no válidos (falta la llave de cierre): %s"

#: ext/napoleon/docstring.py:183
#, python-format
msgid "invalid value set (missing opening brace): %s"
msgstr "conjunto de valor no válido (falta llave de apertura): %s"

#: ext/napoleon/docstring.py:190
#, python-format
msgid "malformed string literal (missing closing quote): %s"
msgstr "literal de cadena con formato incorrecto (falta la comilla de cierre): %s"

#: ext/napoleon/docstring.py:197
#, python-format
msgid "malformed string literal (missing opening quote): %s"
msgstr "literal de cadena con formato incorrecto (falta la comilla de apertura): %s"

#: ext/napoleon/docstring.py:895
msgid "Example"
msgstr "Ejemplo"

#: ext/napoleon/docstring.py:896
msgid "Examples"
msgstr "Ejemplos"

#: ext/napoleon/docstring.py:956
msgid "Notes"
msgstr "Notas"

#: ext/napoleon/docstring.py:965
msgid "Other Parameters"
msgstr "Otros parámetros"

#: ext/napoleon/docstring.py:1001
msgid "Receives"
msgstr "Recibe"

#: ext/napoleon/docstring.py:1005
msgid "References"
msgstr "Referencias"

#: ext/napoleon/docstring.py:1037
msgid "Warns"
msgstr "Avisos"

#: ext/napoleon/docstring.py:1041
msgid "Yields"
msgstr "Campos"

#: ext/autosummary/__init__.py:284
#, python-format
msgid "autosummary references excluded document %r. Ignored."
msgstr "referencias autosummary excluidas documento %r. Ignorado."

#: ext/autosummary/__init__.py:288
#, python-format
msgid ""
"autosummary: stub file not found %r. Check your autosummary_generate "
"setting."
msgstr "autosummary: no se encontró el archivo stub %r. Verifique su configuración de autosummary_generate."

#: ext/autosummary/__init__.py:309
msgid "A captioned autosummary requires :toctree: option. ignored."
msgstr "Un resumen automático con subtítulos requiere la opción :toctree: ignorado."

#: ext/autosummary/__init__.py:384
#, python-format
msgid ""
"autosummary: failed to import %s.\n"
"Possible hints:\n"
"%s"
msgstr "autosummary: no se pudo importar %s.\nPosibles pistas:\n%s"

#: ext/autosummary/__init__.py:404
#, python-format
msgid "failed to parse name %s"
msgstr "fallo al analizar el nombre %s"

#: ext/autosummary/__init__.py:412
#, python-format
msgid "failed to import object %s"
msgstr "fallo al importar el objeto %s"

#: ext/autosummary/__init__.py:730
#, python-format
msgid ""
"Summarised items should not include the current module. Replace %r with %r."
msgstr ""

#: ext/autosummary/__init__.py:927
#, python-format
msgid "autosummary_generate: file not found: %s"
msgstr "autosummary_generate: archivo no encontrado: %s"

#: ext/autosummary/__init__.py:937
msgid ""
"autosummary generates .rst files internally. But your source_suffix does not"
" contain .rst. Skipped."
msgstr ""

#: ext/autosummary/generate.py:232 ext/autosummary/generate.py:450
#, python-format
msgid ""
"autosummary: failed to determine %r to be documented, the following exception was raised:\n"
"%s"
msgstr "autosummary: no se pudo determinar %r que se documentará, se produjo la siguiente excepción:\n%s"

#: ext/autosummary/generate.py:588
#, python-format
msgid "[autosummary] generating autosummary for: %s"
msgstr "[autosummary] generar autosummary para: %s"

#: ext/autosummary/generate.py:592
#, python-format
msgid "[autosummary] writing to %s"
msgstr "[autosummary] escribiendo a %s"

#: ext/autosummary/generate.py:637
#, python-format
msgid ""
"[autosummary] failed to import %s.\n"
"Possible hints:\n"
"%s"
msgstr "[autosummary] no se pudo importar %s.\nPosibles pistas:\n%s"

#: ext/autosummary/generate.py:836
msgid ""
"\n"
"Generate ReStructuredText using autosummary directives.\n"
"\n"
"sphinx-autogen is a frontend to sphinx.ext.autosummary.generate. It generates\n"
"the reStructuredText files from the autosummary directives contained in the\n"
"given input files.\n"
"\n"
"The format of the autosummary directive is documented in the\n"
"``sphinx.ext.autosummary`` Python module and can be read using::\n"
"\n"
"  pydoc sphinx.ext.autosummary\n"
msgstr "\nGenere ReStructuredText usando directivas de resumen automático \"autosummary\".\n\nsphinx-autogen es una interfaz para sphinx.ext.autosummary.generate. Genera\nlos archivos reStructuredText de las directivas autosummary contenidas en el\nlos archivos de entrada dados.\n\nEl formato de la directiva autosummary está documentado en el módulo Python\n``sphinx.ext.autosummary`` y se puede leer usando el siguiente comando::\n\n  pydoc sphinx.ext.autosummary\n"

#: ext/autosummary/generate.py:858
msgid "source files to generate rST files for"
msgstr "archivos fuente para generar archivos rST para"

#: ext/autosummary/generate.py:866
msgid "directory to place all output in"
msgstr "directorio para colocar toda la salida en"

#: ext/autosummary/generate.py:874
#, python-format
msgid "default suffix for files (default: %(default)s)"
msgstr "sufijo predeterminado para archivos (predeterminado: %(default)s)"

#: ext/autosummary/generate.py:882
#, python-format
msgid "custom template directory (default: %(default)s)"
msgstr "directorio de plantillas personalizadas (predeterminado: %(default)s)"

#: ext/autosummary/generate.py:890
#, python-format
msgid "document imported members (default: %(default)s)"
msgstr "documento importados miembros (predeterminado: %(default)s)"

#: ext/autosummary/generate.py:899
#, python-format
msgid ""
"document exactly the members in module __all__ attribute. (default: "
"%(default)s)"
msgstr "documentar exactamente los miembros en module __all__ attribute. (por defecto: %(default)s)"

#: ext/apidoc/_cli.py:178 ext/autosummary/generate.py:909
msgid "Remove existing files in the output directory that were not generated"
msgstr ""

#: ext/apidoc/_shared.py:29 ext/autosummary/generate.py:944
#, python-format
msgid "Failed to remove %s: %s"
msgstr ""

#: ext/apidoc/_cli.py:28
msgid ""
"\n"
"Look recursively in <MODULE_PATH> for Python modules and packages and create\n"
"one reST file with automodule directives per package in the <OUTPUT_PATH>.\n"
"\n"
"The <EXCLUDE_PATTERN>s can be file and/or directory patterns that will be\n"
"excluded from generation.\n"
"\n"
"Note: By default this script will not overwrite already created files."
msgstr "\nMire recursivamente en <MODULE_PATH> para módulos y paquetes de Python y cree\nun archivo reST con directivas automodule por paquete en el <OUTPUT_PATH>.\n\nLos <EXCLUDE_PATTERN>s pueden ser patrones de archivo y/o directorio que serán\nexcluidos de la generación.\n\nNota: Por defecto, este script no sobrescribirá los archivos ya creados."

#: ext/apidoc/_cli.py:45
msgid "path to module to document"
msgstr "ruta al módulo al documento"

#: ext/apidoc/_cli.py:50
msgid ""
"fnmatch-style file and/or directory patterns to exclude from generation"
msgstr "archivo de estilo fnmatch y/o patrones de directorio para excluir de la generación"

#: ext/apidoc/_cli.py:60
msgid "directory to place all output"
msgstr "directorio para colocar toda la salida"

#: ext/apidoc/_cli.py:75
msgid "maximum depth of submodules to show in the TOC (default: 4)"
msgstr "rofundidad máxima de submódulos para mostrar en la tabla de contenido (predeterminado: 4)"

#: ext/apidoc/_cli.py:82
msgid "overwrite existing files"
msgstr "sobreescribir archivos existentes"

#: ext/apidoc/_cli.py:91
msgid ""
"follow symbolic links. Powerful when combined with "
"collective.recipe.omelette."
msgstr "seguir enlaces simbólicos. Potente cuando se combina con el paquete collective.recipe.omelette."

#: ext/apidoc/_cli.py:99
msgid "run the script without creating files"
msgstr "ejecutar la rutina sin crear archivos"

#: ext/apidoc/_cli.py:106
msgid "put documentation for each module on its own page"
msgstr "poner documentación para cada módulo en su propia página"

#: ext/apidoc/_cli.py:113
msgid "include \"_private\" modules"
msgstr "incluir \"_private\" en módulos"

#: ext/apidoc/_cli.py:120
msgid "filename of table of contents (default: modules)"
msgstr "nombre de archivo de la tabla de contenido (predeterminado: módulos)"

#: ext/apidoc/_cli.py:127
msgid "don't create a table of contents file"
msgstr "no crear un archivo de tabla de contenido"

#: ext/apidoc/_cli.py:135
msgid ""
"don't create headings for the module/package packages (e.g. when the "
"docstrings already contain them)"
msgstr "no cree encabezados para los paquetes de módulos/paquetes (por ejemplo, cuando las cadenas de documentación docstrings ya los contienen)"

#: ext/apidoc/_cli.py:145
msgid "put module documentation before submodule documentation"
msgstr "poner la documentación del módulo antes de la documentación del submódulo"

#: ext/apidoc/_cli.py:152
msgid ""
"interpret module paths according to PEP-0420 implicit namespaces "
"specification"
msgstr "interpretar las rutas del módulo de acuerdo con la especificación de espacios de nombres implícitos en la PEP-0420"

#: ext/apidoc/_cli.py:160
msgid ""
"Comma-separated list of options to pass to automodule directive (or use "
"SPHINX_APIDOC_OPTIONS)."
msgstr ""

#: ext/apidoc/_cli.py:170
msgid "file suffix (default: rst)"
msgstr "sufijo de archivo (por defecto: rst)"

#: ext/apidoc/_cli.py:186
msgid "generate a full project with sphinx-quickstart"
msgstr "generar un proyecto completo con sphinx-quickstart"

#: ext/apidoc/_cli.py:193
msgid "append module_path to sys.path, used when --full is given"
msgstr "agregue module_path al sys.path, que se usa cuando se da el parámetro --full"

#: ext/apidoc/_cli.py:200
msgid "project name (default: root module name)"
msgstr "nombre del proyecto (predeterminado: nombre del módulo raíz)"

#: ext/apidoc/_cli.py:207
msgid "project author(s), used when --full is given"
msgstr "autor(es) del proyecto, utilizado cuando se da el parámetro --full"

#: ext/apidoc/_cli.py:214
msgid "project version, used when --full is given"
msgstr "versión del proyecto, utilizado cuando se da el parámetro --full"

#: ext/apidoc/_cli.py:222
msgid "project release, used when --full is given, defaults to --doc-version"
msgstr "lanzamiento del proyecto, utilizado cuando se da el parámetro --full, por defecto es --doc-version"

#: ext/apidoc/_cli.py:226
msgid "extension options"
msgstr "opciones de extensión"

#: ext/apidoc/_cli.py:232
msgid "enable arbitrary extensions, used when --full is given"
msgstr ""

#: ext/apidoc/_cli.py:240
#, python-format
msgid "enable %s extension, used when --full is given"
msgstr ""

#: ext/apidoc/_cli.py:291
#, python-format
msgid "%s is not a directory."
msgstr "%s no es un directorio."

#: ext/apidoc/_extension.py:50
msgid "Running apidoc"
msgstr ""

#: ext/apidoc/_extension.py:102
#, python-format
msgid "apidoc_modules item %i must be a dict"
msgstr ""

#: ext/apidoc/_extension.py:110
#, python-format
msgid "apidoc_modules item %i must have a 'path' key"
msgstr ""

#: ext/apidoc/_extension.py:115
#, python-format
msgid "apidoc_modules item %i 'path' must be a string"
msgstr ""

#: ext/apidoc/_extension.py:121
#, python-format
msgid "apidoc_modules item %i 'path' is not an existing folder: %s"
msgstr ""

#: ext/apidoc/_extension.py:133
#, python-format
msgid "apidoc_modules item %i must have a 'destination' key"
msgstr ""

#: ext/apidoc/_extension.py:140
#, python-format
msgid "apidoc_modules item %i 'destination' must be a string"
msgstr ""

#: ext/apidoc/_extension.py:147
#, python-format
msgid "apidoc_modules item %i 'destination' should be a relative path"
msgstr ""

#: ext/apidoc/_extension.py:157
#, python-format
msgid "apidoc_modules item %i cannot create destination directory: %s"
msgstr ""

#: ext/apidoc/_extension.py:178
#, python-format
msgid "apidoc_modules item %i '%s' must be an int"
msgstr ""

#: ext/apidoc/_extension.py:192
#, python-format
msgid "apidoc_modules item %i '%s' must be a boolean"
msgstr ""

#: ext/apidoc/_extension.py:210
#, python-format
msgid "apidoc_modules item %i has unexpected keys: %s"
msgstr ""

#: ext/apidoc/_extension.py:247
#, python-format
msgid "apidoc_modules item %i '%s' must be a sequence"
msgstr ""

#: ext/apidoc/_extension.py:256
#, python-format
msgid "apidoc_modules item %i '%s' must contain strings"
msgstr ""

#: ext/apidoc/_generate.py:69
#, python-format
msgid "Would create file %s."
msgstr "Debería crear archivo %s."

#: ext/intersphinx/_resolve.py:49
#, python-format
msgid "(in %s v%s)"
msgstr "(en %s versión %s)"

#: ext/intersphinx/_resolve.py:51
#, python-format
msgid "(in %s)"
msgstr "(en %s)"

#: ext/intersphinx/_resolve.py:108
#, python-format
msgid "inventory '%s': duplicate matches found for %s:%s"
msgstr ""

#: ext/intersphinx/_resolve.py:118
#, python-format
msgid "inventory '%s': multiple matches found for %s:%s"
msgstr ""

#: ext/intersphinx/_resolve.py:383
#, python-format
msgid "inventory for external cross-reference not found: %r"
msgstr ""

#: ext/intersphinx/_resolve.py:392
#, python-format
msgid "invalid external cross-reference suffix: %r"
msgstr ""

#: ext/intersphinx/_resolve.py:403
#, python-format
msgid "domain for external cross-reference not found: %r"
msgstr ""

#: ext/intersphinx/_resolve.py:619
#, python-format
msgid "external %s:%s reference target not found: %s"
msgstr "%s externo: destino de referencia %s no encontrado: %s"

#: ext/intersphinx/_load.py:60
#, python-format
msgid ""
"Invalid intersphinx project identifier `%r` in intersphinx_mapping. Project "
"identifiers must be non-empty strings."
msgstr ""

#: ext/intersphinx/_load.py:71
#, python-format
msgid ""
"Invalid value `%r` in intersphinx_mapping[%r]. Expected a two-element tuple "
"or list."
msgstr ""

#: ext/intersphinx/_load.py:82
#, python-format
msgid ""
"Invalid value `%r` in intersphinx_mapping[%r]. Values must be a (target URI,"
" inventory locations) pair."
msgstr ""

#: ext/intersphinx/_load.py:93
#, python-format
msgid ""
"Invalid target URI value `%r` in intersphinx_mapping[%r][0]. Target URIs "
"must be unique non-empty strings."
msgstr ""

#: ext/intersphinx/_load.py:102
#, python-format
msgid ""
"Invalid target URI value `%r` in intersphinx_mapping[%r][0]. Target URIs "
"must be unique (other instance in intersphinx_mapping[%r])."
msgstr ""

#: ext/intersphinx/_load.py:121
#, python-format
msgid ""
"Invalid inventory location value `%r` in intersphinx_mapping[%r][1]. "
"Inventory locations must be non-empty strings or None."
msgstr ""

#: ext/intersphinx/_load.py:131
msgid "Invalid `intersphinx_mapping` configuration (1 error)."
msgstr ""

#: ext/intersphinx/_load.py:134
#, python-format
msgid "Invalid `intersphinx_mapping` configuration (%s errors)."
msgstr ""

#: ext/intersphinx/_load.py:157
msgid "An invalid intersphinx_mapping entry was added after normalisation."
msgstr ""

#: ext/intersphinx/_load.py:261
#, python-format
msgid "loading intersphinx inventory '%s' from %s ..."
msgstr ""

#: ext/intersphinx/_load.py:287
msgid ""
"encountered some issues with some of the inventories, but they had working "
"alternatives:"
msgstr "encontró algunos problemas con algunos de los inventarios, pero tenían alternativas de trabajo:"

#: ext/intersphinx/_load.py:297
msgid "failed to reach any of the inventories with the following issues:"
msgstr "no se pudo llegar a ninguno de los inventarios con los siguientes problemas:"

#: ext/intersphinx/_load.py:361
#, python-format
msgid "intersphinx inventory has moved: %s -> %s"
msgstr "el inventario intersphinx se ha movido: %s -> %s"

#: ext/autodoc/__init__.py:150
#, python-format
msgid "invalid value for member-order option: %s"
msgstr "valor no válido para la opción de pedido de miembro: %s"

#: ext/autodoc/__init__.py:158
#, python-format
msgid "invalid value for class-doc-from option: %s"
msgstr "valor no válido para la opción class-doc-from: %s"

#: ext/autodoc/__init__.py:460
#, python-format
msgid "invalid signature for auto%s (%r)"
msgstr "firma inválida para auto%s (%r)"

#: ext/autodoc/__init__.py:579
#, python-format
msgid "error while formatting arguments for %s: %s"
msgstr "error al formatear argumentos para %s: %s"

#: ext/autodoc/__init__.py:898
#, python-format
msgid ""
"autodoc: failed to determine %s.%s (%r) to be documented, the following exception was raised:\n"
"%s"
msgstr "autodoc: no pudo determinar %s.%s (%r) para ser documentado, se planteó la siguiente excepción:\n%s"

#: ext/autodoc/__init__.py:1021
#, python-format
msgid ""
"don't know which module to import for autodocumenting %r (try placing a "
"\"module\" or \"currentmodule\" directive in the document, or giving an "
"explicit module name)"
msgstr "no sabe qué módulo importar para el autodocumento %r (intente colocar una directiva \"module\" o \"currentmodule\" en el documento o dar un nombre explícito al módulo)"

#: ext/autodoc/__init__.py:1080
#, python-format
msgid "A mocked object is detected: %r"
msgstr "Se detecta un objeto simulado: %r"

#: ext/autodoc/__init__.py:1103
#, python-format
msgid "error while formatting signature for %s: %s"
msgstr "error al formatear la firma para %s: %s"

#: ext/autodoc/__init__.py:1177
msgid "\"::\" in automodule name doesn't make sense"
msgstr "\"::\" en el nombre del automodule no tiene sentido"

#: ext/autodoc/__init__.py:1185
#, python-format
msgid "signature arguments or return annotation given for automodule %s"
msgstr "argumentos de firma o anotación de retorno dada para automodule %s"

#: ext/autodoc/__init__.py:1201
#, python-format
msgid ""
"__all__ should be a list of strings, not %r (in module %s) -- ignoring "
"__all__"
msgstr "__all__ debe ser una lista de cadenas, no %r (en el módulo %s) -- ignorando __all__"

#: ext/autodoc/__init__.py:1278
#, python-format
msgid ""
"missing attribute mentioned in :members: option: module %s, attribute %s"
msgstr "atributo faltante mencionado en la :members: módulo %s, atributo %s"

#: ext/autodoc/__init__.py:1505 ext/autodoc/__init__.py:1593
#: ext/autodoc/__init__.py:3127
#, python-format
msgid "Failed to get a function signature for %s: %s"
msgstr "Error al obtener una firma de función para %s: %s"

#: ext/autodoc/__init__.py:1828
#, python-format
msgid "Failed to get a constructor signature for %s: %s"
msgstr "Error al obtener una firma de constructor para %s: %s"

#: ext/autodoc/__init__.py:1966
#, python-format
msgid "Bases: %s"
msgstr "Bases: %s"

#: ext/autodoc/__init__.py:1985
#, python-format
msgid "missing attribute %s in object %s"
msgstr "falta el atributo %s en el objeto %s"

#: ext/autodoc/__init__.py:2081 ext/autodoc/__init__.py:2110
#: ext/autodoc/__init__.py:2204
#, python-format
msgid "alias of %s"
msgstr "alias de %s"

#: ext/autodoc/__init__.py:2097
#, python-format
msgid "alias of TypeVar(%s)"
msgstr "alias de TypeVar(%s)"

#: ext/autodoc/__init__.py:2456 ext/autodoc/__init__.py:2576
#, python-format
msgid "Failed to get a method signature for %s: %s"
msgstr "Error al obtener una firma de método para %s: %s"

#: ext/autodoc/__init__.py:2720
#, python-format
msgid "Invalid __slots__ found on %s. Ignored."
msgstr "Se encontraron __slots__ no válidas en %s. Ignorado."

#: ext/autodoc/preserve_defaults.py:195
#, python-format
msgid "Failed to parse a default argument value for %r: %s"
msgstr "Error al analizar un valor de argumento predeterminado para %r: %s"

#: ext/autodoc/type_comment.py:151
#, python-format
msgid "Failed to update signature for %r: parameter not found: %s"
msgstr "Error al actualizar la firma para %r: parámetro no encontrado: %s"

#: ext/autodoc/type_comment.py:154
#, python-format
msgid "Failed to parse type_comment for %r: %s"
msgstr "Error al analizar type_comment para %r: %s"

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Modbus寄存器监控工具
用于监控Modbus设备寄存器的变化，特别适用于发现HMI使用的地址
"""

import sys
import os
import time
import json
import binascii
import logging
from logging.handlers import TimedRotatingFileHandler
import serial
import serial.tools.list_ports
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QComboBox, QSpinBox, QGroupBox, QTextEdit, QTableWidget,
    QTableWidgetItem, QHeaderView, QCheckBox, QFileDialog, QMessageBox, QTabWidget,
    QSplitter, QFrame, QDoubleSpinBox, QLineEdit, QProgressBar
)
from PyQt5.QtCore import QTimer, Qt, QDateTime
from PyQt5.QtGui import QFont, QColor

# 导入现有的ModbusRTU和ModbusTCP类
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
try:
    from main import ModbusRTU, ModbusTCP
except ImportError:
    # 如果无法导入，提供一个简化版的ModbusRTU类
    class ModbusRTU:
        """简化版的ModbusRTU通信类"""
        def __init__(self, port=None, baudrate=9600, bytesize=serial.EIGHTBITS,
                    parity=serial.PARITY_NONE, stopbits=serial.STOPBITS_ONE, timeout=0.5):
            # 日志设置
            self.logger = logging.getLogger("ModbusRTU")
            if not self.logger.handlers:
                log_dir = os.path.join(os.getcwd(), "logs")
                if not os.path.exists(log_dir):
                    os.makedirs(log_dir)
                handler = TimedRotatingFileHandler(os.path.join(log_dir, "modbus_monitor.log"),
                                                when="midnight", backupCount=7, encoding="utf-8")
                formatter = logging.Formatter('%(asctime)s %(levelname)s: %(message)s')
                handler.setFormatter(formatter)
                self.logger.addHandler(handler)
                self.logger.setLevel(logging.INFO)
            self.serial = None
            self.port = port
            self.baudrate = baudrate
            self.bytesize = bytesize
            self.parity = parity
            self.stopbits = stopbits
            self.timeout = timeout
            self.connected = False
            self.rx_data = []  # 接收的数据
            self.tx_data = []  # 发送的数据
            self.error_count = 0  # 错误计数
            self.success_count = 0  # 成功计数
            self.max_retries = 3  # 最大重试次数

        def connect(self):
            """连接串口"""
            try:
                if self.is_connected():
                    self.disconnect()
                self.serial = serial.Serial(
                    port=self.port,
                    baudrate=self.baudrate,
                    bytesize=self.bytesize,
                    parity=self.parity,
                    stopbits=self.stopbits,
                    timeout=self.timeout
                )
                self.connected = True
                self.logger.info(f"串口连接成功: {self.port}, 波特率: {self.baudrate}")
                return True
            except Exception as e:
                self.logger.error(f"连接错误: {e}")
                print(f"连接错误: {e}")
                self.connected = False
                return False

        def disconnect(self):
            """断开串口连接"""
            try:
                if self.serial and self.serial.is_open:
                    self.serial.close()
                    self.logger.info("串口已断开")
            except Exception as e:
                self.logger.error(f"断开连接错误: {e}")
                print(f"断开连接错误: {e}")
            finally:
                self.connected = False

        def is_connected(self):
            """检查是否已连接"""
            try:
                return self.connected and self.serial and self.serial.is_open
            except:
                self.connected = False
                return False

        def calculate_crc(self, data):
            """计算CRC校验码"""
            crc = 0xFFFF
            for byte in data:
                crc ^= byte
                for _ in range(8):
                    if crc & 0x0001:
                        crc = (crc >> 1) ^ 0xA001
                    else:
                        crc = crc >> 1
            return crc.to_bytes(2, byteorder='little')

        def read_register(self, slave_addr, register_addr, register_count=1):
            """读取寄存器数据"""
            if not self.is_connected():
                self.logger.warning("尝试读取寄存器但串口未连接")
                self.error_count += 1
                return None
            request = bytearray([
                slave_addr, 0x03, register_addr >> 8, register_addr & 0xFF, 0x00, register_count
            ])
            request += self.calculate_crc(request)
            self.tx_data.append({
                'time': time.strftime('%H:%M:%S'),
                'data': binascii.hexlify(request).decode('ascii'),
                'type': '读寄存器'
            })
            self.logger.info(f"发送: {binascii.hexlify(request).decode('ascii')}")
            try:
                self.serial.write(request)
                response = self.serial.read(5 + register_count * 2)
                self.rx_data.append({
                    'time': time.strftime('%H:%M:%S'),
                    'data': binascii.hexlify(response).decode('ascii') if response else 'No response',
                    'type': '读寄存器响应'
                })
                self.logger.info(f"接收: {binascii.hexlify(response).decode('ascii') if response else 'No response'}")
                if len(response) < 5:
                    self.logger.warning("响应数据不完整")
                    self.error_count += 1
                    return None
                if response[0] != slave_addr or response[1] != 0x03:
                    self.logger.warning("响应格式错误")
                    self.error_count += 1
                    return None
                data_length = response[2]
                self.success_count += 1
                data = []
                for i in range(0, data_length, 2):
                    if 3 + i + 1 < len(response):
                        value = (response[3 + i] << 8) | response[3 + i + 1]
                        data.append(value)
                return data
            except Exception as e:
                self.logger.error(f"读取寄存器错误: {e}")
                self.error_count += 1
                return None


class ModbusMonitor(QMainWindow):
    """Modbus寄存器监控工具主界面"""
    def __init__(self):
        super().__init__()

        # 通讯协议类型和对象
        self.protocol_type = "RTU"  # 默认使用RTU协议
        self.modbus = ModbusRTU()
        try:
            self.modbus_tcp = ModbusTCP()
        except:
            self.modbus_tcp = None

        # 监控数据
        self.register_values = {}  # 存储寄存器值
        self.register_changes = []  # 存储寄存器变化记录

        # 监控设置
        self.start_address = 0
        self.end_address = 9999
        self.scan_interval = 1.0  # 秒
        self.slave_address = 1
        self.is_monitoring = False

        # 设置界面
        self.setWindowTitle("Modbus寄存器监控工具")
        self.setGeometry(100, 100, 1000, 700)

        # 创建主界面
        self.init_ui()

        # 定时器
        self.scan_timer = QTimer()
        self.scan_timer.timeout.connect(self.scan_registers)

        # 状态更新定时器
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(500)  # 每0.5秒更新一次

    def get_current_modbus(self):
        """获取当前活动的Modbus通讯对象"""
        if self.protocol_type == "TCP" and self.modbus_tcp:
            return self.modbus_tcp
        else:
            return self.modbus

    def init_ui(self):
        """初始化用户界面"""
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout(central_widget)

        # 创建顶部控制区域
        control_layout = QHBoxLayout()

        # 连接设置组
        connection_group = QGroupBox("连接设置")
        connection_layout = QGridLayout()

        # 协议选择
        connection_layout.addWidget(QLabel("协议:"), 0, 0)
        self.protocol_combo = QComboBox()
        self.protocol_combo.addItem("Modbus RTU", "RTU")
        if self.modbus_tcp:
            self.protocol_combo.addItem("Modbus TCP", "TCP")
        self.protocol_combo.currentTextChanged.connect(self.on_protocol_changed)
        connection_layout.addWidget(self.protocol_combo, 0, 1)

        # 串口选择
        connection_layout.addWidget(QLabel("串口:"), 1, 0)
        self.port_combo = QComboBox()
        self.refresh_ports()
        connection_layout.addWidget(self.port_combo, 1, 1)

        # TCP地址设置
        connection_layout.addWidget(QLabel("IP地址:"), 1, 0)
        self.tcp_host_edit = QLineEdit("*************")
        connection_layout.addWidget(self.tcp_host_edit, 1, 1)

        # TCP端口设置
        connection_layout.addWidget(QLabel("TCP端口:"), 2, 0)
        self.tcp_port_spin = QSpinBox()
        self.tcp_port_spin.setRange(1, 65535)
        self.tcp_port_spin.setValue(502)
        connection_layout.addWidget(self.tcp_port_spin, 2, 1)

        # 刷新按钮
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.refresh_ports)
        connection_layout.addWidget(self.refresh_btn, 1, 2)

        # 波特率
        self.baudrate_label = QLabel("波特率:")
        connection_layout.addWidget(self.baudrate_label, 3, 0)
        self.baudrate_combo = QComboBox()
        for baudrate in [4800, 9600, 19200, 38400, 57600, 115200]:
            self.baudrate_combo.addItem(str(baudrate))
        self.baudrate_combo.setCurrentText("9600")
        connection_layout.addWidget(self.baudrate_combo, 3, 1)

        # 数据位
        self.bytesize_label = QLabel("数据位:")
        connection_layout.addWidget(self.bytesize_label, 4, 0)
        self.bytesize_combo = QComboBox()
        self.bytesize_combo.addItem("5", serial.FIVEBITS)
        self.bytesize_combo.addItem("6", serial.SIXBITS)
        self.bytesize_combo.addItem("7", serial.SEVENBITS)
        self.bytesize_combo.addItem("8", serial.EIGHTBITS)
        self.bytesize_combo.setCurrentIndex(3)  # 默认8位
        connection_layout.addWidget(self.bytesize_combo, 4, 1)

        # 校验位
        self.parity_label = QLabel("校验位:")
        connection_layout.addWidget(self.parity_label, 5, 0)
        self.parity_combo = QComboBox()
        self.parity_combo.addItem("无", serial.PARITY_NONE)
        self.parity_combo.addItem("奇校验", serial.PARITY_ODD)
        self.parity_combo.addItem("偶校验", serial.PARITY_EVEN)
        self.parity_combo.addItem("标记", serial.PARITY_MARK)
        self.parity_combo.addItem("空格", serial.PARITY_SPACE)
        self.parity_combo.setCurrentIndex(0)  # 默认无校验
        connection_layout.addWidget(self.parity_combo, 5, 1)

        # 停止位
        self.stopbits_label = QLabel("停止位:")
        connection_layout.addWidget(self.stopbits_label, 6, 0)
        self.stopbits_combo = QComboBox()
        self.stopbits_combo.addItem("1", serial.STOPBITS_ONE)
        self.stopbits_combo.addItem("1.5", serial.STOPBITS_ONE_POINT_FIVE)
        self.stopbits_combo.addItem("2", serial.STOPBITS_TWO)
        self.stopbits_combo.setCurrentIndex(0)  # 默认1位
        connection_layout.addWidget(self.stopbits_combo, 6, 1)

        # 超时设置
        connection_layout.addWidget(QLabel("超时(秒):"), 7, 0)
        self.timeout_spin = QDoubleSpinBox()
        self.timeout_spin.setRange(0.1, 10.0)
        self.timeout_spin.setSingleStep(0.1)
        self.timeout_spin.setValue(0.5)
        connection_layout.addWidget(self.timeout_spin, 7, 1)

        # 从站地址
        connection_layout.addWidget(QLabel("从站地址:"), 8, 0)
        self.slave_addr_spin = QSpinBox()
        self.slave_addr_spin.setRange(1, 247)
        self.slave_addr_spin.setValue(1)
        connection_layout.addWidget(self.slave_addr_spin, 8, 1)

        # 连接按钮
        self.connect_btn = QPushButton("连接")
        self.connect_btn.clicked.connect(self.toggle_connection)
        connection_layout.addWidget(self.connect_btn, 8, 2)

        connection_group.setLayout(connection_layout)
        control_layout.addWidget(connection_group)

        # 监控设置组
        monitor_group = QGroupBox("监控设置")
        monitor_layout = QGridLayout()

        # 起始地址
        monitor_layout.addWidget(QLabel("起始地址:"), 0, 0)
        self.start_addr_spin = QSpinBox()
        self.start_addr_spin.setRange(0, 9999)
        self.start_addr_spin.setValue(0)
        monitor_layout.addWidget(self.start_addr_spin, 0, 1)

        # 结束地址
        monitor_layout.addWidget(QLabel("结束地址:"), 1, 0)
        self.end_addr_spin = QSpinBox()
        self.end_addr_spin.setRange(0, 9999)
        self.end_addr_spin.setValue(100)
        monitor_layout.addWidget(self.end_addr_spin, 1, 1)

        # 扫描间隔
        monitor_layout.addWidget(QLabel("扫描间隔(秒):"), 2, 0)
        self.interval_spin = QDoubleSpinBox()
        self.interval_spin.setRange(0.1, 10.0)
        self.interval_spin.setSingleStep(0.1)
        self.interval_spin.setValue(1.0)
        monitor_layout.addWidget(self.interval_spin, 2, 1)

        # 开始/停止监控按钮
        self.monitor_btn = QPushButton("开始监控")
        self.monitor_btn.clicked.connect(self.toggle_monitoring)
        self.monitor_btn.setEnabled(False)
        monitor_layout.addWidget(self.monitor_btn, 3, 0, 1, 2)

        # 添加空白行以匹配连接设置区域的高度
        for i in range(4, 7):
            monitor_layout.addWidget(QLabel(""), i, 0)

        monitor_group.setLayout(monitor_layout)
        control_layout.addWidget(monitor_group)

        # 状态组
        status_group = QGroupBox("状态")
        status_layout = QGridLayout()

        # 连接状态
        status_layout.addWidget(QLabel("连接状态:"), 0, 0)
        self.connection_status_label = QLabel("未连接")
        self.connection_status_label.setStyleSheet("color: red;")
        status_layout.addWidget(self.connection_status_label, 0, 1)

        # 监控状态
        status_layout.addWidget(QLabel("监控状态:"), 1, 0)
        self.monitor_status_label = QLabel("未监控")
        self.monitor_status_label.setStyleSheet("color: gray;")
        status_layout.addWidget(self.monitor_status_label, 1, 1)

        # 扫描进度
        status_layout.addWidget(QLabel("扫描进度:"), 2, 0)
        self.scan_progress = QProgressBar()
        self.scan_progress.setValue(0)
        status_layout.addWidget(self.scan_progress, 2, 1)

        # 清除按钮
        clear_btn = QPushButton("清除记录")
        clear_btn.clicked.connect(self.clear_records)
        status_layout.addWidget(clear_btn, 3, 0)

        # 保存按钮
        save_btn = QPushButton("保存记录")
        save_btn.clicked.connect(self.save_records)
        status_layout.addWidget(save_btn, 3, 1)

        status_group.setLayout(status_layout)
        control_layout.addWidget(status_group)

        main_layout.addLayout(control_layout)

        # 创建选项卡
        tabs = QTabWidget()

        # 变化记录选项卡
        changes_tab = QWidget()
        changes_layout = QVBoxLayout(changes_tab)

        # 变化记录表格
        self.changes_table = QTableWidget()
        self.changes_table.setColumnCount(5)
        self.changes_table.setHorizontalHeaderLabels(["时间", "地址", "旧值", "新值", "变化量"])
        self.changes_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        changes_layout.addWidget(self.changes_table)

        tabs.addTab(changes_tab, "变化记录")

        # 当前值选项卡
        values_tab = QWidget()
        values_layout = QVBoxLayout(values_tab)

        # 当前值表格
        self.values_table = QTableWidget()
        self.values_table.setColumnCount(3)
        self.values_table.setHorizontalHeaderLabels(["地址", "值", "上次更新"])
        self.values_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        values_layout.addWidget(self.values_table)

        tabs.addTab(values_tab, "当前值")

        main_layout.addWidget(tabs)

        # 状态栏
        self.statusBar = self.statusBar()
        self.status_label = QLabel("就绪")
        self.statusBar.addPermanentWidget(self.status_label)

        # 初始化协议显示
        self.on_protocol_changed()

    def on_protocol_changed(self):
        """协议切换时的处理"""
        protocol_data = self.protocol_combo.currentData()
        if protocol_data:
            self.protocol_type = protocol_data
        else:
            # 如果没有数据，根据文本判断
            if "TCP" in self.protocol_combo.currentText():
                self.protocol_type = "TCP"
            else:
                self.protocol_type = "RTU"

        # 根据协议类型显示/隐藏相应的控件
        if self.protocol_type == "TCP":
            # 隐藏RTU相关控件
            self.port_combo.hide()
            self.refresh_btn.hide()
            self.baudrate_label.hide()
            self.baudrate_combo.hide()
            self.bytesize_label.hide()
            self.bytesize_combo.hide()
            self.parity_label.hide()
            self.parity_combo.hide()
            self.stopbits_label.hide()
            self.stopbits_combo.hide()

            # 显示TCP相关控件
            self.tcp_host_edit.show()
            self.tcp_port_spin.show()
        else:
            # 显示RTU相关控件
            self.port_combo.show()
            self.refresh_btn.show()
            self.baudrate_label.show()
            self.baudrate_combo.show()
            self.bytesize_label.show()
            self.bytesize_combo.show()
            self.parity_label.show()
            self.parity_combo.show()
            self.stopbits_label.show()
            self.stopbits_combo.show()

            # 隐藏TCP相关控件
            self.tcp_host_edit.hide()
            self.tcp_port_spin.hide()

    def refresh_ports(self):
        """刷新可用串口列表"""
        self.port_combo.clear()
        ports = serial.tools.list_ports.comports()
        for port in ports:
            self.port_combo.addItem(port.device)

    def toggle_connection(self):
        """切换连接状态"""
        try:
            current_modbus = self.get_current_modbus()
            if not current_modbus.is_connected():
                # 连接
                if self.protocol_type == "RTU":
                    # RTU协议连接
                    port = self.port_combo.currentText()
                    baudrate = int(self.baudrate_combo.currentText())
                    bytesize = self.bytesize_combo.currentData()
                    parity = self.parity_combo.currentData()
                    stopbits = self.stopbits_combo.currentData()
                    timeout = self.timeout_spin.value()

                    if not port:
                        QMessageBox.warning(self, "警告", "请选择串口")
                        return

                    # 更新状态栏
                    self.status_label.setText(f"正在连接到 {port}...")
                    QApplication.processEvents()  # 确保UI更新

                    self.modbus.port = port
                    self.modbus.baudrate = baudrate
                    self.modbus.bytesize = bytesize
                    self.modbus.parity = parity
                    self.modbus.stopbits = stopbits
                    self.modbus.timeout = timeout
                    connection_success = self.modbus.connect()
                else:
                    # TCP协议连接
                    if not self.modbus_tcp:
                        QMessageBox.warning(self, "警告", "TCP协议不可用")
                        return

                    host = self.tcp_host_edit.text()
                    port = self.tcp_port_spin.value()
                    timeout = self.timeout_spin.value()

                    if not host:
                        QMessageBox.warning(self, "警告", "请输入IP地址")
                        return

                    # 更新状态栏
                    self.status_label.setText(f"正在连接到 {host}:{port}...")
                    QApplication.processEvents()  # 确保UI更新

                    self.modbus_tcp.host = host
                    self.modbus_tcp.port = port
                    self.modbus_tcp.timeout = timeout
                    connection_success = self.modbus_tcp.connect()

                if connection_success:
                    self.connection_status_label.setText("已连接")
                    self.connection_status_label.setStyleSheet("color: green;")
                    self.connect_btn.setText("断开")
                    self.monitor_btn.setEnabled(True)

                    if self.protocol_type == "RTU":
                        self.status_label.setText(f"已连接到 {port}")
                    else:
                        self.status_label.setText(f"已连接到 {host}:{port}")
                else:
                    if self.protocol_type == "RTU":
                        QMessageBox.critical(self, "错误", f"无法连接到串口 {port}")
                    else:
                        QMessageBox.critical(self, "错误", f"无法连接到TCP服务器 {host}:{port}")
                    self.status_label.setText("连接失败")
            else:
                # 断开连接
                self.status_label.setText("正在断开连接...")
                QApplication.processEvents()  # 确保UI更新

                # 如果正在监控，先停止监控
                if self.is_monitoring:
                    self.toggle_monitoring()

                current_modbus.disconnect()
                self.connection_status_label.setText("未连接")
                self.connection_status_label.setStyleSheet("color: red;")
                self.connect_btn.setText("连接")
                self.monitor_btn.setEnabled(False)
                self.status_label.setText("已断开连接")
        except Exception as e:
            self.status_label.setText(f"连接操作错误: {str(e)}")
            QMessageBox.critical(self, "错误", f"连接操作发生错误: {str(e)}")
            # 确保断开连接
            try:
                self.modbus.disconnect()
            except:
                pass
            self.connection_status_label.setText("未连接")
            self.connection_status_label.setStyleSheet("color: red;")
            self.connect_btn.setText("连接")
            self.monitor_btn.setEnabled(False)

    def toggle_monitoring(self):
        """切换监控状态"""
        if not self.is_monitoring:
            # 开始监控
            self.start_address = self.start_addr_spin.value()
            self.end_address = self.end_addr_spin.value()
            self.scan_interval = self.interval_spin.value()
            self.slave_address = self.slave_addr_spin.value()

            if self.start_address > self.end_address:
                QMessageBox.warning(self, "警告", "起始地址不能大于结束地址")
                return

            if self.end_address - self.start_address > 500:
                reply = QMessageBox.question(self, "确认",
                                           f"您选择了监控 {self.end_address - self.start_address + 1} 个寄存器，这可能会导致扫描速度较慢。是否继续？",
                                           QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
                if reply == QMessageBox.No:
                    return

            self.is_monitoring = True
            self.monitor_status_label.setText("监控中")
            self.monitor_status_label.setStyleSheet("color: green;")
            self.monitor_btn.setText("停止监控")

            # 禁用设置控件
            self.start_addr_spin.setEnabled(False)
            self.end_addr_spin.setEnabled(False)
            self.interval_spin.setEnabled(False)
            self.slave_addr_spin.setEnabled(False)

            # 启动定时器
            self.scan_timer.start(int(self.scan_interval * 1000))
            self.status_label.setText(f"正在监控寄存器 {self.start_address} 到 {self.end_address}")
        else:
            # 停止监控
            self.is_monitoring = False
            self.scan_timer.stop()
            self.monitor_status_label.setText("未监控")
            self.monitor_status_label.setStyleSheet("color: gray;")
            self.monitor_btn.setText("开始监控")

            # 启用设置控件
            self.start_addr_spin.setEnabled(True)
            self.end_addr_spin.setEnabled(True)
            self.interval_spin.setEnabled(True)
            self.slave_addr_spin.setEnabled(True)

            self.status_label.setText("监控已停止")

    def scan_registers(self):
        """扫描寄存器并检测变化"""
        current_modbus = self.get_current_modbus()
        if not current_modbus.is_connected() or not self.is_monitoring:
            return

        total_registers = self.end_address - self.start_address + 1
        registers_scanned = 0

        # 分批读取寄存器，每次最多读取125个
        for addr in range(self.start_address, self.end_address + 1, 125):
            end = min(addr + 124, self.end_address)
            count = end - addr + 1

            # 更新进度条
            registers_scanned += count
            progress = int((registers_scanned / total_registers) * 100)
            self.scan_progress.setValue(progress)
            QApplication.processEvents()  # 确保UI更新

            # 读取寄存器
            values = current_modbus.read_register(self.slave_address, addr, count)
            if values is None:
                continue

            # 检查每个寄存器的值是否变化
            for i, value in enumerate(values):
                register_addr = addr + i

                # 如果是第一次读取该寄存器
                if register_addr not in self.register_values:
                    self.register_values[register_addr] = {
                        'value': value,
                        'last_update': QDateTime.currentDateTime().toString('yyyy-MM-dd hh:mm:ss')
                    }
                    continue

                # 检查值是否变化
                old_value = self.register_values[register_addr]['value']
                if value != old_value:
                    # 记录变化
                    change = {
                        'time': QDateTime.currentDateTime().toString('yyyy-MM-dd hh:mm:ss'),
                        'address': register_addr,
                        'old_value': old_value,
                        'new_value': value,
                        'diff': value - old_value
                    }
                    self.register_changes.append(change)

                    # 更新表格
                    row = self.changes_table.rowCount()
                    self.changes_table.insertRow(row)
                    self.changes_table.setItem(row, 0, QTableWidgetItem(change['time']))
                    self.changes_table.setItem(row, 1, QTableWidgetItem(str(change['address'])))
                    self.changes_table.setItem(row, 2, QTableWidgetItem(str(change['old_value'])))
                    self.changes_table.setItem(row, 3, QTableWidgetItem(str(change['new_value'])))
                    self.changes_table.setItem(row, 4, QTableWidgetItem(str(change['diff'])))

                    # 高亮显示
                    for col in range(5):
                        self.changes_table.item(row, col).setBackground(QColor(255, 255, 0, 100))

                    # 滚动到底部
                    self.changes_table.scrollToBottom()

                    # 更新寄存器值
                    self.register_values[register_addr] = {
                        'value': value,
                        'last_update': change['time']
                    }

                    # 记录到日志
                    logging.info(f"寄存器变化: 地址={register_addr}, 旧值={old_value}, 新值={value}, 差值={value-old_value}")

        # 更新当前值表格
        self.update_values_table()

    def update_values_table(self):
        """更新当前值表格"""
        self.values_table.setRowCount(0)
        for addr, data in sorted(self.register_values.items()):
            row = self.values_table.rowCount()
            self.values_table.insertRow(row)
            self.values_table.setItem(row, 0, QTableWidgetItem(str(addr)))
            self.values_table.setItem(row, 1, QTableWidgetItem(str(data['value'])))
            self.values_table.setItem(row, 2, QTableWidgetItem(data['last_update']))

    def update_status(self):
        """更新状态信息"""
        # 检查连接状态
        current_modbus = self.get_current_modbus()
        if not current_modbus.is_connected():
            if self.connection_status_label.text() != "未连接":
                self.connection_status_label.setText("未连接")
                self.connection_status_label.setStyleSheet("color: red;")
                self.connect_btn.setText("连接")
                self.monitor_btn.setEnabled(False)

                # 如果正在监控，停止监控
                if self.is_monitoring:
                    self.toggle_monitoring()

    def clear_records(self):
        """清除记录"""
        msgBox = QMessageBox(QMessageBox.Question, "确认", "确定要清除所有记录吗？",
                          QMessageBox.Yes | QMessageBox.No, self)
        msgBox.button(QMessageBox.Yes).setText("是")
        msgBox.button(QMessageBox.No).setText("否")
        msgBox.setDefaultButton(QMessageBox.No)
        reply = msgBox.exec_()
        if reply == QMessageBox.Yes:
            self.register_values = {}
            self.register_changes = []
            self.changes_table.setRowCount(0)
            self.values_table.setRowCount(0)
            self.status_label.setText("已清除所有记录")

    def save_records(self):
        """保存记录到文件"""
        if not self.register_changes:
            QMessageBox.information(self, "提示", "没有变化记录可保存")
            return

        filename, _ = QFileDialog.getSaveFileName(self, "保存记录", "", "CSV文件 (*.csv);;JSON文件 (*.json);;所有文件 (*)")
        if not filename:
            return

        try:
            if filename.endswith('.csv'):
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write("时间,地址,旧值,新值,变化量\n")
                    for change in self.register_changes:
                        f.write(f"{change['time']},{change['address']},{change['old_value']},{change['new_value']},{change['diff']}\n")
            else:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(self.register_changes, f, indent=4)

            self.status_label.setText(f"记录已保存到 {filename}")
            QMessageBox.information(self, "成功", f"记录已保存到 {filename}")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存记录时发生错误: {str(e)}")

    def closeEvent(self, event):
        """关闭窗口事件"""
        if self.is_monitoring:
            msgBox = QMessageBox(QMessageBox.Question, "确认", "监控正在进行中，确定要退出吗？",
                              QMessageBox.Yes | QMessageBox.No, self)
            msgBox.button(QMessageBox.Yes).setText("是")
            msgBox.button(QMessageBox.No).setText("否")
            msgBox.setDefaultButton(QMessageBox.No)
            reply = msgBox.exec_()
            if reply == QMessageBox.No:
                event.ignore()
                return

        # 断开连接
        current_modbus = self.get_current_modbus()
        if current_modbus.is_connected():
            current_modbus.disconnect()

        event.accept()


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = ModbusMonitor()
    window.show()
    sys.exit(app.exec_())

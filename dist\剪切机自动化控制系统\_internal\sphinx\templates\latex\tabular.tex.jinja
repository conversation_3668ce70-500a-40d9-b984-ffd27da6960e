\begin{savenotes}\sphinxattablestart
\sphinxthistablewithglobalstyle
<% if 'booktabs' in table.styles -%>
\sphinxthistablewithbooktabsstyle
<% endif -%>
<% if 'borderless' in table.styles -%>
\sphinxthistablewithborderlessstyle
<% endif -%>
<% if 'standard' in table.styles -%>
\sphinxthistablewithstandardstyle
<% endif -%>
<% if 'vlines' in table.styles -%>
\sphinxthistablewithvlinesstyle
<% endif -%>
<% if 'novlines' in table.styles -%>
\sphinxthistablewithnovlinesstyle
<% endif -%>
<% if 'colorrows' in table.styles -%>
\sphinxthistablewithcolorrowsstyle
<% endif -%>
<% if 'nocolorrows' in table.styles -%>
\sphinxthistablewithnocolorrowsstyle
<% endif -%>
<% if table.align -%>
  <%- if table.align in ('center', 'default') -%>
  \centering
  <%- elif table.align == 'left' -%>
  \raggedright
  <%- else -%>
  \raggedleft
  <%- endif %>
<%- else -%>
  \centering
<%- endif %>
<% if table.caption -%>
\sphinxcapstartof{table}
\sphinxthecaptionisattop
\sphinxcaption{<%= ''.join(table.caption) %>}<%= labels %>
\sphinxaftertopcaption
<% elif labels -%>
\phantomsection<%= labels %>\nobreak
<% endif -%>
\begin{tabular}[t]<%= table.get_colspec() -%>
\sphinxtoprule
<%= ''.join(table.header) -%>
<%- if table.header -%>
\sphinxmidrule
<% endif -%>
\sphinxtableatstartofbodyhook
<%=- ''.join(table.body) -%>
\sphinxbottomrule
\end{tabular}
\sphinxtableafterendhook\par
\sphinxattableend\end{savenotes}

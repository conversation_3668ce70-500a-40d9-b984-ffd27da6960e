%% Generated by <PERSON>phinx.
\def\sphinxdocclass{<%= docclass %>}
<% if latex_engine == 'lualatex' -%>
\IfFileExists{luatex85.sty}
 {\RequirePackage{luatex85}}
 {\ifdefined\luatexversion\ifnum\luatexversion>84\relax
  \PackageError{sphinx}
  {** With this LuaTeX (\the\luatexversion),Sphinx requires luatex85.sty **}
  {** Add the LaTeX package luatex85 to your TeX installation, and try again **}
  \endinput\fi\fi}
<% endif -%>
\documentclass[<%= papersize %>,<%= pointsize %><%= classoptions %>]{<%= wrapperclass %>}
\ifdefined\pdfpxdimen
   \let\sphinxpxdimen\pdfpxdimen\else\newdimen\sphinxpxdimen
\fi \sphinxpxdimen=<%= pxunit %>\relax
\ifdefined\pdfimageresolution
    \pdfimageresolution= \numexpr \dimexpr1in\relax/\sphinxpxdimen\relax
\fi
%% let collapsible pdf bookmarks panel have high depth per default
\PassOptionsToPackage{bookmarksdepth=5}{hyperref}
<% if use_xindy -%>
%% turn off hyperref patch of \index as sphinx.xdy xindy module takes care of
%% suitable \hyperpage mark-up, working around hyperref-xindy incompatibility
\PassOptionsToPackage{hyperindex=false}{hyperref}
%% memoir class requires extra handling
\makeatletter\@ifclassloaded{memoir}
{\ifdefined\memhyperindexfalse\memhyperindexfalse\fi}{}\makeatother
<% endif %>
<% if booktabs -%>
\PassOptionsToPackage{booktabs}{sphinx}
<% endif -%>
<% if borderless -%>
\PassOptionsToPackage{borderless}{sphinx}
<% endif -%>
<% if colorrows -%>
\PassOptionsToPackage{colorrows}{sphinx}
<% endif -%>
<%= passoptionstopackages %>
\PassOptionsToPackage{warn}{textcomp}
<%= inputenc %>
<%= utf8extra %>
<%= cmappkg %>
<%= fontenc %>
<%= amsmath %>
<%= multilingual %>
<%= substitutefont %>
<%= textcyrillic %>
<%= fontpkg %>
<%= fontsubstitution %>
<%= textgreek %>
<%= fncychap %>
\usepackage<%= sphinxpkgoptions %>{sphinx}
<%= sphinxsetup %>
<%= fvset %>
<%= geometry %>
<%= extrapackages %>

<%- for name, option in packages %>
<%- if option %>
\usepackage[<%= option %>]{<%= name %>}
<%- else %>
\usepackage{<%= name %>}
<%- endif %>
<%- endfor %>

<%= hyperref %>
<%- for name, option in packages_after_hyperref %>
<%- if option %>
\usepackage[<%= option %>]{<%= name %>}
<%- else %>
\usepackage{<%= name %>}
<%- endif %>
<%- endfor %>

<%= contentsname %>
\usepackage{sphinxmessages}
<%= tocdepth %>
<%= secnumdepth %>
<%= preamble %>

\title{<%= title %>}
\date{<%= date %>}
\release{<%= release | e %>}
\author{<%= author %>}
<%- if logofilename %>
\newcommand{\sphinxlogo}{\sphinxincludegraphics{<%= logofilename %>}\par}
<%- else %>
\newcommand{\sphinxlogo}{\vbox{}}
<%- endif %>
<%- if releasename or release %>
\renewcommand{\releasename}{<%= releasename or _('Release') | e %>}
<%- else %>
\renewcommand{\releasename}{}
<%- endif %>
<%= makeindex %>
\begin{document}
<%= shorthandoff %>
\pagestyle{empty}
<%= maketitle %>
\pagestyle{plain}
<%= tableofcontents %>
\pagestyle{normal}
<%= body %>
<%= atendofbody %>
<%= indices %>
\renewcommand{\indexname}{<%= _('Index') | e %>}
<%= printindex %>
\end{document}

# TCP连接稳定性修复说明

## 问题分析

根据PLC人员提供的日志分析，发现以下问题：

### 1. 连接频繁断开重连
```
2025-07-28 21:08:15,364 INFO: TCP连接成功: ***********:502
2025-07-28 21:08:17,664 INFO: TCP连接已断开
2025-07-28 21:08:19,302 INFO: TCP连接成功: ***********:502
```
- 连接持续时间很短（约2秒）
- 频繁的断开重连影响通信稳定性

### 2. 超时配置过短
- 原配置：`tcp_timeout: 0.2`（200毫秒）
- 对于工业网络环境来说太短，容易导致超时断开

### 3. 心跳检测干扰
- 原心跳检测机制发送额外的测试请求
- 可能与PLC的正常通信产生冲突

## 修复方案

### 1. 优化超时配置
```json
{
    "timeout": 2.0,        // RTU超时改为2秒
    "tcp_timeout": 5.0,    // TCP超时改为5秒
    "tcp_port": 502        // 修正端口号
}
```

### 2. 改进心跳检测机制
```python
def check_connection_health(self):
    """检查TCP连接健康状态 - 优化版本，避免干扰正常通信"""
    # 简化连接检查，只检查socket状态，不发送测试数据
    if self.socket.fileno() == -1:
        return False
    
    # 检查活动间隔放宽到60秒
    if time.time() - self.last_activity_time > 60:
        return False
    
    return True
```

### 3. 增强连接状态检查
```python
def is_connected(self):
    """检查是否已连接 - 增强版本"""
    # 多重检查确保连接状态准确
    if not self.connected or not self.socket:
        return False
    
    # 检查socket文件描述符是否有效
    if self.socket.fileno() == -1:
        self.connected = False
        return False
    
    return True
```

### 4. 添加连接稳定性缓冲
```python
def connect(self):
    """连接TCP服务器"""
    # 设置socket选项，提高连接稳定性
    self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)
    self.socket.setsockopt(socket.IPPROTO_TCP, socket.TCP_KEEPIDLE, 30)
    
    # 记录连接建立时间
    self.connection_time = time.time()

def is_connection_stable(self):
    """检查连接是否已稳定（连接后需要等待2秒）"""
    if not self.connection_time:
        return False
    
    elapsed = time.time() - self.connection_time
    return elapsed >= 2.0  # 连接后等待2秒再进行通信
```

### 5. 优化自动重连机制
```python
# 减少重连敏感度
self.max_reconnect_attempts = 3  # 最大重连次数减少到3次
self.reconnect_interval = 10     # 重连间隔增加到10秒

# 添加防抖机制
time_since_disconnect = current_time - self.last_disconnect_time
if time_since_disconnect >= 10:  # 至少等待10秒再重连
    # 执行重连逻辑
```

## 修复效果

### 1. 连接稳定性提升
- 超时时间增加，减少因网络延迟导致的误判断开
- Socket选项优化，提高TCP连接的稳定性
- 连接稳定性缓冲，避免连接后立即通信导致的问题

### 2. 减少频繁重连
- 增加重连间隔时间（5秒 → 10秒）
- 添加防抖机制，断开后至少等待10秒再重连
- 减少最大重连次数（5次 → 3次）

### 3. 改善通信质量
- 移除干扰性的心跳检测请求
- 连接状态检查更加准确
- 通信前检查连接稳定性

## 测试建议

### 1. 使用测试工具
运行 `TCP连接稳定性测试.py` 进行连接稳定性测试：
```bash
python TCP连接稳定性测试.py
```

### 2. 测试步骤
1. 配置目标IP为PLC地址（***********）
2. 设置端口为502
3. 设置超时为5秒
4. 设置测试间隔为10秒
5. 运行测试，观察成功率

### 3. 预期结果
- 连接成功率应该 > 95%
- 连接时间应该稳定在合理范围内
- 不应该出现频繁的连接失败

### 4. 长期测试
- 让程序连续运行数小时
- 观察是否还有频繁断开重连的现象
- 检查日志中的连接状态变化

## 配置建议

### 1. 网络环境优化
```bash
# 检查网络连通性
ping ***********

# 检查端口连通性
telnet *********** 502
```

### 2. PLC端检查
- 确认PLC的Modbus TCP服务正常运行
- 检查PLC是否有连接数限制
- 确认防火墙设置允许502端口通信

### 3. 系统配置
- 确保网络适配器设置正确
- 检查Windows防火墙设置
- 确认没有其他程序占用502端口

## 监控和维护

### 1. 日志监控
定期检查以下日志文件：
- `logs/modbus TCP.log` - TCP通信日志
- `logs/crash_error.log` - 错误日志

### 2. 关键指标
- 连接成功率 > 95%
- 平均连接时间 < 1秒
- 重连频率 < 1次/小时

### 3. 异常处理
如果仍然出现连接问题：
1. 检查网络环境
2. 调整超时参数
3. 联系PLC厂商确认设备状态

## 总结

通过以上修复，TCP连接的稳定性应该得到显著改善：

1. **超时配置优化**：适应工业网络环境的延迟特性
2. **连接检查增强**：更准确地判断连接状态
3. **稳定性缓冲**：避免连接后立即通信的问题
4. **重连机制优化**：减少频繁重连对系统的影响
5. **心跳检测简化**：避免干扰正常通信

这些修复从根本上解决了TCP连接频繁断开重连的问题，提高了系统的整体稳定性和可靠性。

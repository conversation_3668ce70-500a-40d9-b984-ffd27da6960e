#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
创建一个专业的剪切机图标文件
"""

from PIL import Image, ImageDraw, ImageFont, ImageFilter
import os
import math

def create_icon(output_file="icon.ico", size=256, bg_color=(41, 128, 185), text="剪"):
    """创建一个专业的剪切机图标文件"""
    # 创建一个正方形图像，带有圆角
    img = Image.new('RGBA', (size, size), color=(0, 0, 0, 0))
    draw = ImageDraw.Draw(img)

    # 绘制圆角矩形背景
    corner_radius = size // 8
    draw.rounded_rectangle([(0, 0), (size, size)], corner_radius, fill=bg_color)

    # 尝试加载字体，如果失败则使用默认字体
    try:
        # 尝试使用系统字体
        font_size = size // 2
        font = ImageFont.truetype("simhei.ttf", font_size)  # 使用黑体
    except IOError:
        try:
            # 尝试使用Arial字体
            font = ImageFont.truetype("arial.ttf", font_size)
        except IOError:
            # 如果都失败，使用默认字体
            font = ImageFont.load_default()

    # 计算文本位置，使其居中
    text_width, text_height = draw.textbbox((0, 0), text, font=font)[2:4]
    position = ((size - text_width) // 2, (size - text_height) // 2 - size // 16)

    # 绘制文本阴影
    shadow_offset = size // 64
    draw.text((position[0] + shadow_offset, position[1] + shadow_offset), text, font=font, fill=(0, 0, 0, 128))

    # 绘制文本
    draw.text(position, text, font=font, fill=(255, 255, 255))

    # 绘制剪刀图案
    draw_scissors(draw, size, position[1] + text_height + size // 16)

    # 添加轻微的发光效果
    img = img.filter(ImageFilter.GaussianBlur(radius=size//128))

    # 创建多种尺寸的图标
    icon_sizes = [(16, 16), (32, 32), (48, 48), (64, 64), (128, 128), (256, 256)]
    icons = []

    for size in icon_sizes:
        resized_img = img.resize(size, Image.LANCZOS)
        icons.append(resized_img)

    # 保存为ICO文件
    icons[0].save(output_file, format="ICO", sizes=icon_sizes, append_images=icons[1:])

    print(f"图标已创建: {os.path.abspath(output_file)}")
    return os.path.abspath(output_file)

def draw_scissors(draw, size, y_position):
    """绘制简化的剪刀图案"""
    # 剪刀大小
    scissors_width = size * 0.6
    scissors_height = size * 0.2

    # 剪刀位置
    x_center = size / 2
    y_center = y_position + scissors_height / 2

    # 剪刀手柄
    handle_radius = scissors_height / 2
    handle1_center = (x_center - scissors_width / 3, y_center)
    handle2_center = (x_center + scissors_width / 3, y_center)

    # 绘制剪刀刀片
    blade_points1 = [
        (x_center, y_center - scissors_height / 4),
        (x_center - scissors_width / 2, y_center - scissors_height / 2),
        (x_center - scissors_width / 2.5, y_center)
    ]

    blade_points2 = [
        (x_center, y_center - scissors_height / 4),
        (x_center + scissors_width / 2, y_center - scissors_height / 2),
        (x_center + scissors_width / 2.5, y_center)
    ]

    # 绘制剪刀
    draw.polygon(blade_points1, fill=(192, 192, 192))
    draw.polygon(blade_points2, fill=(160, 160, 160))

    # 绘制手柄
    draw.ellipse((handle1_center[0] - handle_radius, handle1_center[1] - handle_radius,
                  handle1_center[0] + handle_radius, handle1_center[1] + handle_radius),
                 fill=(255, 215, 0))

    draw.ellipse((handle2_center[0] - handle_radius, handle2_center[1] - handle_radius,
                  handle2_center[0] + handle_radius, handle2_center[1] + handle_radius),
                 fill=(255, 215, 0))

if __name__ == "__main__":
    create_icon()

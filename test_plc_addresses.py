#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PLC地址测试程序
用于测试新的Modbus地址映射和双字寄存器功能
"""

import sys
import time
from main import ModbusRTU

def test_double_word_registers():
    """测试双字寄存器读写功能"""
    print("=" * 50)
    print("测试双字寄存器功能")
    print("=" * 50)

    # 创建Modbus连接
    modbus = ModbusRTU(port="COM1", baudrate=9600, timeout=1.0)

    if not modbus.connect():
        print("❌ 无法连接到Modbus设备")
        return False

    print("✅ Modbus连接成功")
    slave_addr = 1

    # 测试地址列表（根据PLC提供的地址：HD0-HD6143对应41088-47231）
    test_addresses = {
        42088: "手动转速 (HD1000)",
        42090: "自动转速 (HD1002)",
        42092: "第一次相对自动定位距离 (HD1004)",
        42094: "每次递增的补偿距离 (HD1006)",
        42096: "自动运行的相对距离 (HD1008)",
        42098: "设定数量 (HD1010)",
        42100: "已完成数量 (HD1012)"
    }

    # 测试写入和读取
    for addr, name in test_addresses.items():
        print(f"\n📝 测试 {name} (地址: {addr})")

        # 跳过只读寄存器的写入测试
        if addr == 42100:  # 已完成数量为只读
            print(f"   ⚠️  {name} 为只读寄存器，跳过写入测试")
            # 只测试读取
            value = modbus.read_double_word_register(slave_addr, addr)
            if value is not None:
                print(f"   ✅ 读取成功: {value}")
            else:
                print(f"   ❌ 读取失败")
            continue

        # 测试写入 - 使用不同的测试值
        if "手动转速" in name:
            test_value = 1500  # 转速测试值
        elif "数量" in name:
            test_value = 4000000000  # 大数值测试（接近32位上限）
        elif "距离" in name:
            test_value = 123456789  # 距离测试值
        else:
            test_value = 12345678  # 默认测试值

        print(f"   📤 写入测试值: {test_value} (32位整数)")

        if modbus.write_double_word_register(slave_addr, addr, test_value):
            print(f"   ✅ 写入成功")

            # 等待一小段时间
            time.sleep(0.1)

            # 读取验证
            read_value = modbus.read_double_word_register(slave_addr, addr)
            if read_value is not None:
                print(f"   📥 读取值: {read_value}")
                if read_value == test_value:
                    print(f"   ✅ 读写验证成功")
                else:
                    print(f"   ⚠️  读写值不匹配 (写入: {test_value}, 读取: {read_value})")
            else:
                print(f"   ❌ 读取失败")
        else:
            print(f"   ❌ 写入失败")

    modbus.disconnect()
    return True

def test_bit_operations():
    """测试位操作功能"""
    print("\n" + "=" * 50)
    print("测试位操作功能")
    print("=" * 50)

    # 创建Modbus连接
    modbus = ModbusRTU(port="COM1", baudrate=9600, timeout=1.0)

    if not modbus.connect():
        print("❌ 无法连接到Modbus设备")
        return False

    print("✅ Modbus连接成功")
    slave_addr = 1

    # 测试位地址列表 (M10-M15对应Modbus地址10-15)
    bit_addresses = {
        10: {"name": "手自动切换 (M10)", "writable": True},
        11: {"name": "上位机复位按钮 (M11)", "writable": True},
        12: {"name": "复位中 (M12)", "writable": False},
        13: {"name": "待机中 (M13)", "writable": False},
        14: {"name": "自动运行中 (M14)", "writable": False},
        15: {"name": "急停中 (M15)", "writable": False}
    }

    for addr, info in bit_addresses.items():
        print(f"\n🔘 测试 {info['name']} (位地址: {addr})")

        # 读取当前状态
        current_value = modbus.read_coil(slave_addr, addr, 1)
        if current_value is not None and len(current_value) > 0:
            print(f"   📥 当前状态: {current_value[0]}")

            # 如果是可写位，测试写入
            if info['writable']:
                # 测试写入相反的值
                new_value = 1 - current_value[0]
                print(f"   📤 写入测试值: {new_value}")

                if modbus.write_coil(slave_addr, addr, new_value):
                    print(f"   ✅ 写入成功")

                    # 等待一小段时间
                    time.sleep(0.1)

                    # 读取验证
                    verify_value = modbus.read_coil(slave_addr, addr, 1)
                    if verify_value is not None and len(verify_value) > 0:
                        print(f"   📥 验证读取: {verify_value[0]}")
                        if verify_value[0] == new_value:
                            print(f"   ✅ 位操作验证成功")
                        else:
                            print(f"   ⚠️  位操作值不匹配")
                    else:
                        print(f"   ❌ 验证读取失败")
                else:
                    print(f"   ❌ 写入失败")
            else:
                print(f"   ⚠️  {info['name']} 为只读位，跳过写入测试")
        else:
            print(f"   ❌ 读取失败")

    modbus.disconnect()
    return True

def main():
    """主测试函数"""
    print("🚀 开始PLC地址测试")
    print("📋 测试内容:")
    print("   1. 双字寄存器读写功能")
    print("   2. 位操作读写功能")
    print("   3. 权限控制验证")

    try:
        # 测试双字寄存器
        if not test_double_word_registers():
            print("❌ 双字寄存器测试失败")
            return

        # 测试位操作
        if not test_bit_operations():
            print("❌ 位操作测试失败")
            return

        print("\n" + "=" * 50)
        print("🎉 所有测试完成")
        print("=" * 50)

    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")

if __name__ == "__main__":
    main()

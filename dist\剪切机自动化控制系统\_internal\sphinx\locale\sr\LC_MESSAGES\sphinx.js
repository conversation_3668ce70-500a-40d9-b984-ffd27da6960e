Documentation.addTranslations({
    "locale": "sr",
    "messages": {
        "%(filename)s &#8212; %(docstitle)s": "%(filename)s &#8212; %(docstitle)s",
        "&#169; %(copyright_prefix)s %(copyright)s.": "",
        ", in ": ", \u0443 ",
        "About these documents": "",
        "Automatically generated list of changes in version %(version)s": "",
        "C API changes": "",
        "Changes in Version %(version)s &#8212; %(docstitle)s": "\u0418\u0437\u043c\u0435\u043d\u0435 \u0443 \u0432\u0435\u0440\u0437\u0438\u0458\u0438 %(version)s &#8212; %(docstitle)s",
        "Collapse sidebar": "",
        "Complete Table of Contents": "",
        "Contents": "\u0421\u0430\u0434\u0440\u0436\u0430\u0458",
        "Copyright": "",
        "Created using <a href=\"https://www.sphinx-doc.org/\">Sphinx</a> %(sphinx_version)s.": "",
        "Expand sidebar": "",
        "Full index on one page": "",
        "General Index": "",
        "Global Module Index": "",
        "Go": "\u0422\u0440\u0430\u0436\u0438",
        "Hide Search Matches": "",
        "Index": "\u0418\u043d\u0434\u0435\u043a\u0441",
        "Index &#x2013; %(key)s": "",
        "Index pages by letter": "",
        "Indices and tables:": "",
        "Last updated on %(last_updated)s.": "",
        "Library changes": "\u0418\u0437\u043c\u0435\u043d\u0435 \u0443 \u0431\u0438\u0431\u043b\u0438\u043e\u0442\u0435\u0446\u0438",
        "Navigation": "\u041d\u0430\u0432\u0438\u0433\u0430\u0446\u0438\u0458\u0430",
        "Next topic": "\u0421\u043b\u0435\u0434\u0435\u045b\u0438 \u043e\u0434\u0435\u0459\u0430\u043a",
        "Other changes": "\u0414\u0440\u0443\u0433\u0435 \u0438\u0437\u043c\u0435\u043d\u0435",
        "Overview": "\u041f\u0440\u0435\u0433\u043b\u0435\u0434",
        "Please activate JavaScript to enable the search\n    functionality.": "",
        "Preparing search...": "\u041f\u0440\u0438\u043f\u0440\u0435\u043c\u0430 \u043f\u0440\u0435\u0442\u0440\u0430\u0433\u0435...",
        "Previous topic": "\u041f\u0440\u0435\u0442\u0445\u043e\u0434\u043d\u0438 \u043e\u0434\u0435\u0459\u0430\u043a",
        "Quick search": "\u0411\u0440\u0437\u0430 \u043f\u0440\u0435\u0442\u0440\u0430\u0433\u0430",
        "Search": "\u041f\u0440\u0435\u0442\u0440\u0430\u0433\u0430",
        "Search Page": "",
        "Search Results": "\u0420\u0435\u0437\u0443\u043b\u0442\u0430\u0442\u0438 \u043f\u0440\u0435\u0442\u0440\u0430\u0433\u0435",
        "Search finished, found one page matching the search query.": [
            "",
            "",
            ""
        ],
        "Search within %(docstitle)s": "",
        "Searching": "\u041f\u0440\u0435\u0442\u0440\u0430\u0436\u0443\u0458\u0435 \u0441\u0435",
        "Searching for multiple words only shows matches that contain\n    all words.": "",
        "Show Source": "\u0418\u0437\u0432\u043e\u0440\u043d\u0438 \u043a\u043e\u0434",
        "Table of Contents": "\u0421\u0430\u0434\u0440\u0436\u0430\u0458",
        "This Page": "\u041e\u0432\u0430 \u0441\u0442\u0440\u0430\u043d\u0438\u0446\u0430",
        "Welcome! This is": "\u0414\u043e\u0431\u0440\u043e \u0434\u043e\u0448\u043b\u0438! \u041e\u0432\u043e \u0458\u0435",
        "Your search did not match any documents. Please make sure that all words are spelled correctly and that you've selected enough categories.": "",
        "all functions, classes, terms": "",
        "can be huge": "",
        "last updated": "\u043f\u043e\u0441\u043b\u0435\u0434\u045a\u0430 \u0438\u0437\u043c\u0435\u043d\u0430",
        "lists all sections and subsections": "",
        "next chapter": "\u043d\u0430\u0440\u0435\u0434\u043d\u0430 \u0433\u043b\u0430\u0432\u0430",
        "previous chapter": "\u043f\u0440\u0435\u0442\u0445\u043e\u0434\u043d\u0430 \u0433\u043b\u0430\u0432\u0430",
        "quick access to all modules": "",
        "search": "\u0442\u0440\u0430\u0436\u0438",
        "search this documentation": "",
        "the documentation for": "\u0434\u043e\u043a\u0443\u043c\u0435\u043d\u0442\u0430\u0446\u0438\u0458\u0430"
    },
    "plural_expr": "(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2)"
});
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动重连功能测试脚本
"""

import sys
import time
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import QTimer
from main import CuttingMachineController

def test_auto_reconnect():
    """测试自动重连功能"""
    app = QApplication(sys.argv)
    
    # 创建主窗口
    window = CuttingMachineController()
    window.show()
    
    # 显示测试说明
    msg = QMessageBox()
    msg.setWindowTitle("自动重连功能测试")
    msg.setText("""
自动重连功能已添加到应用中！

功能特点：
1. 在连接区域新增了"自动重连"复选框
2. 当连接断开时，如果启用了自动重连，系统会自动尝试重新连接
3. 最多重试5次，每次间隔5秒
4. 重连成功后会自动恢复所有功能

测试步骤：
1. 勾选"自动重连"复选框
2. 建立连接（串口或TCP）
3. 人为断开连接（如拔掉串口线或关闭TCP服务器）
4. 观察系统是否自动尝试重连

注意：手动点击"断开"按钮不会触发自动重连。
    """)
    msg.setStandardButtons(QMessageBox.Ok)
    msg.exec_()
    
    # 启动应用
    sys.exit(app.exec_())

if __name__ == "__main__":
    test_auto_reconnect()

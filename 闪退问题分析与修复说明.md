# 剪切机自动化控制系统 - 闪退问题分析与修复说明

## 问题描述
用户反映程序在连接PLC后会出现闪退现象，程序突然关闭，没有明显的错误提示。

## 问题分析

### 1. 根本原因
通过代码分析和日志检查，发现主要问题出现在以下几个方面：

#### 1.1 定时器异常处理不足
- `update_status()` 函数每3秒执行一次，用于读取PLC状态
- `update_port_monitor()` 函数每2秒执行一次，用于更新通信统计
- `update_status_time()` 函数每秒执行一次，用于更新时间显示
- 当PLC通信出现异常时，这些定时器回调函数中的未捕获异常会导致程序崩溃

#### 1.2 UI更新时的线程安全问题
- 在定时器回调中直接更新UI控件
- 当UI控件不存在或已被销毁时，访问会导致异常
- 缺少对UI控件存在性的检查

#### 1.3 PLC通信异常处理不完整
- 读取寄存器时的超时异常
- 网络连接中断异常
- 数据格式错误异常
- 这些异常如果没有被正确捕获，会导致程序终止

#### 1.4 变量未定义问题
- 某些函数中缺少必要的变量定义（如`current_modbus`）
- 导致运行时出现NameError异常

### 2. 具体问题位置

#### 2.1 update_status() 函数
```python
# 原始代码缺少完整的异常处理
def update_status(self):
    # 直接访问UI控件，没有检查控件是否存在
    self.connection_status_label.setText("未连接")
    # 直接调用PLC通信函数，没有捕获所有可能的异常
    data = current_modbus.read_register(...)
```

#### 2.2 check_infrared_sensor() 函数
```python
# 原始代码对UI更新异常处理不足
if hasattr(self, 'infrared_status_label'):
    self.infrared_status_label.setText("🟢 有料")  # 可能导致异常
```

#### 2.3 定时器回调函数
- 缺少顶层的try-catch块
- 异常发生时没有适当的恢复机制

## 修复方案

### 1. 增强异常处理

#### 1.1 修复 update_status() 函数
```python
def update_status(self):
    """更新状态信息 - 增强异常处理版本"""
    try:
        # 主要逻辑
        current_modbus = self.get_current_modbus()
        if not current_modbus.is_connected():
            # UI更新也要包装在try-catch中
            try:
                self.connection_status_label.setText("未连接")
            except Exception as ui_error:
                print(f"更新UI状态时发生错误: {ui_error}")
            return
        
        # 每个PLC操作都单独处理异常
        try:
            length_data = current_modbus.read_register(...)
        except Exception as e:
            print(f"读取长度数据错误: {e}")
            # 继续执行其他操作，不中断整个函数
            
    except Exception as main_error:
        print(f"update_status主函数发生严重错误: {main_error}")
        # 发生严重错误时停止定时器，防止持续崩溃
        try:
            self.status_timer.stop()
        except:
            pass
```

#### 1.2 修复 check_infrared_sensor() 函数
```python
def check_infrared_sensor(self):
    try:
        # 检查UI控件是否存在且有效
        try:
            if hasattr(self, 'infrared_status_label') and self.infrared_status_label:
                self.infrared_status_label.setText("🟢 有料")
        except Exception as ui_error:
            print(f"更新红外感应器状态显示时发生错误: {ui_error}")
        
        # PLC通信也要单独处理
        try:
            data = current_modbus.read_coil(...)
        except Exception as read_error:
            print(f"读取红外感应器数据时发生错误: {read_error}")
            return None
            
    except Exception as e:
        print(f"检查红外感应器状态主函数发生错误: {e}")
        return None
```

#### 1.3 修复变量未定义问题
```python
def read_all_parameters(self):
    # 添加缺少的变量定义
    current_modbus = self.get_current_modbus()
    if not current_modbus.is_connected():
        return
```

### 2. 增强程序稳定性

#### 2.1 全局异常处理
```python
def handle_exception(exc_type, exc_value, exc_traceback):
    if issubclass(exc_type, KeyboardInterrupt):
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return
    
    print("发生未捕获的异常:")
    print(''.join(traceback.format_exception(exc_type, exc_value, exc_traceback)))
    
    # 保存错误日志
    try:
        with open("crash_error.log", "a", encoding="utf-8") as f:
            f.write(f"错误时间: {datetime.datetime.now()}\n")
            f.write(''.join(traceback.format_exception(exc_type, exc_value, exc_traceback)))
    except:
        pass

sys.excepthook = handle_exception
```

#### 2.2 定时器安全机制
- 在定时器回调中添加顶层异常处理
- 异常发生时不停止定时器，而是记录错误并继续运行
- 连续异常时才停止定时器

#### 2.3 UI控件安全访问
- 访问UI控件前检查控件是否存在
- 使用hasattr()和None检查
- UI更新操作包装在独立的try-catch中

## 修复效果

### 1. 提高程序稳定性
- 程序不再因为单个异常而崩溃
- 定时器异常不会导致程序终止
- UI更新异常被安全处理

### 2. 增强错误诊断能力
- 详细的错误日志记录
- 异常发生时的上下文信息
- 便于问题定位和调试

### 3. 改善用户体验
- 程序运行更加稳定
- 异常情况下程序仍能继续工作
- 错误信息更加友好

## 测试建议

### 1. 连接测试
1. 启动程序
2. 连接PLC
3. 观察程序是否稳定运行
4. 检查状态更新是否正常

### 2. 异常测试
1. 在PLC连接状态下断开网络
2. 观察程序是否能正常处理连接中断
3. 重新连接后程序是否能恢复正常

### 3. 长时间运行测试
1. 让程序连续运行数小时
2. 观察是否出现内存泄漏或性能问题
3. 检查日志文件中是否有异常记录

## 后续优化建议

### 1. 添加连接状态监控
- 实时监控PLC连接状态
- 连接异常时自动重连
- 连接状态变化时的用户提示

### 2. 优化定时器机制
- 根据连接状态动态调整更新频率
- 实现智能的异常恢复机制
- 添加定时器健康检查

### 3. 完善日志系统
- 分级日志记录（DEBUG、INFO、WARNING、ERROR）
- 日志文件自动轮转
- 远程日志上传功能

## 总结

通过以上修复，程序的稳定性得到了显著提升。主要改进包括：

1. **全面的异常处理**：每个可能出错的地方都添加了适当的异常处理
2. **UI安全访问**：确保UI控件访问的安全性
3. **定时器稳定性**：定时器回调函数不再因异常而导致程序崩溃
4. **错误诊断**：详细的错误日志帮助快速定位问题

这些修复应该能够解决连接PLC后程序闪退的问题，让程序运行更加稳定可靠。

/*
 * This script contains the language-specific data used by searchtools.js,
 * namely the list of stopwords, stemmer, scorer and splitter.
 */

var stopwords = {{ search_language_stop_words }};

{% if search_language_stemming_code %}
/* Non-minified version is copied as a separate JS file, if available */
{{ search_language_stemming_code|safe }}
{% endif -%}

{% if search_scorer_tool %}
{{ search_scorer_tool|safe }}
{% endif -%}

{% if search_word_splitter_code %}
{{ search_word_splitter_code }}
{% endif -%}

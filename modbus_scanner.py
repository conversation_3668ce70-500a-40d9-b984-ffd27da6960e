#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Modbus地址扫描工具
专为DOP-107BV HMI设计，用于扫描和监控Modbus地址
"""

import sys
import os
import time
import json
import binascii
import logging
from logging.handlers import TimedRotatingFileHandler
import serial
import serial.tools.list_ports
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QComboBox, QSpinBox, QGroupBox, QTextEdit, QTableWidget,
    QTableWidgetItem, QHeaderView, QCheckBox, QFileDialog, QMessageBox, QTabWidget,
    QSplitter, QFrame, QDoubleSpinBox, QLineEdit, QProgressBar, QRadioButton,
    QButtonGroup
)
from PyQt5.QtCore import QTimer, Qt, QDateTime, QThread, pyqtSignal
from PyQt5.QtGui import QFont, QColor

# 设置日志
log_dir = os.path.join(os.getcwd(), "logs")
if not os.path.exists(log_dir):
    os.makedirs(log_dir)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s %(levelname)s: %(message)s',
    handlers=[
        TimedRotatingFileHandler(os.path.join(log_dir, "modbus_scanner.log"),
                                when="midnight", backupCount=7, encoding="utf-8"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 尝试导入ModbusTCP类
try:
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    from main import ModbusTCP
    TCP_AVAILABLE = True
except ImportError:
    TCP_AVAILABLE = False
    ModbusTCP = None

class ModbusRTU:
    """Modbus RTU通信类"""
    def __init__(self, port=None, baudrate=9600, bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE, stopbits=serial.STOPBITS_ONE, timeout=0.5):
        self.serial = None
        self.port = port
        self.baudrate = baudrate
        self.bytesize = bytesize
        self.parity = parity
        self.stopbits = stopbits
        self.timeout = timeout
        self.connected = False
        self.rx_data = []  # 接收的数据
        self.tx_data = []  # 发送的数据
        self.error_count = 0  # 错误计数
        self.success_count = 0  # 成功计数
        self.max_retries = 3  # 最大重试次数

    def connect(self):
        """连接串口"""
        try:
            if self.is_connected():
                self.disconnect()
            self.serial = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                bytesize=self.bytesize,
                parity=self.parity,
                stopbits=self.stopbits,
                timeout=self.timeout
            )
            self.connected = True
            logger.info(f"串口连接成功: {self.port}, 波特率: {self.baudrate}")
            return True
        except Exception as e:
            logger.error(f"连接错误: {e}")
            print(f"连接错误: {e}")
            self.connected = False
            return False

    def disconnect(self):
        """断开串口连接"""
        try:
            if self.serial and self.serial.is_open:
                self.serial.close()
                logger.info("串口已断开")
        except Exception as e:
            logger.error(f"断开连接错误: {e}")
            print(f"断开连接错误: {e}")
        finally:
            self.connected = False

    def is_connected(self):
        """检查是否已连接"""
        try:
            return self.connected and self.serial and self.serial.is_open
        except:
            self.connected = False
            return False

    def calculate_crc(self, data):
        """计算CRC校验码"""
        crc = 0xFFFF
        for byte in data:
            crc ^= byte
            for _ in range(8):
                if crc & 0x0001:
                    crc = (crc >> 1) ^ 0xA001
                else:
                    crc = crc >> 1
        return crc.to_bytes(2, byteorder='little')

    def read_register(self, slave_addr, register_addr, register_count=1):
        """读取保持寄存器数据"""
        if not self.is_connected():
            logger.warning("尝试读取寄存器但串口未连接")
            self.error_count += 1
            return None
        request = bytearray([
            slave_addr, 0x03, register_addr >> 8, register_addr & 0xFF, 0x00, register_count
        ])
        request += self.calculate_crc(request)
        self.tx_data.append({
            'time': time.strftime('%H:%M:%S'),
            'data': binascii.hexlify(request).decode('ascii'),
            'type': '读寄存器'
        })
        logger.debug(f"发送: {binascii.hexlify(request).decode('ascii')}")
        try:
            self.serial.write(request)
            response = self.serial.read(5 + register_count * 2)
            self.rx_data.append({
                'time': time.strftime('%H:%M:%S'),
                'data': binascii.hexlify(response).decode('ascii') if response else 'No response',
                'type': '读寄存器响应'
            })
            logger.debug(f"接收: {binascii.hexlify(response).decode('ascii') if response else 'No response'}")
            if len(response) < 5:
                logger.debug("响应数据不完整")
                self.error_count += 1
                return None
            if response[0] != slave_addr or response[1] != 0x03:
                logger.debug("响应格式错误")
                self.error_count += 1
                return None
            data_length = response[2]
            self.success_count += 1
            data = []
            for i in range(0, data_length, 2):
                if 3 + i + 1 < len(response):
                    value = (response[3 + i] << 8) | response[3 + i + 1]
                    data.append(value)
            return data
        except Exception as e:
            logger.error(f"读取寄存器错误: {e}")
            self.error_count += 1
            return None

    def read_input_register(self, slave_addr, register_addr, register_count=1):
        """读取输入寄存器数据"""
        if not self.is_connected():
            logger.warning("尝试读取输入寄存器但串口未连接")
            self.error_count += 1
            return None
        request = bytearray([
            slave_addr, 0x04, register_addr >> 8, register_addr & 0xFF, 0x00, register_count
        ])
        request += self.calculate_crc(request)
        self.tx_data.append({
            'time': time.strftime('%H:%M:%S'),
            'data': binascii.hexlify(request).decode('ascii'),
            'type': '读输入寄存器'
        })
        logger.debug(f"发送: {binascii.hexlify(request).decode('ascii')}")
        try:
            self.serial.write(request)
            response = self.serial.read(5 + register_count * 2)
            self.rx_data.append({
                'time': time.strftime('%H:%M:%S'),
                'data': binascii.hexlify(response).decode('ascii') if response else 'No response',
                'type': '读输入寄存器响应'
            })
            logger.debug(f"接收: {binascii.hexlify(response).decode('ascii') if response else 'No response'}")
            if len(response) < 5:
                logger.debug("响应数据不完整")
                self.error_count += 1
                return None
            if response[0] != slave_addr or response[1] != 0x04:
                logger.debug("响应格式错误")
                self.error_count += 1
                return None
            data_length = response[2]
            self.success_count += 1
            data = []
            for i in range(0, data_length, 2):
                if 3 + i + 1 < len(response):
                    value = (response[3 + i] << 8) | response[3 + i + 1]
                    data.append(value)
            return data
        except Exception as e:
            logger.error(f"读取输入寄存器错误: {e}")
            self.error_count += 1
            return None

    def read_coil(self, slave_addr, coil_addr, coil_count=1):
        """读取线圈状态"""
        if not self.is_connected():
            logger.warning("尝试读取线圈但串口未连接")
            self.error_count += 1
            return None
        request = bytearray([
            slave_addr, 0x01, coil_addr >> 8, coil_addr & 0xFF, 0x00, coil_count
        ])
        request += self.calculate_crc(request)
        self.tx_data.append({
            'time': time.strftime('%H:%M:%S'),
            'data': binascii.hexlify(request).decode('ascii'),
            'type': '读线圈'
        })
        logger.debug(f"发送: {binascii.hexlify(request).decode('ascii')}")
        try:
            self.serial.write(request)
            # 计算响应长度：1字节从站地址 + 1字节功能码 + 1字节字节数 + N字节数据 + 2字节CRC
            byte_count = (coil_count + 7) // 8  # 向上取整到字节
            response = self.serial.read(5 + byte_count)
            self.rx_data.append({
                'time': time.strftime('%H:%M:%S'),
                'data': binascii.hexlify(response).decode('ascii') if response else 'No response',
                'type': '读线圈响应'
            })
            logger.debug(f"接收: {binascii.hexlify(response).decode('ascii') if response else 'No response'}")
            if len(response) < 5:
                logger.debug("响应数据不完整")
                self.error_count += 1
                return None
            if response[0] != slave_addr or response[1] != 0x01:
                logger.debug("响应格式错误")
                self.error_count += 1
                return None
            data_length = response[2]
            self.success_count += 1
            data = []
            for i in range(data_length):
                if 3 + i < len(response):
                    byte_val = response[3 + i]
                    for bit in range(8):
                        if i * 8 + bit < coil_count:
                            data.append((byte_val >> bit) & 1)
            return data
        except Exception as e:
            logger.error(f"读取线圈错误: {e}")
            self.error_count += 1
            return None

    def read_discrete_input(self, slave_addr, input_addr, input_count=1):
        """读取离散输入状态"""
        if not self.is_connected():
            logger.warning("尝试读取离散输入但串口未连接")
            self.error_count += 1
            return None
        request = bytearray([
            slave_addr, 0x02, input_addr >> 8, input_addr & 0xFF, 0x00, input_count
        ])
        request += self.calculate_crc(request)
        self.tx_data.append({
            'time': time.strftime('%H:%M:%S'),
            'data': binascii.hexlify(request).decode('ascii'),
            'type': '读离散输入'
        })
        logger.debug(f"发送: {binascii.hexlify(request).decode('ascii')}")
        try:
            self.serial.write(request)
            # 计算响应长度：1字节从站地址 + 1字节功能码 + 1字节字节数 + N字节数据 + 2字节CRC
            byte_count = (input_count + 7) // 8  # 向上取整到字节
            response = self.serial.read(5 + byte_count)
            self.rx_data.append({
                'time': time.strftime('%H:%M:%S'),
                'data': binascii.hexlify(response).decode('ascii') if response else 'No response',
                'type': '读离散输入响应'
            })
            logger.debug(f"接收: {binascii.hexlify(response).decode('ascii') if response else 'No response'}")
            if len(response) < 5:
                logger.debug("响应数据不完整")
                self.error_count += 1
                return None
            if response[0] != slave_addr or response[1] != 0x02:
                logger.debug("响应格式错误")
                self.error_count += 1
                return None
            data_length = response[2]
            self.success_count += 1
            data = []
            for i in range(data_length):
                if 3 + i < len(response):
                    byte_val = response[3 + i]
                    for bit in range(8):
                        if i * 8 + bit < input_count:
                            data.append((byte_val >> bit) & 1)
            return data
        except Exception as e:
            logger.error(f"读取离散输入错误: {e}")
            self.error_count += 1
            return None


class ScanThread(QThread):
    """Modbus地址扫描线程"""
    # 信号定义
    progress_signal = pyqtSignal(int)  # 扫描进度信号
    result_signal = pyqtSignal(dict)   # 扫描结果信号
    status_signal = pyqtSignal(str)    # 状态信息信号
    finished_signal = pyqtSignal()     # 扫描完成信号

    def __init__(self, modbus, slave_addr, start_addr, end_addr,
                 register_type, batch_size=10, scan_delay=0.01):
        super().__init__()
        self.modbus = modbus
        self.slave_addr = slave_addr
        self.start_addr = start_addr
        self.end_addr = end_addr
        self.register_type = register_type  # 'holding', 'input', 'coil', 'discrete'
        self.batch_size = batch_size
        self.scan_delay = scan_delay
        self.stop_flag = False

    def stop(self):
        """停止扫描"""
        self.stop_flag = True

    def run(self):
        """执行扫描"""
        if not self.modbus.is_connected():
            self.status_signal.emit("错误：Modbus未连接")
            return

        total_addresses = self.end_addr - self.start_addr + 1
        scanned = 0
        active_addresses = {}

        self.status_signal.emit(f"开始扫描 {self.register_type} 寄存器，地址范围: {self.start_addr}-{self.end_addr}")

        # 根据寄存器类型选择读取函数
        if self.register_type == 'holding':
            read_func = self.modbus.read_register
        elif self.register_type == 'input':
            read_func = self.modbus.read_input_register
        elif self.register_type == 'coil':
            read_func = self.modbus.read_coil
        elif self.register_type == 'discrete':
            read_func = self.modbus.read_discrete_input
        else:
            self.status_signal.emit("错误：未知的寄存器类型")
            return

        # 分批扫描地址
        for addr in range(self.start_addr, self.end_addr + 1, self.batch_size):
            if self.stop_flag:
                self.status_signal.emit("扫描已停止")
                break

            end = min(addr + self.batch_size - 1, self.end_addr)
            count = end - addr + 1

            # 更新进度
            scanned += count
            progress = int((scanned / total_addresses) * 100)
            self.progress_signal.emit(progress)

            # 读取寄存器
            try:
                values = read_func(self.slave_addr, addr, count)
                if values is not None:
                    # 记录有响应的地址
                    for i, value in enumerate(values):
                        current_addr = addr + i
                        active_addresses[current_addr] = value
                        self.status_signal.emit(f"发现活动地址: {current_addr} = {value}")
            except Exception as e:
                self.status_signal.emit(f"扫描错误: {str(e)}")

            # 短暂延迟，避免通信过载
            time.sleep(self.scan_delay)

        # 发送结果
        self.result_signal.emit(active_addresses)
        self.status_signal.emit(f"扫描完成，发现 {len(active_addresses)} 个活动地址")
        self.finished_signal.emit()


class MonitorThread(QThread):
    """Modbus地址监控线程"""
    # 信号定义
    change_signal = pyqtSignal(dict)   # 值变化信号
    status_signal = pyqtSignal(str)    # 状态信息信号

    def __init__(self, modbus, slave_addr, addresses, register_types, interval=1.0):
        super().__init__()
        self.modbus = modbus
        self.slave_addr = slave_addr
        self.addresses = addresses  # {地址: 寄存器类型}
        self.register_types = register_types  # {地址: 'holding'/'input'/'coil'/'discrete'}
        self.interval = interval
        self.stop_flag = False
        self.values = {}  # 存储当前值

    def stop(self):
        """停止监控"""
        self.stop_flag = True

    def run(self):
        """执行监控"""
        if not self.modbus.is_connected():
            self.status_signal.emit("错误：Modbus未连接")
            return

        self.status_signal.emit(f"开始监控 {len(self.addresses)} 个地址")

        # 初始读取所有地址的值
        for addr, reg_type in self.register_types.items():
            if self.stop_flag:
                break

            value = self._read_address(addr, reg_type)
            if value is not None:
                self.values[addr] = value

        # 持续监控
        while not self.stop_flag:
            changes = {}

            # 读取所有地址并检查变化
            for addr, reg_type in self.register_types.items():
                if self.stop_flag:
                    break

                value = self._read_address(addr, reg_type)
                if value is not None:
                    # 检查值是否变化
                    if addr in self.values and self.values[addr] != value:
                        changes[addr] = {
                            'old': self.values[addr],
                            'new': value,
                            'type': reg_type
                        }

                    # 更新当前值
                    self.values[addr] = value

            # 如果有变化，发送信号
            if changes:
                self.change_signal.emit(changes)
                for addr, change in changes.items():
                    self.status_signal.emit(
                        f"地址 {addr} ({change['type']}) 值变化: {change['old']} -> {change['new']}"
                    )

            # 等待下一个监控周期
            time.sleep(self.interval)

    def _read_address(self, addr, reg_type):
        """读取指定地址的值"""
        try:
            if reg_type == 'holding':
                result = self.modbus.read_register(self.slave_addr, addr, 1)
            elif reg_type == 'input':
                result = self.modbus.read_input_register(self.slave_addr, addr, 1)
            elif reg_type == 'coil':
                result = self.modbus.read_coil(self.slave_addr, addr, 1)
            elif reg_type == 'discrete':
                result = self.modbus.read_discrete_input(self.slave_addr, addr, 1)
            else:
                return None

            return result[0] if result else None
        except Exception as e:
            logger.error(f"读取地址 {addr} 错误: {e}")
            return None


class ModbusScanner(QMainWindow):
    """Modbus地址扫描工具主界面"""
    def __init__(self):
        super().__init__()

        # 通讯协议类型和对象
        self.protocol_type = "RTU"  # 默认使用RTU协议
        self.modbus = ModbusRTU()
        if TCP_AVAILABLE:
            self.modbus_tcp = ModbusTCP()
        else:
            self.modbus_tcp = None

        # 扫描和监控线程
        self.scan_thread = None
        self.monitor_thread = None

        # 扫描结果
        self.active_addresses = {}  # {地址: 值}
        self.register_types = {}    # {地址: 寄存器类型}
        self.monitored_addresses = set()  # 正在监控的地址集合

        # 变化记录
        self.changes = []  # 存储地址值变化记录

        # 设置界面
        self.setWindowTitle("Modbus地址扫描工具 - 专为DOP-107BV HMI设计")
        self.setGeometry(100, 100, 1200, 800)

        # 创建主界面
        self.init_ui()

        # 状态更新定时器
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(500)  # 每0.5秒更新一次

    def get_current_modbus(self):
        """获取当前活动的Modbus通讯对象"""
        if self.protocol_type == "TCP" and self.modbus_tcp:
            return self.modbus_tcp
        else:
            return self.modbus

    def init_ui(self):
        """初始化用户界面"""
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout(central_widget)

        # 创建顶部控制区域
        control_layout = QHBoxLayout()

        # 连接设置组
        connection_group = QGroupBox("连接设置")
        connection_layout = QGridLayout()

        # 协议选择
        connection_layout.addWidget(QLabel("协议:"), 0, 0)
        self.protocol_combo = QComboBox()
        self.protocol_combo.addItem("Modbus RTU", "RTU")
        if TCP_AVAILABLE:
            self.protocol_combo.addItem("Modbus TCP", "TCP")
        self.protocol_combo.currentTextChanged.connect(self.on_protocol_changed)
        connection_layout.addWidget(self.protocol_combo, 0, 1)

        # 串口选择
        self.port_label = QLabel("串口:")
        connection_layout.addWidget(self.port_label, 1, 0)
        self.port_combo = QComboBox()
        self.refresh_ports()
        connection_layout.addWidget(self.port_combo, 1, 1)

        # TCP地址设置
        self.tcp_host_label = QLabel("IP地址:")
        connection_layout.addWidget(self.tcp_host_label, 1, 0)
        self.tcp_host_edit = QLineEdit("*************")
        connection_layout.addWidget(self.tcp_host_edit, 1, 1)

        # TCP端口设置
        self.tcp_port_label = QLabel("TCP端口:")
        connection_layout.addWidget(self.tcp_port_label, 2, 0)
        self.tcp_port_spin = QSpinBox()
        self.tcp_port_spin.setRange(1, 65535)
        self.tcp_port_spin.setValue(502)
        connection_layout.addWidget(self.tcp_port_spin, 2, 1)

        # 刷新按钮
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.refresh_ports)
        connection_layout.addWidget(self.refresh_btn, 1, 2)

        # 波特率
        self.baudrate_label = QLabel("波特率:")
        connection_layout.addWidget(self.baudrate_label, 3, 0)
        self.baudrate_combo = QComboBox()
        for baudrate in [4800, 9600, 19200, 38400, 57600, 115200]:
            self.baudrate_combo.addItem(str(baudrate))
        self.baudrate_combo.setCurrentText("9600")
        connection_layout.addWidget(self.baudrate_combo, 3, 1)

        # 数据位
        self.bytesize_label = QLabel("数据位:")
        connection_layout.addWidget(self.bytesize_label, 4, 0)
        self.bytesize_combo = QComboBox()
        self.bytesize_combo.addItem("5", serial.FIVEBITS)
        self.bytesize_combo.addItem("6", serial.SIXBITS)
        self.bytesize_combo.addItem("7", serial.SEVENBITS)
        self.bytesize_combo.addItem("8", serial.EIGHTBITS)
        self.bytesize_combo.setCurrentIndex(3)  # 默认8位
        connection_layout.addWidget(self.bytesize_combo, 4, 1)

        # 校验位
        self.parity_label = QLabel("校验位:")
        connection_layout.addWidget(self.parity_label, 5, 0)
        self.parity_combo = QComboBox()
        self.parity_combo.addItem("无", serial.PARITY_NONE)
        self.parity_combo.addItem("奇校验", serial.PARITY_ODD)
        self.parity_combo.addItem("偶校验", serial.PARITY_EVEN)
        self.parity_combo.addItem("标记", serial.PARITY_MARK)
        self.parity_combo.addItem("空格", serial.PARITY_SPACE)
        self.parity_combo.setCurrentIndex(0)  # 默认无校验
        connection_layout.addWidget(self.parity_combo, 5, 1)

        # 停止位
        self.stopbits_label = QLabel("停止位:")
        connection_layout.addWidget(self.stopbits_label, 6, 0)
        self.stopbits_combo = QComboBox()
        self.stopbits_combo.addItem("1", serial.STOPBITS_ONE)
        self.stopbits_combo.addItem("1.5", serial.STOPBITS_ONE_POINT_FIVE)
        self.stopbits_combo.addItem("2", serial.STOPBITS_TWO)
        self.stopbits_combo.setCurrentIndex(0)  # 默认1位
        connection_layout.addWidget(self.stopbits_combo, 6, 1)

        # 超时设置
        connection_layout.addWidget(QLabel("超时(秒):"), 7, 0)
        self.timeout_spin = QDoubleSpinBox()
        self.timeout_spin.setRange(0.1, 10.0)
        self.timeout_spin.setSingleStep(0.1)
        self.timeout_spin.setValue(0.5)
        connection_layout.addWidget(self.timeout_spin, 7, 1)

        # 从站地址
        connection_layout.addWidget(QLabel("从站地址:"), 8, 0)
        self.slave_addr_spin = QSpinBox()
        self.slave_addr_spin.setRange(1, 247)
        self.slave_addr_spin.setValue(1)
        connection_layout.addWidget(self.slave_addr_spin, 8, 1)

        # 连接按钮
        self.connect_btn = QPushButton("连接")
        self.connect_btn.clicked.connect(self.toggle_connection)
        connection_layout.addWidget(self.connect_btn, 8, 2)

        connection_group.setLayout(connection_layout)
        control_layout.addWidget(connection_group)

        # 扫描设置组
        scan_group = QGroupBox("扫描设置")
        scan_layout = QGridLayout()

        # 寄存器类型
        scan_layout.addWidget(QLabel("寄存器类型:"), 0, 0)
        self.register_type_group = QButtonGroup()

        self.holding_radio = QRadioButton("保持寄存器 (03)")
        self.holding_radio.setChecked(True)
        self.register_type_group.addButton(self.holding_radio, 1)
        scan_layout.addWidget(self.holding_radio, 0, 1)

        self.input_radio = QRadioButton("输入寄存器 (04)")
        self.register_type_group.addButton(self.input_radio, 2)
        scan_layout.addWidget(self.input_radio, 1, 1)

        self.coil_radio = QRadioButton("线圈 (01)")
        self.register_type_group.addButton(self.coil_radio, 3)
        scan_layout.addWidget(self.coil_radio, 2, 1)

        self.discrete_radio = QRadioButton("离散输入 (02)")
        self.register_type_group.addButton(self.discrete_radio, 4)
        scan_layout.addWidget(self.discrete_radio, 3, 1)

        # 地址范围
        scan_layout.addWidget(QLabel("起始地址:"), 0, 2)
        self.start_addr_spin = QSpinBox()
        self.start_addr_spin.setRange(0, 65535)
        self.start_addr_spin.setValue(0)
        scan_layout.addWidget(self.start_addr_spin, 0, 3)

        scan_layout.addWidget(QLabel("结束地址:"), 1, 2)
        self.end_addr_spin = QSpinBox()
        self.end_addr_spin.setRange(0, 65535)
        self.end_addr_spin.setValue(1000)
        scan_layout.addWidget(self.end_addr_spin, 1, 3)

        # 批处理大小
        scan_layout.addWidget(QLabel("批处理大小:"), 2, 2)
        self.batch_size_spin = QSpinBox()
        self.batch_size_spin.setRange(1, 125)
        self.batch_size_spin.setValue(10)
        scan_layout.addWidget(self.batch_size_spin, 2, 3)

        # 扫描延迟
        scan_layout.addWidget(QLabel("扫描延迟(秒):"), 3, 2)
        self.scan_delay_spin = QDoubleSpinBox()
        self.scan_delay_spin.setRange(0.01, 1.0)
        self.scan_delay_spin.setSingleStep(0.01)
        self.scan_delay_spin.setValue(0.05)
        scan_layout.addWidget(self.scan_delay_spin, 3, 3)

        # 扫描按钮
        self.scan_btn = QPushButton("开始扫描")
        self.scan_btn.clicked.connect(self.toggle_scan)
        self.scan_btn.setEnabled(False)
        scan_layout.addWidget(self.scan_btn, 4, 0, 1, 4)

        # 扫描进度条
        self.scan_progress = QProgressBar()
        self.scan_progress.setValue(0)
        scan_layout.addWidget(self.scan_progress, 5, 0, 1, 4)

        scan_group.setLayout(scan_layout)
        control_layout.addWidget(scan_group)

        # 监控设置组
        monitor_group = QGroupBox("监控设置")
        monitor_layout = QGridLayout()

        # 监控间隔
        monitor_layout.addWidget(QLabel("监控间隔(秒):"), 0, 0)
        self.monitor_interval_spin = QDoubleSpinBox()
        self.monitor_interval_spin.setRange(0.1, 10.0)
        self.monitor_interval_spin.setSingleStep(0.1)
        self.monitor_interval_spin.setValue(1.0)
        monitor_layout.addWidget(self.monitor_interval_spin, 0, 1)

        # 监控按钮
        self.monitor_btn = QPushButton("开始监控")
        self.monitor_btn.clicked.connect(self.toggle_monitor)
        self.monitor_btn.setEnabled(False)
        monitor_layout.addWidget(self.monitor_btn, 1, 0, 1, 2)

        # 清除按钮
        clear_btn = QPushButton("清除记录")
        clear_btn.clicked.connect(self.clear_records)
        monitor_layout.addWidget(clear_btn, 2, 0)

        # 保存按钮
        save_btn = QPushButton("保存记录")
        save_btn.clicked.connect(self.save_records)
        monitor_layout.addWidget(save_btn, 2, 1)

        monitor_group.setLayout(monitor_layout)
        control_layout.addWidget(monitor_group)

        main_layout.addLayout(control_layout)

        # 创建选项卡
        tabs = QTabWidget()

        # 活动地址选项卡
        active_tab = QWidget()
        active_layout = QVBoxLayout(active_tab)

        # 活动地址表格
        self.active_table = QTableWidget()
        self.active_table.setColumnCount(4)
        self.active_table.setHorizontalHeaderLabels(["地址", "值", "寄存器类型", "监控"])
        self.active_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        active_layout.addWidget(self.active_table)

        tabs.addTab(active_tab, "活动地址")

        # 变化记录选项卡
        changes_tab = QWidget()
        changes_layout = QVBoxLayout(changes_tab)

        # 变化记录表格
        self.changes_table = QTableWidget()
        self.changes_table.setColumnCount(5)
        self.changes_table.setHorizontalHeaderLabels(["时间", "地址", "旧值", "新值", "变化量"])
        self.changes_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        changes_layout.addWidget(self.changes_table)

        tabs.addTab(changes_tab, "变化记录")

        # 日志选项卡
        log_tab = QWidget()
        log_layout = QVBoxLayout(log_tab)

        # 日志文本框
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)

        tabs.addTab(log_tab, "日志")

        main_layout.addWidget(tabs)

        # 状态栏
        self.statusBar = self.statusBar()
        self.status_label = QLabel("就绪")
        self.statusBar.addPermanentWidget(self.status_label)

        # 初始化协议显示
        self.on_protocol_changed()

    def on_protocol_changed(self):
        """协议切换时的处理"""
        protocol_data = self.protocol_combo.currentData()
        if protocol_data:
            self.protocol_type = protocol_data
        else:
            # 如果没有数据，根据文本判断
            if "TCP" in self.protocol_combo.currentText():
                self.protocol_type = "TCP"
            else:
                self.protocol_type = "RTU"

        # 根据协议类型显示/隐藏相应的控件
        if self.protocol_type == "TCP":
            # 隐藏RTU相关控件
            self.port_label.hide()
            self.port_combo.hide()
            self.refresh_btn.hide()
            self.baudrate_label.hide()
            self.baudrate_combo.hide()
            self.bytesize_label.hide()
            self.bytesize_combo.hide()
            self.parity_label.hide()
            self.parity_combo.hide()
            self.stopbits_label.hide()
            self.stopbits_combo.hide()

            # 显示TCP相关控件
            self.tcp_host_label.show()
            self.tcp_host_edit.show()
            self.tcp_port_label.show()
            self.tcp_port_spin.show()
        else:
            # 显示RTU相关控件
            self.port_label.show()
            self.port_combo.show()
            self.refresh_btn.show()
            self.baudrate_label.show()
            self.baudrate_combo.show()
            self.bytesize_label.show()
            self.bytesize_combo.show()
            self.parity_label.show()
            self.parity_combo.show()
            self.stopbits_label.show()
            self.stopbits_combo.show()

            # 隐藏TCP相关控件
            self.tcp_host_label.hide()
            self.tcp_host_edit.hide()
            self.tcp_port_label.hide()
            self.tcp_port_spin.hide()

    def refresh_ports(self):
        """刷新可用串口列表"""
        self.port_combo.clear()
        ports = serial.tools.list_ports.comports()
        for port in ports:
            self.port_combo.addItem(port.device)

    def toggle_connection(self):
        """切换连接状态"""
        try:
            current_modbus = self.get_current_modbus()
            if not current_modbus.is_connected():
                # 连接
                if self.protocol_type == "RTU":
                    # RTU协议连接
                    port = self.port_combo.currentText()
                    baudrate = int(self.baudrate_combo.currentText())
                    bytesize = self.bytesize_combo.currentData()
                    parity = self.parity_combo.currentData()
                    stopbits = self.stopbits_combo.currentData()
                    timeout = self.timeout_spin.value()

                    if not port:
                        QMessageBox.warning(self, "警告", "请选择串口")
                        return

                    # 更新状态栏
                    self.status_label.setText(f"正在连接到 {port}...")
                    QApplication.processEvents()  # 确保UI更新

                    self.modbus.port = port
                    self.modbus.baudrate = baudrate
                    self.modbus.bytesize = bytesize
                    self.modbus.parity = parity
                    self.modbus.stopbits = stopbits
                    self.modbus.timeout = timeout
                    connection_success = self.modbus.connect()
                else:
                    # TCP协议连接
                    if not self.modbus_tcp:
                        QMessageBox.warning(self, "警告", "TCP协议不可用")
                        return

                    host = self.tcp_host_edit.text()
                    port = self.tcp_port_spin.value()
                    timeout = self.timeout_spin.value()

                    if not host:
                        QMessageBox.warning(self, "警告", "请输入IP地址")
                        return

                    # 更新状态栏
                    self.status_label.setText(f"正在连接到 {host}:{port}...")
                    QApplication.processEvents()  # 确保UI更新

                    self.modbus_tcp.host = host
                    self.modbus_tcp.port = port
                    self.modbus_tcp.timeout = timeout
                    connection_success = self.modbus_tcp.connect()

                if connection_success:
                    self.connect_btn.setText("断开")
                    self.scan_btn.setEnabled(True)
                    if self.protocol_type == "RTU":
                        self.add_log_message(f"已连接到 {port}, 波特率 {baudrate}")
                        self.status_label.setText(f"已连接到 {port}")
                    else:
                        self.add_log_message(f"已连接到TCP服务器 {host}:{port}")
                        self.status_label.setText(f"已连接到 {host}:{port}")
                else:
                    if self.protocol_type == "RTU":
                        QMessageBox.critical(self, "错误", f"无法连接到串口 {port}")
                    else:
                        QMessageBox.critical(self, "错误", f"无法连接到TCP服务器 {host}:{port}")
                    self.status_label.setText("连接失败")
            else:
                # 断开连接
                self.status_label.setText("正在断开连接...")
                QApplication.processEvents()  # 确保UI更新

                # 如果正在扫描或监控，先停止
                if self.scan_thread and self.scan_thread.isRunning():
                    self.toggle_scan()

                if self.monitor_thread and self.monitor_thread.isRunning():
                    self.toggle_monitor()

                current_modbus.disconnect()
                self.connect_btn.setText("连接")
                self.scan_btn.setEnabled(False)
                self.monitor_btn.setEnabled(False)
                self.add_log_message("已断开连接")
                self.status_label.setText("已断开连接")
        except Exception as e:
            self.status_label.setText(f"连接操作错误: {str(e)}")
            QMessageBox.critical(self, "错误", f"连接操作发生错误: {str(e)}")
            # 确保断开连接
            try:
                self.modbus.disconnect()
            except:
                pass
            self.connect_btn.setText("连接")
            self.scan_btn.setEnabled(False)
            self.monitor_btn.setEnabled(False)

    def get_register_type(self):
        """获取当前选择的寄存器类型"""
        if self.holding_radio.isChecked():
            return "holding"
        elif self.input_radio.isChecked():
            return "input"
        elif self.coil_radio.isChecked():
            return "coil"
        elif self.discrete_radio.isChecked():
            return "discrete"
        return "holding"  # 默认

    def toggle_scan(self):
        """切换扫描状态"""
        if not self.scan_thread or not self.scan_thread.isRunning():
            # 开始扫描
            start_addr = self.start_addr_spin.value()
            end_addr = self.end_addr_spin.value()
            register_type = self.get_register_type()
            batch_size = self.batch_size_spin.value()
            scan_delay = self.scan_delay_spin.value()
            slave_addr = self.slave_addr_spin.value()

            if start_addr > end_addr:
                QMessageBox.warning(self, "警告", "起始地址不能大于结束地址")
                return

            if end_addr - start_addr > 10000:
                reply = QMessageBox.question(self, "确认",
                                           f"您选择了扫描 {end_addr - start_addr + 1} 个地址，这可能需要较长时间。是否继续？",
                                           QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
                if reply == QMessageBox.No:
                    return

            # 禁用设置控件
            self.start_addr_spin.setEnabled(False)
            self.end_addr_spin.setEnabled(False)
            self.batch_size_spin.setEnabled(False)
            self.scan_delay_spin.setEnabled(False)
            self.holding_radio.setEnabled(False)
            self.input_radio.setEnabled(False)
            self.coil_radio.setEnabled(False)
            self.discrete_radio.setEnabled(False)

            # 创建并启动扫描线程
            current_modbus = self.get_current_modbus()
            self.scan_thread = ScanThread(
                current_modbus, slave_addr, start_addr, end_addr,
                register_type, batch_size, scan_delay
            )
            self.scan_thread.progress_signal.connect(self.update_scan_progress)
            self.scan_thread.result_signal.connect(self.handle_scan_result)
            self.scan_thread.status_signal.connect(self.add_log_message)
            self.scan_thread.finished_signal.connect(self.scan_finished)

            self.scan_thread.start()
            self.scan_btn.setText("停止扫描")
            self.status_label.setText(f"正在扫描 {register_type} 寄存器，地址范围: {start_addr}-{end_addr}")
            self.add_log_message(f"开始扫描 {register_type} 寄存器，地址范围: {start_addr}-{end_addr}")
        else:
            # 停止扫描
            self.scan_thread.stop()
            self.scan_btn.setText("开始扫描")
            self.status_label.setText("扫描已停止")
            self.add_log_message("扫描已停止")

            # 启用设置控件
            self.start_addr_spin.setEnabled(True)
            self.end_addr_spin.setEnabled(True)
            self.batch_size_spin.setEnabled(True)
            self.scan_delay_spin.setEnabled(True)
            self.holding_radio.setEnabled(True)
            self.input_radio.setEnabled(True)
            self.coil_radio.setEnabled(True)
            self.discrete_radio.setEnabled(True)

    def update_scan_progress(self, progress):
        """更新扫描进度"""
        self.scan_progress.setValue(progress)

    def handle_scan_result(self, active_addresses):
        """处理扫描结果"""
        # 更新活动地址
        register_type = self.get_register_type()
        for addr, value in active_addresses.items():
            self.active_addresses[addr] = value
            self.register_types[addr] = register_type

        # 更新活动地址表格
        self.update_active_table()

        # 启用监控按钮
        if self.active_addresses:
            self.monitor_btn.setEnabled(True)

    def scan_finished(self):
        """扫描完成处理"""
        self.scan_btn.setText("开始扫描")

        # 启用设置控件
        self.start_addr_spin.setEnabled(True)
        self.end_addr_spin.setEnabled(True)
        self.batch_size_spin.setEnabled(True)
        self.scan_delay_spin.setEnabled(True)
        self.holding_radio.setEnabled(True)
        self.input_radio.setEnabled(True)
        self.coil_radio.setEnabled(True)
        self.discrete_radio.setEnabled(True)

    def update_active_table(self):
        """更新活动地址表格"""
        self.active_table.setRowCount(0)
        for addr in sorted(self.active_addresses.keys()):
            row = self.active_table.rowCount()
            self.active_table.insertRow(row)

            # 地址
            self.active_table.setItem(row, 0, QTableWidgetItem(str(addr)))

            # 值
            self.active_table.setItem(row, 1, QTableWidgetItem(str(self.active_addresses[addr])))

            # 寄存器类型
            reg_type = self.register_types.get(addr, "unknown")
            self.active_table.setItem(row, 2, QTableWidgetItem(reg_type))

            # 监控复选框
            checkbox = QCheckBox()
            checkbox.setChecked(addr in self.monitored_addresses)
            checkbox.stateChanged.connect(lambda state, a=addr: self.toggle_monitor_address(a, state))
            self.active_table.setCellWidget(row, 3, checkbox)

    def toggle_monitor_address(self, addr, state):
        """切换监控地址"""
        if state:
            self.monitored_addresses.add(addr)
        else:
            self.monitored_addresses.discard(addr)

    def toggle_monitor(self):
        """切换监控状态"""
        if not self.monitor_thread or not self.monitor_thread.isRunning():
            # 开始监控
            if not self.monitored_addresses:
                # 如果没有选择监控地址，监控所有活动地址
                self.monitored_addresses = set(self.active_addresses.keys())
                self.update_active_table()

            interval = self.monitor_interval_spin.value()
            slave_addr = self.slave_addr_spin.value()

            # 创建并启动监控线程
            current_modbus = self.get_current_modbus()
            self.monitor_thread = MonitorThread(
                current_modbus, slave_addr, self.monitored_addresses,
                {addr: self.register_types.get(addr, "holding") for addr in self.monitored_addresses},
                interval
            )
            self.monitor_thread.change_signal.connect(self.handle_value_changes)
            self.monitor_thread.status_signal.connect(self.add_log_message)

            self.monitor_thread.start()
            self.monitor_btn.setText("停止监控")
            self.status_label.setText(f"正在监控 {len(self.monitored_addresses)} 个地址")
            self.add_log_message(f"开始监控 {len(self.monitored_addresses)} 个地址")
        else:
            # 停止监控
            self.monitor_thread.stop()
            self.monitor_btn.setText("开始监控")
            self.status_label.setText("监控已停止")
            self.add_log_message("监控已停止")

    def handle_value_changes(self, changes):
        """处理值变化"""
        # 更新活动地址表格中的值
        for addr, change in changes.items():
            self.active_addresses[addr] = change['new']

        self.update_active_table()

        # 添加到变化记录
        current_time = QDateTime.currentDateTime().toString('yyyy-MM-dd hh:mm:ss')
        for addr, change in changes.items():
            # 记录变化
            self.changes.append({
                'time': current_time,
                'address': addr,
                'old_value': change['old'],
                'new_value': change['new'],
                'diff': change['new'] - change['old']
            })

            # 更新变化记录表格
            row = self.changes_table.rowCount()
            self.changes_table.insertRow(row)
            self.changes_table.setItem(row, 0, QTableWidgetItem(current_time))
            self.changes_table.setItem(row, 1, QTableWidgetItem(str(addr)))
            self.changes_table.setItem(row, 2, QTableWidgetItem(str(change['old'])))
            self.changes_table.setItem(row, 3, QTableWidgetItem(str(change['new'])))
            self.changes_table.setItem(row, 4, QTableWidgetItem(str(change['new'] - change['old'])))

            # 高亮显示
            for col in range(5):
                self.changes_table.item(row, col).setBackground(QColor(255, 255, 0, 100))

        # 滚动到底部
        self.changes_table.scrollToBottom()

    def clear_records(self):
        """清除记录"""
        msgBox = QMessageBox(QMessageBox.Question, "确认", "确定要清除所有记录吗？",
                          QMessageBox.Yes | QMessageBox.No, self)
        msgBox.button(QMessageBox.Yes).setText("是")
        msgBox.button(QMessageBox.No).setText("否")
        msgBox.setDefaultButton(QMessageBox.No)
        reply = msgBox.exec_()
        if reply == QMessageBox.Yes:
            self.changes = []
            self.changes_table.setRowCount(0)
            self.add_log_message("已清除所有记录")

    def save_records(self):
        """保存记录到文件"""
        if not self.changes:
            QMessageBox.information(self, "提示", "没有变化记录可保存")
            return

        filename, _ = QFileDialog.getSaveFileName(self, "保存记录", "", "CSV文件 (*.csv);;JSON文件 (*.json);;所有文件 (*)")
        if not filename:
            return

        try:
            if filename.endswith('.csv'):
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write("时间,地址,旧值,新值,变化量\n")
                    for change in self.changes:
                        f.write(f"{change['time']},{change['address']},{change['old_value']},{change['new_value']},{change['diff']}\n")
            else:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(self.changes, f, indent=4)

            self.add_log_message(f"记录已保存到 {filename}")
            QMessageBox.information(self, "成功", f"记录已保存到 {filename}")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存记录时发生错误: {str(e)}")

    def add_log_message(self, message):
        """添加日志消息"""
        timestamp = time.strftime('%H:%M:%S')
        self.log_text.append(f"[{timestamp}] {message}")
        # 滚动到底部
        self.log_text.verticalScrollBar().setValue(self.log_text.verticalScrollBar().maximum())
        # 同时记录到日志文件
        logger.info(message)

    def update_status(self):
        """更新状态信息"""
        # 检查连接状态
        current_modbus = self.get_current_modbus()
        if not current_modbus.is_connected():
            if self.connect_btn.text() == "断开":
                self.connect_btn.setText("连接")
                self.scan_btn.setEnabled(False)
                self.monitor_btn.setEnabled(False)
                self.add_log_message("检测到连接已断开")

                # 如果正在扫描或监控，停止
                if self.scan_thread and self.scan_thread.isRunning():
                    self.toggle_scan()

                if self.monitor_thread and self.monitor_thread.isRunning():
                    self.toggle_monitor()

    def closeEvent(self, event):
        """关闭窗口事件"""
        # 检查是否有正在运行的线程
        if (self.scan_thread and self.scan_thread.isRunning()) or \
           (self.monitor_thread and self.monitor_thread.isRunning()):
            msgBox = QMessageBox(QMessageBox.Question, "确认", "扫描或监控正在进行中，确定要退出吗？",
                              QMessageBox.Yes | QMessageBox.No, self)
            msgBox.button(QMessageBox.Yes).setText("是")
            msgBox.button(QMessageBox.No).setText("否")
            msgBox.setDefaultButton(QMessageBox.No)
            reply = msgBox.exec_()
            if reply == QMessageBox.No:
                event.ignore()
                return

            # 停止线程
            if self.scan_thread and self.scan_thread.isRunning():
                self.scan_thread.stop()

            if self.monitor_thread and self.monitor_thread.isRunning():
                self.monitor_thread.stop()

        # 断开连接
        current_modbus = self.get_current_modbus()
        if current_modbus.is_connected():
            current_modbus.disconnect()

        event.accept()


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = ModbusScanner()
    window.show()
    sys.exit(app.exec_())

{# Default template for the "index" page.
#}{{ warn('Now base template defindex.html is deprecated.') }}
{%- extends "layout.html" %}
{% set title = _('Overview') %}
{% block body %}
  <h1>{{ docstitle|e }}</h1>
  <p>
    {{ _('Welcome! This is') }}
    {% block description %}{{ _('the documentation for') }} {{ project|e }}
    {{ release|e }}{% if last_updated %}, {{ _('last updated') }} {{ last_updated|e }}{% endif %}{% endblock %}.
  </p>
  {% block tables %}
  <p><strong>{{ _('Indices and tables:') }}</strong></p>
  <table class="contentstable"><tr>
    <td style="width: 50%">
      <p class="biglink"><a class="biglink" href="{{ pathto("contents") }}">{{ _('Complete Table of Contents') }}</a><br>
         <span class="linkdescr">{{ _('lists all sections and subsections') }}</span></p>
      <p class="biglink"><a class="biglink" href="{{ pathto("search") }}">{{ _('Search Page') }}</a><br>
         <span class="linkdescr">{{ _('search this documentation') }}</span></p>
    </td><td style="width: 50%">
      <p class="biglink"><a class="biglink" href="{{ pathto("modindex") }}">{{ _('Global Module Index') }}</a><br>
         <span class="linkdescr">{{ _('quick access to all modules') }}</span></p>
      <p class="biglink"><a class="biglink" href="{{ pathto("genindex") }}">{{ _('General Index') }}</a><br>
         <span class="linkdescr">{{ _('all functions, classes, terms') }}</span></p>
    </td></tr>
  </table>
  {% endblock %}
{% endblock %}

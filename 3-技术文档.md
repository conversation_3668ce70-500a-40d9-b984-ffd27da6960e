# 剪切机自动化控制系统技术文档

## 目录
1. [PLC Modbus地址需求说明](#plc-modbus地址需求说明)
2. [系统架构](#系统架构)
3. [通讯协议规范](#通讯协议规范)
4. [数据格式说明](#数据格式说明)
5. [配置文件格式](#配置文件格式)
6. [API接口说明](#api接口说明)
7. [测试验证](#测试验证)

## PLC Modbus地址需求说明

### 概述

根据PLC人员提供的信息，系统需要支持以下Modbus地址配置：
- **寄存器类型**: 双字（32位）格式
- **位地址**: 前两个需要读写功能，其余只需读取功能
- **最后一个寄存器**: 只需要读取功能
- **其余寄存器**: 需要写入功能

### 详细地址映射

#### 1. 寄存器地址（32位直接读写）

| 寄存器名称 | PLC地址 | Modbus地址 | 数据类型 | 读写权限 | 说明 |
|-----------|---------|------------|----------|----------|------|
| 手动转速 | HD1000 | 42088 | 32位整数 | 读写 | 手动模式下的转速设定 |
| 自动转速 | HD1002 | 42090 | 32位整数 | 读写 | 自动模式下的转速设定 |
| 第一次相对自动定位距离 | HD1004 | 42092 | 32位整数 | 读写 | 首次定位的相对距离 |
| 每次递增的补偿距离 | HD1006 | 42094 | 32位整数 | 读写 | 每次操作的递增补偿值 |
| 自动运行的相对距离 | HD1008 | 42096 | 32位整数 | 读写 | 自动运行时的相对距离 |
| 设定数量 | HD1010 | 42098 | 32位整数 | 读写 | 目标生产数量 |
| 已完成数量 | HD1012 | 42100 | 32位整数 | **只读** | 当前已完成的数量 |

**重要说明**:
- HD0-HD6143对应Modbus地址41088-47231，因此HD1000对应42088
- **寄存器可以直接读写32位数据**，不需要拆分为两个16位寄存器
- 每个寄存器地址对应一个完整的32位值

#### 2. 位地址（Bit位）

| 位名称 | PLC地址 | Modbus地址 | 读写权限 | 说明 |
|--------|---------|------------|----------|------|
| 手自动切换 | M10 | 00010 | **读写** | 手动/自动模式切换控制 |
| 上位机复位按钮 | M11 | 00011 | **读写** | 上位机发送的复位命令 |
| 复位中 | M12 | 00012 | 只读 | 系统复位状态指示 |
| 待机中 | M13 | 00013 | 只读 | 系统待机状态指示 |
| 自动运行中 | M14 | 00014 | 只读 | 自动运行状态指示 |
| 急停中 | M15 | 00015 | 只读 | 急停状态指示 |

**注意**: PLC地址M0-M20479直接对应Modbus地址0-20479，即M10对应Modbus地址10（00010）。

## 系统架构

### 软件架构
```
剪切机自动化控制系统
├── 主程序 (main.py)
│   ├── GUI界面管理
│   ├── 参数配置
│   ├── 通讯控制
│   └── 状态监控
├── 通讯模块
│   ├── ModbusRTU类 (串口通讯)
│   ├── ModbusTCP类 (网络通讯)
│   └── 协议抽象层
├── 监控工具 (modbus_monitor.py)
│   ├── 寄存器实时监控
│   └── 数据变化记录
├── 扫描工具 (modbus_scanner.py)
│   ├── 地址自动扫描
│   └── 设备发现
├── 生产管理
│   ├── 订单管理 (production_order.py)
│   └── 任务规划 (task_planner.py)
└── 配置管理
    ├── 配置文件加载/保存
    └── 参数验证
```

### 硬件架构
```
PC端软件
    ↓ (串口/以太网)
信捷XL5E-16T PLC
    ↓ (控制信号)
剪切机设备
    ├── 伺服电机
    ├── 传感器
    └── 执行机构
```

## 通讯协议规范

### Modbus RTU通信参数
- **协议**: Modbus RTU
- **波特率**: 9600 bps（可配置）
- **数据位**: 8位
- **校验位**: 无校验(N)
- **停止位**: 1位
- **从站地址**: 1（可配置）

### Modbus TCP通信参数
- **协议**: Modbus TCP
- **端口**: 502（标准端口）
- **IP地址**: 可配置（默认*************）
- **超时**: 5秒（可配置）

### 支持的Modbus功能码
- **01**: 读线圈状态
- **03**: 读保持寄存器
- **05**: 写单个线圈
- **06**: 写单个寄存器
- **16**: 写多个寄存器

## 数据格式说明

### 32位寄存器处理
1. **直接32位操作**: 信捷XL5E-16T PLC支持直接读写32位数据
2. **自动数据处理**: 系统自动处理32位数据的分离和组合
3. **Modbus传输**: 32位数据自动拆分为两个16位寄存器进行传输
4. **PLC端接收**: PLC端接收到完整的32位值，无需手动组合

### 位操作处理
1. **权限控制**: 自动检查读写权限，防止误操作
2. **功能码选择**: 根据操作类型自动选择合适的Modbus功能码
3. **状态同步**: 实时同步位状态变化

### 数据范围
- **32位有符号整数**: -2,147,483,648 到 2,147,483,647
- **32位无符号整数**: 0 到 4,294,967,295
- **位数据**: 0（False）或 1（True）

## 配置文件格式

### 系统配置文件（config.json）
```json
{
    "protocol_type": "TCP",
    "tcp_host": "*************",
    "tcp_port": 502,
    "tcp_timeout": 5.0,
    "port": "COM1",
    "baudrate": 9600,
    "timeout": 0.5,
    "register_map": {
        "manual_speed": 42088,
        "auto_speed": 42090,
        "first_position_distance": 42092,
        "increment_compensation": 42094,
        "auto_run_distance": 42096,
        "target_quantity": 42098,
        "completed_count": 42100
    },
    "bit_map": {
        "manual_auto_switch": 10,
        "host_reset_button": 11,
        "resetting": 12,
        "standby": 13,
        "auto_running": 14,
        "emergency_stop": 15
    },
    "data_types": {
        "double_word_registers": [42088, 42090, 42092, 42094, 42096, 42098, 42100],
        "read_only_registers": [42100],
        "read_write_bits": [10, 11],
        "read_only_bits": [12, 13, 14, 15]
    }
}
```

### 配置字段说明
- `protocol_type`: 协议类型（"RTU" 或 "TCP"）
- `tcp_host`: TCP服务器IP地址
- `tcp_port`: TCP服务器端口号
- `tcp_timeout`: TCP连接超时时间（秒）
- `register_map`: 寄存器地址映射
- `bit_map`: 位地址映射
- `data_types`: 数据类型配置

## API接口说明

### ModbusRTU类接口
```python
class ModbusRTU:
    def connect(self) -> bool
    def disconnect(self) -> None
    def is_connected(self) -> bool
    def read_register(self, slave_addr: int, address: int, count: int = 1) -> list
    def write_register(self, slave_addr: int, address: int, value: int) -> bool
    def read_double_word_register(self, slave_addr: int, address: int) -> int
    def write_double_word_register(self, slave_addr: int, address: int, value: int) -> bool
    def read_coil(self, slave_addr: int, address: int, count: int = 1) -> list
    def write_coil(self, slave_addr: int, address: int, value: bool) -> bool
```

### ModbusTCP类接口
```python
class ModbusTCP:
    def __init__(self, host: str = '*************', port: int = 502, timeout: float = 5.0)
    def connect(self) -> bool
    def disconnect(self) -> None
    def is_connected(self) -> bool
    def read_register(self, slave_addr: int, address: int, count: int = 1) -> list
    def write_register(self, slave_addr: int, address: int, value: int) -> bool
    def read_double_word_register(self, slave_addr: int, address: int) -> int
    def write_double_word_register(self, slave_addr: int, address: int, value: int) -> bool
    def read_coil(self, slave_addr: int, address: int, count: int = 1) -> list
    def write_coil(self, slave_addr: int, address: int, value: bool) -> bool
```

### 智能操作接口
```python
def smart_write_register(self, slave_addr: int, register_addr: int, value: int) -> bool
def smart_read_register(self, slave_addr: int, register_addr: int) -> int
def smart_write_bit(self, slave_addr: int, bit_addr: int, value: bool) -> bool
def smart_read_bit(self, slave_addr: int, bit_addr: int) -> bool
```

## 测试验证

### 功能测试
1. **连接测试**: 验证RTU和TCP协议的连接功能
2. **读写测试**: 测试32位寄存器和位操作的读写功能
3. **权限测试**: 验证只读地址的保护机制
4. **数据范围测试**: 测试大数值（如4,000,000,000）的传输

### 性能测试
1. **响应时间**: 测试读写操作的响应时间
2. **并发性能**: 测试多个操作的并发处理能力
3. **稳定性**: 长时间运行的稳定性测试
4. **错误恢复**: 网络中断后的自动恢复能力

### 兼容性测试
1. **PLC兼容性**: 与信捷XL5E-16T PLC的兼容性
2. **操作系统兼容性**: Windows/Linux/macOS的兼容性
3. **网络环境兼容性**: 不同网络环境下的工作状态

### 测试建议
1. 使用Modbus扫描工具验证所有地址的可访问性
2. 测试32位寄存器的读写功能，确认PLC端能正确接收32位值
3. 验证位操作的读写权限控制
4. 确认只读地址的保护机制
5. 测试大数值（如4,000,000,000）的传输是否正确

### 调试工具
1. **modbus_monitor.py**: 实时监控寄存器变化
2. **modbus_scanner.py**: 自动扫描活动地址
3. **simple_tcp_test.py**: TCP功能测试脚本
4. **tcp_demo.py**: TCP功能演示脚本

## 注意事项

### 地址配置
1. **地址对应**: 请确保PLC程序中的地址与上述映射表一致
2. **32位数据**: PLC端可以直接读写32位整数，上位机会自动处理Modbus传输的拆分和组合
3. **权限设置**: 只读寄存器(HD1012)和只读位(M12-M15)不应响应写入命令

### 通信优化
1. **超时设置**: 根据网络环境调整合适的超时时间
2. **重试机制**: 实现适当的重试机制以提高可靠性
3. **错误处理**: 完善的错误处理和日志记录

### 安全考虑
1. **权限控制**: 严格控制读写权限，防止误操作
2. **数据验证**: 对输入数据进行范围和格式验证
3. **网络安全**: TCP模式下注意网络安全配置

---

**技术支持**: 如有疑问，请及时沟通确认技术规范的准确性。  
**版本**: 3.2  
**最后更新**: 2024年

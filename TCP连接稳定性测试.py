#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TCP连接稳定性测试程序
用于测试修复后的TCP连接稳定性
"""

import sys
import time
import socket
import threading
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

class TCPStabilityTest(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("TCP连接稳定性测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 测试参数
        self.test_host = "***********"
        self.test_port = 502
        self.test_timeout = 5.0
        self.test_running = False
        self.connection_count = 0
        self.success_count = 0
        self.failure_count = 0
        self.disconnect_count = 0
        
        self.init_ui()
        
        # 测试定时器
        self.test_timer = QTimer()
        self.test_timer.timeout.connect(self.run_connection_test)
        
        # 状态更新定时器
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(1000)  # 每秒更新状态
        
    def init_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title = QLabel("TCP连接稳定性测试")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 18px; font-weight: bold; padding: 10px;")
        layout.addWidget(title)
        
        # 配置区域
        config_group = QGroupBox("测试配置")
        config_layout = QGridLayout()
        
        config_layout.addWidget(QLabel("目标IP:"), 0, 0)
        self.host_edit = QLineEdit(self.test_host)
        config_layout.addWidget(self.host_edit, 0, 1)
        
        config_layout.addWidget(QLabel("端口:"), 1, 0)
        self.port_spin = QSpinBox()
        self.port_spin.setRange(1, 65535)
        self.port_spin.setValue(self.test_port)
        config_layout.addWidget(self.port_spin, 1, 1)
        
        config_layout.addWidget(QLabel("超时(秒):"), 2, 0)
        self.timeout_spin = QDoubleSpinBox()
        self.timeout_spin.setRange(0.1, 30.0)
        self.timeout_spin.setValue(self.test_timeout)
        self.timeout_spin.setSingleStep(0.5)
        config_layout.addWidget(self.timeout_spin, 2, 1)
        
        config_layout.addWidget(QLabel("测试间隔(秒):"), 3, 0)
        self.interval_spin = QSpinBox()
        self.interval_spin.setRange(1, 60)
        self.interval_spin.setValue(5)
        config_layout.addWidget(self.interval_spin, 3, 1)
        
        config_group.setLayout(config_layout)
        layout.addWidget(config_group)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        self.start_btn = QPushButton("开始测试")
        self.start_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        self.start_btn.clicked.connect(self.start_test)
        button_layout.addWidget(self.start_btn)
        
        self.stop_btn = QPushButton("停止测试")
        self.stop_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        self.stop_btn.clicked.connect(self.stop_test)
        self.stop_btn.setEnabled(False)
        button_layout.addWidget(self.stop_btn)
        
        self.clear_btn = QPushButton("清除日志")
        self.clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        self.clear_btn.clicked.connect(self.clear_log)
        button_layout.addWidget(self.clear_btn)
        
        layout.addLayout(button_layout)
        
        # 统计信息
        stats_group = QGroupBox("测试统计")
        stats_layout = QGridLayout()
        
        stats_layout.addWidget(QLabel("总连接次数:"), 0, 0)
        self.connection_count_label = QLabel("0")
        self.connection_count_label.setStyleSheet("font-weight: bold; color: #3498db;")
        stats_layout.addWidget(self.connection_count_label, 0, 1)
        
        stats_layout.addWidget(QLabel("成功次数:"), 1, 0)
        self.success_count_label = QLabel("0")
        self.success_count_label.setStyleSheet("font-weight: bold; color: #27ae60;")
        stats_layout.addWidget(self.success_count_label, 1, 1)
        
        stats_layout.addWidget(QLabel("失败次数:"), 2, 0)
        self.failure_count_label = QLabel("0")
        self.failure_count_label.setStyleSheet("font-weight: bold; color: #e74c3c;")
        stats_layout.addWidget(self.failure_count_label, 2, 1)
        
        stats_layout.addWidget(QLabel("成功率:"), 3, 0)
        self.success_rate_label = QLabel("0%")
        self.success_rate_label.setStyleSheet("font-weight: bold; color: #9b59b6;")
        stats_layout.addWidget(self.success_rate_label, 3, 1)
        
        stats_group.setLayout(stats_layout)
        layout.addWidget(stats_group)
        
        # 日志显示
        log_group = QGroupBox("测试日志")
        log_layout = QVBoxLayout()
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        self.log_text.setStyleSheet("""
            QTextEdit {
                border: 1px solid #ddd;
                border-radius: 5px;
                padding: 5px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 10px;
                background-color: #f8f9fa;
            }
        """)
        log_layout.addWidget(self.log_text)
        
        log_group.setLayout(log_layout)
        layout.addWidget(log_group)
        
    def add_log(self, message, level="INFO"):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        color_map = {
            "INFO": "black",
            "SUCCESS": "green",
            "ERROR": "red",
            "WARNING": "orange"
        }
        color = color_map.get(level, "black")
        
        log_message = f"[{timestamp}] [{level}] {message}"
        self.log_text.append(f'<span style="color: {color};">{log_message}</span>')
        
        # 自动滚动到底部
        cursor = self.log_text.textCursor()
        cursor.movePosition(cursor.End)
        self.log_text.setTextCursor(cursor)
        
        print(log_message)  # 同时输出到控制台
    
    def start_test(self):
        """开始测试"""
        self.test_host = self.host_edit.text()
        self.test_port = self.port_spin.value()
        self.test_timeout = self.timeout_spin.value()
        
        self.test_running = True
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        
        # 重置统计
        self.connection_count = 0
        self.success_count = 0
        self.failure_count = 0
        
        self.add_log(f"开始TCP连接稳定性测试: {self.test_host}:{self.test_port}", "INFO")
        
        # 启动测试定时器
        interval = self.interval_spin.value() * 1000  # 转换为毫秒
        self.test_timer.start(interval)
        
        # 立即执行一次测试
        self.run_connection_test()
    
    def stop_test(self):
        """停止测试"""
        self.test_running = False
        self.test_timer.stop()
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        
        self.add_log("测试已停止", "INFO")
    
    def clear_log(self):
        """清除日志"""
        self.log_text.clear()
        self.add_log("日志已清除", "INFO")
    
    def run_connection_test(self):
        """运行连接测试"""
        if not self.test_running:
            return
        
        self.connection_count += 1
        
        try:
            # 创建socket连接
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.test_timeout)
            
            # 设置socket选项（与修复后的代码一致）
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)
            
            start_time = time.time()
            sock.connect((self.test_host, self.test_port))
            connect_time = (time.time() - start_time) * 1000  # 转换为毫秒
            
            # 连接成功，尝试发送简单的Modbus请求
            test_request = bytearray([
                0x00, 0x01,  # 事务ID
                0x00, 0x00,  # 协议ID
                0x00, 0x06,  # 长度
                0x01,        # 单元ID
                0x03,        # 功能码
                0x00, 0x00,  # 起始地址
                0x00, 0x01   # 数量
            ])
            
            sock.send(test_request)
            response = sock.recv(1024)
            
            sock.close()
            
            self.success_count += 1
            self.add_log(f"连接测试 #{self.connection_count}: 成功 (耗时: {connect_time:.1f}ms, 响应: {len(response)}字节)", "SUCCESS")
            
        except socket.timeout:
            self.failure_count += 1
            self.add_log(f"连接测试 #{self.connection_count}: 超时 (>{self.test_timeout}秒)", "ERROR")
        except ConnectionRefusedError:
            self.failure_count += 1
            self.add_log(f"连接测试 #{self.connection_count}: 连接被拒绝", "ERROR")
        except Exception as e:
            self.failure_count += 1
            self.add_log(f"连接测试 #{self.connection_count}: 失败 ({str(e)})", "ERROR")
    
    def update_status(self):
        """更新状态显示"""
        self.connection_count_label.setText(str(self.connection_count))
        self.success_count_label.setText(str(self.success_count))
        self.failure_count_label.setText(str(self.failure_count))
        
        if self.connection_count > 0:
            success_rate = (self.success_count / self.connection_count) * 100
            self.success_rate_label.setText(f"{success_rate:.1f}%")
            
            # 根据成功率设置颜色
            if success_rate >= 95:
                self.success_rate_label.setStyleSheet("font-weight: bold; color: #27ae60;")
            elif success_rate >= 80:
                self.success_rate_label.setStyleSheet("font-weight: bold; color: #f39c12;")
            else:
                self.success_rate_label.setStyleSheet("font-weight: bold; color: #e74c3c;")
    
    def closeEvent(self, event):
        """关闭事件"""
        if self.test_running:
            self.stop_test()
        event.accept()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # 设置应用程序样式
    app.setStyle(QStyleFactory.create('Fusion'))
    
    window = TCPStabilityTest()
    window.show()
    
    sys.exit(app.exec_())

@ECHO OFF

REM Command file for Sphinx documentation

pushd %~dp0

{% if latex_engine in ('platex', 'uplatex') -%}
REM latexmkrc is read then overridden by latexmkjarc
set PDFLATEX=latexmk -r latexmkjarc -pdfdvi -dvi- -ps-
{% else -%}
set PDFLATEX=latexmk -pdf -dvi- -ps-
{% endif %}
set "LATEXOPTS= "

{% if xindy_use -%}
set XINDYOPTS={{ xindy_lang_option }} -M sphinx.xdy
{% if latex_engine == 'pdflatex' -%}
set XINDYOPTS=%XINDYOPTS% -M LICRlatin2utf8.xdy
{% if xindy_cyrillic -%}
set XINDYOPTS=%XINDYOPTS% -M LICRcyr2utf8.xdy
{% endif -%}
{% endif -%}
{% if xindy_cyrillic -%}
set XINDYOPTS=%XINDYOPTS% -M LatinRules.xdy
{% endif -%}
set XINDYOPTS=%XINDYOPTS% -I xelatex
{% endif -%}


if "%1" == "" goto all-pdf

if "%1" == "all-pdf" (
	:all-pdf
	for %%i in (*.tex) do (
		%PDFLATEX% %LATEXMKOPTS% %%i
	)
	goto end
)

if "%1" == "all-pdf-ja" (
	goto all-pdf
)

if "%1" == "clean" (
	del /q /s *.dvi *.log *.ind *.aux *.toc *.syn *.idx *.out *.ilg *.pla *.ps *.tar *.tar.gz *.tar.bz2 *.tar.xz *.fls *.fdb_latexmk
	goto end
)

:end
popd

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
TCP/IP协议功能演示脚本
展示如何使用新添加的Modbus TCP功能
"""

import sys
import os
import time

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main import ModbusTCP

def demo_basic_tcp_operations():
    """演示基本的TCP操作"""
    print("=== Modbus TCP 基本操作演示 ===\n")
    
    # 创建ModbusTCP实例
    print("1. 创建ModbusTCP实例")
    modbus_tcp = ModbusTCP(host='*************', port=502, timeout=5.0)
    print(f"   - 目标地址: {modbus_tcp.host}:{modbus_tcp.port}")
    print(f"   - 超时设置: {modbus_tcp.timeout}秒")
    print()
    
    # 尝试连接（注意：这里可能会失败，因为没有真实的设备）
    print("2. 尝试连接到Modbus TCP服务器")
    try:
        if modbus_tcp.connect():
            print("   ✓ 连接成功！")
            
            # 演示读取操作
            print("\n3. 演示读取寄存器")
            data = modbus_tcp.read_register(1, 0, 5)  # 从站1，地址0，读取5个寄存器
            if data:
                print(f"   ✓ 读取成功: {data}")
            else:
                print("   ✗ 读取失败")
            
            # 演示写入操作
            print("\n4. 演示写入寄存器")
            success = modbus_tcp.write_register(1, 0, 1234)  # 从站1，地址0，写入1234
            if success:
                print("   ✓ 写入成功")
            else:
                print("   ✗ 写入失败")
            
            # 演示32位操作
            print("\n5. 演示32位寄存器操作")
            success = modbus_tcp.write_double_word_register(1, 10, 0x12345678)
            if success:
                print("   ✓ 32位写入成功")
                value = modbus_tcp.read_double_word_register(1, 10)
                if value is not None:
                    print(f"   ✓ 32位读取成功: 0x{value:08X}")
                else:
                    print("   ✗ 32位读取失败")
            else:
                print("   ✗ 32位写入失败")
            
            # 演示线圈操作
            print("\n6. 演示线圈操作")
            success = modbus_tcp.write_coil(1, 0, True)  # 从站1，线圈0，设置为True
            if success:
                print("   ✓ 线圈写入成功")
                coil_data = modbus_tcp.read_coil(1, 0, 1)
                if coil_data:
                    print(f"   ✓ 线圈读取成功: {coil_data[0]}")
                else:
                    print("   ✗ 线圈读取失败")
            else:
                print("   ✗ 线圈写入失败")
            
            # 断开连接
            print("\n7. 断开连接")
            modbus_tcp.disconnect()
            print("   ✓ 连接已断开")
            
        else:
            print("   ✗ 连接失败（这是正常的，因为没有真实的Modbus TCP服务器）")
            print("   💡 要测试真实连接，请：")
            print("      1. 确保有支持Modbus TCP的设备在网络中")
            print("      2. 修改IP地址为实际设备地址")
            print("      3. 确认设备端口号（通常为502）")
            
    except Exception as e:
        print(f"   ✗ 连接异常: {e}")
        print("   💡 这通常是因为没有可用的Modbus TCP服务器")

def demo_tcp_header_construction():
    """演示TCP头部构建"""
    print("\n=== TCP头部构建演示 ===\n")
    
    modbus_tcp = ModbusTCP()
    
    print("1. 事务ID生成")
    for i in range(5):
        tid = modbus_tcp._get_next_transaction_id()
        print(f"   事务ID {i+1}: {tid}")
    print()
    
    print("2. TCP头部构建")
    header = modbus_tcp._build_tcp_header(6, 1)  # PDU长度6，单元ID 1
    print(f"   头部长度: {len(header)} 字节")
    print(f"   头部内容: {header.hex().upper()}")
    print("   头部解析:")
    print(f"     - 事务标识符: {(header[0] << 8) | header[1]}")
    print(f"     - 协议标识符: {(header[2] << 8) | header[3]}")
    print(f"     - 长度字段: {(header[4] << 8) | header[5]}")
    print(f"     - 单元标识符: {header[6]}")

def demo_protocol_comparison():
    """演示协议对比"""
    print("\n=== 协议对比演示 ===\n")
    
    print("Modbus RTU vs Modbus TCP 对比:")
    print()
    print("┌─────────────────┬─────────────────┬─────────────────┐")
    print("│      特性       │   Modbus RTU    │   Modbus TCP    │")
    print("├─────────────────┼─────────────────┼─────────────────┤")
    print("│   传输介质      │     串口        │    以太网       │")
    print("│   连接方式      │   点对点/总线   │     网络        │")
    print("│   传输速度      │   9600-115200   │   10/100/1000M  │")
    print("│   传输距离      │    <1200米      │     无限制      │")
    print("│   错误检测      │     CRC16       │      TCP        │")
    print("│   设备数量      │     1-247       │     无限制      │")
    print("│   实时性        │      高         │      中等       │")
    print("│   配置复杂度    │      低         │      中等       │")
    print("└─────────────────┴─────────────────┴─────────────────┘")
    print()
    
    print("使用场景建议:")
    print("• Modbus RTU: 适用于工业现场、距离较近、实时性要求高的场合")
    print("• Modbus TCP: 适用于网络环境、远程监控、多设备集成的场合")

def demo_configuration_examples():
    """演示配置示例"""
    print("\n=== 配置示例演示 ===\n")
    
    print("1. 典型的工业以太网配置:")
    print("   IP地址: *************")
    print("   端口号: 502")
    print("   超时: 5秒")
    print("   适用于: 标准工业设备")
    print()
    
    print("2. 高速局域网配置:")
    print("   IP地址: **********")
    print("   端口号: 502")
    print("   超时: 1秒")
    print("   适用于: 高速响应要求")
    print()
    
    print("3. 远程监控配置:")
    print("   IP地址: 公网IP或域名")
    print("   端口号: 自定义端口")
    print("   超时: 10秒")
    print("   适用于: 远程设备监控")
    print()
    
    print("4. 配置文件示例:")
    config_example = {
        "protocol_type": "TCP",
        "tcp_host": "*************",
        "tcp_port": 502,
        "tcp_timeout": 5.0
    }
    for key, value in config_example.items():
        print(f"   {key}: {value}")

if __name__ == "__main__":
    print("🚀 TCP/IP协议功能演示程序")
    print("=" * 50)
    
    # 基本操作演示
    demo_basic_tcp_operations()
    
    # TCP头部构建演示
    demo_tcp_header_construction()
    
    # 协议对比
    demo_protocol_comparison()
    
    # 配置示例
    demo_configuration_examples()
    
    print("\n" + "=" * 50)
    print("🎉 演示完成！")
    print("\n📖 更多信息请参考:")
    print("   - TCP_IP_Protocol_Guide.md: 详细使用指南")
    print("   - main.py: 主程序，支持协议切换")
    print("   - modbus_monitor.py: 监控工具，支持TCP协议")
    print("   - modbus_scanner.py: 扫描工具，支持TCP协议")
    print("\n💡 提示:")
    print("   要测试真实的TCP连接，请确保网络中有支持Modbus TCP的设备，")
    print("   并修改IP地址为实际设备地址。")
